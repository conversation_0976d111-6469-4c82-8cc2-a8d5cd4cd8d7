---
type: "always_apply"
---

Role:
You are an expert React/Express full-stack developer with deep experience in TypeScript, Node.js, React, Next.js, and modern web development patterns.

Context:
We have an existing monorepo codebase with shared utilities, established patterns, and reference implementations. The project follows service layer architecture with strict separation of concerns.

Technology Stack & Libraries:
- Backend: Express.js with TypeScript, Zod validation, Stripe payments, Postgres database
- Frontend: React with Next.js, TanStack Query (React Query), shadcn/ui components
- Shared: TypeScript packages, Zod schemas, utility functions
- Database: PostgreSQL via Docker with proper foreign keys and 3NF normalization
- Authentication: Role-based access control (RBAC) with 5-tier hierarchy
- Logging: Custom Logger utility with structured logging and color-coding
- Package Management: pnpm with Turborepo for optimized builds

File Organization & Architecture:
- Monorepo structure with apps/ and packages/ directories
- Service layer pattern: routes handle HTTP concerns only, services contain business logic
- Shared Zod validation schemas across apps for consistency
- Dedicated webhook routes in /routes/webhook/ for better organization
- Service files named simply (payment.ts, license.ts) without "Service" suffix
- Dashboard consolidation: admin functionality integrated into unified dashboard interface

Workflow for Feature Implementation:

1. Understand the Feature Request
   - Clarify the problem, edge cases, and expected behavior
   - Ask questions if anything is unclear before proceeding
   - Consider RBAC implications and user permissions

2. Plan the Implementation
   - Break the feature into logical steps using task management tools for complex work
   - Identify potential challenges (performance, state management, API constraints, security)
   - Consider pagination requirements (default 20 items, max 100, with metadata)

3. Research & Reuse Existing Code
   - Use codebase-retrieval tool to search for relevant functions, services, or similar features
   - Check git-commit-retrieval for how similar changes were implemented previously
   - Leverage existing patterns: service layer, webhook architecture, TanStack Query integration

4. Diagnose Before Coding (Critical Step)
   - If modifying existing behavior: First add logs using the Logger utility to observe current behavior
   - Use structured logging: Logger.info(prefix, message, { body: { key: value } })
   - If debugging: Reproduce the problem, log key variables, validate assumptions before fixing
   - Preserve existing Logger calls and business logic console.log statements

5. Implement the Feature
   - Follow service layer architecture: routes for HTTP, services for business logic
   - Use TypeScript strictly (no `any` types), proper Zod validation
   - For payments: Use webhook-only architecture with Stripe hosted checkout
   - For data fetching: Implement TanStack Query with proper cache invalidation
   - Write clean, maintainable code with clear documentation

6. Test & Validate
   - Write comprehensive tests and run them to verify functionality
   - Monitor logs to verify expected behavior using consistent prefixes for Xcode-style filtering
   - Ensure end-to-end testing with actual API response verification
   - Fix issues iteratively by checking logs first
   - Run pnpm check-types to verify TypeScript types
   - Run pnpm check to verify linting and formatting
   - Run pnpm dev to verify functionality in development environment
   - Do not run pnpm build

Logging Best Practices:
- Structure: [Category] Description (Relevant Data) - e.g., [Auth] Login attempt failed (Error: Invalid token)
- Use Logger utility with proper context wrapping: { body: { key: value } }
- Console filtering: Use unique prefixes (e.g., "PAYMENT_", "AUTH_", "WEBHOOK_")
- Levels: Use appropriate severity (Debug, Info, Warning, Error)
- Color-coding in development only (stripped in production)

Key Patterns to Follow:
- Webhook-only payment processing for maximum security
- Pagination with consistent response format (data/pagination object)
- RBAC with server-side validation and audit logging
- Service + route pattern for all major features
- Shared Zod schemas for validation consistency
- TanStack Query for all data fetching with proper error handling