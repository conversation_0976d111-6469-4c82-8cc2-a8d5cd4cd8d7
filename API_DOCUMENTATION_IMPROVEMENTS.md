# API Documentation Improvements

This document outlines specific improvements needed for the API documentation to accurately reflect the current state of the codebase.

## Critical Discrepancies Found

### Missing Endpoints
- [ ] **GET /api/licenses** - List licenses with pagination and filtering (Admin endpoint)
- [ ] **GET /api/licenses/trial/status/{licenseKey}** - Check trial license status and remaining days
- [ ] **POST /api/licenses/trial/convert** - Convert trial license to paid license
- [ ] **GET /api/licenses/trial/info** - Get trial license information by email
- [ ] **GET /api/licenses/trial/registration-allowed** - Check if trial registrations are allowed
- [ ] **GET /api/payments/checkout-session/{sessionId}** - Get checkout session status
- [ ] **GET /api/payments/payment-intent/{paymentIntentId}** - Get payment intent status
- [ ] **GET /api/users/me** - Get current user information and permissions
- [ ] **GET /api/users** - List users with pagination and filtering
- [ ] **POST /api/users** - Create a new user
- [ ] **PUT /api/users/{userId}** - Update user information
- [ ] **POST /api/users/invite** - Send user invitation
- [ ] **POST /api/users/invitations/{token}/accept** - Accept user invitation
- [ ] **POST /api/users/invitations/{invitationId}/resend** - Resend invitation
- [ ] **DELETE /api/users/invitations/{invitationId}** - Revoke invitation
- [ ] **GET /api/refunds/status/{licenseKey}** - Get refund status for license
- [ ] **GET /api/refunds/history** - Get refund history (Admin endpoint)

### Incorrect Endpoint Paths
- [ ] **Webhook endpoint path**: Documentation shows `/api/webhooks/stripe` but actual path is `/api/stripe/webhook`
- [ ] **Authentication endpoints**: Missing documentation for `/api/auth/*` endpoints (better-auth integration)

### Missing HTTP Methods
- [ ] **PUT method**: Documentation lists only GET, POST, DELETE, OPTIONS but PUT is used for device metadata updates and user updates

### Outdated Information
- [ ] **Base URL**: Shows `https://localhost:3000/api` but should be environment-configurable
- [ ] **License types**: Documentation mentions "standard" and "extended" but codebase only uses "trial" and "pro"
- [ ] **Pricing structure**: Documentation shows outdated pricing model with multiple tiers

### Missing Sections
- [ ] **Authentication System**: No documentation for better-auth integration and session management
- [ ] **User Management**: Complete section missing for RBAC system
- [ ] **Role-Based Access Control**: Missing documentation for 5-tier role hierarchy
- [ ] **User Invitations**: Missing documentation for invitation system
- [ ] **Admin Endpoints**: Missing documentation for admin-only endpoints
- [ ] **Test Endpoints**: Missing documentation for development/testing endpoints
- [ ] **Migration Endpoints**: Missing documentation for one-time migration endpoints

### Schema Inconsistencies
- [ ] **License model**: Missing fields like `refundedAt`, `refundReason`, `refundAmount`, `stripeRefundId`
- [ ] **Device model**: Missing enhanced metadata fields for better UX
- [ ] **User model**: Complete user model missing from documentation
- [ ] **Invitation model**: User invitation model missing

### Response Format Issues
- [ ] **Pagination**: Missing consistent pagination format documentation
- [ ] **Error responses**: Some endpoints have different error response formats than documented
- [ ] **Success responses**: Some endpoints return different data structures

### Security Documentation Gaps
- [ ] **Better-auth integration**: Missing authentication flow documentation
- [ ] **Session management**: Missing session handling documentation
- [ ] **Role-based permissions**: Missing detailed permission matrix
- [ ] **Audit logging**: Missing documentation for audit trail features

### Rate Limiting Updates
- [ ] **User management endpoints**: Missing rate limiting documentation
- [ ] **Admin endpoints**: Missing rate limiting information
- [ ] **Test endpoints**: Missing rate limiting (if any)

### Environment Configuration
- [ ] **Better-auth configuration**: Missing auth-related environment variables
- [ ] **Session configuration**: Missing session-related settings
- [ ] **RBAC configuration**: Missing role-based access control settings

## Organizational Improvements

### Better Structure
- [ ] **Group endpoints by feature**: License, Payment, User, Refund, Admin, Auth
- [ ] **Add endpoint index**: Quick reference table with all endpoints
- [ ] **Add authentication requirements**: Clear indication of which endpoints require auth
- [ ] **Add role requirements**: Clear indication of required roles for each endpoint

### Enhanced Examples
- [ ] **Complete request/response examples**: Add examples for all endpoints
- [ ] **Error handling examples**: Show common error scenarios
- [ ] **Authentication examples**: Show how to authenticate requests
- [ ] **Pagination examples**: Show how to handle paginated responses

### Additional Sections Needed
- [ ] **Getting Started Guide**: Quick start for developers
- [ ] **Authentication Guide**: Detailed auth implementation guide
- [ ] **Error Handling Guide**: Comprehensive error handling documentation
- [ ] **Rate Limiting Guide**: Detailed rate limiting explanation
- [ ] **Webhook Integration Guide**: Step-by-step webhook setup
- [ ] **Testing Guide**: How to test API endpoints
- [ ] **Migration Guide**: How to handle API changes

## Priority Levels

### Critical (Fix Immediately)
- Missing User Management section
- Incorrect webhook endpoint path
- Missing authentication documentation
- Outdated license types and pricing

### High (Fix Soon)
- Missing admin endpoints
- Missing payment status endpoints
- Schema inconsistencies
- Missing role-based access control

### Medium (Fix When Possible)
- Enhanced examples
- Better organization
- Additional guides
- Test endpoint documentation

### Low (Nice to Have)
- Migration endpoint documentation
- Development/testing information
- Advanced configuration options

## Detailed Endpoint Documentation Needed

### User Management Endpoints (Complete Section Missing)

#### Authentication & Session Management
- [ ] **POST /api/auth/sign-in** - User sign in
- [ ] **POST /api/auth/sign-up** - User registration
- [ ] **POST /api/auth/sign-out** - User sign out
- [ ] **GET /api/auth/session** - Get current session
- [ ] **POST /api/auth/forgot-password** - Password reset request
- [ ] **POST /api/auth/reset-password** - Reset password with token

#### User CRUD Operations
- [ ] **GET /api/users/me** - Get current user profile and permissions
- [ ] **GET /api/users** - List users (MANAGER+ role required)
- [ ] **POST /api/users** - Create user (ADMIN+ role required)
- [ ] **PUT /api/users/{userId}** - Update user (ADMIN+ role required)
- [ ] **DELETE /api/users/{userId}** - Deactivate user (ADMIN+ role required)

#### User Invitation System
- [ ] **POST /api/users/invite** - Send invitation (MANAGER+ role required)
- [ ] **GET /api/users/invitations** - List pending invitations
- [ ] **POST /api/users/invitations/{token}/accept** - Accept invitation
- [ ] **POST /api/users/invitations/{invitationId}/resend** - Resend invitation
- [ ] **DELETE /api/users/invitations/{invitationId}** - Revoke invitation

### Enhanced License Management Endpoints

#### Administrative License Management
- [ ] **GET /api/licenses** - List all licenses with advanced filtering (ADMIN+ required)
  - Supports pagination, search, license type filtering, status filtering
  - Returns comprehensive license data including device counts and refund status

#### Trial License Extensions
- [ ] **GET /api/licenses/trial/status/{licenseKey}** - Get detailed trial status
  - Returns trial expiration info, days remaining, conversion eligibility
- [ ] **POST /api/licenses/trial/convert** - Convert trial to paid license
  - Handles trial-to-paid conversion with payment verification
- [ ] **GET /api/licenses/trial/info** - Get trial info by email
  - Returns existing trial license information for email
- [ ] **GET /api/licenses/trial/registration-allowed** - Check trial availability
  - Returns whether new trial registrations are currently enabled

### Payment Status Endpoints (Missing from Documentation)

#### Payment Tracking
- [ ] **GET /api/payments/checkout-session/{sessionId}** - Get checkout session details
  - Returns session status, payment status, associated license information
- [ ] **GET /api/payments/payment-intent/{paymentIntentId}** - Get payment intent details
  - Returns payment status, amount, currency, associated license information

### Refund Management Extensions

#### Refund Status Tracking
- [ ] **GET /api/refunds/status/{licenseKey}** - Get refund status for specific license
  - Returns refund history, current status, refund amounts
- [ ] **GET /api/refunds/history** - Get paginated refund history (ADMIN+ required)
  - Supports filtering by status, date range, pagination

## Schema Documentation Updates Needed

### User Model (Missing Entirely)
```typescript
interface User {
  id: string;
  email: string;
  name?: string;
  role: "VIEWER" | "USER" | "MANAGER" | "ADMIN" | "SUPER_ADMIN";
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  invitedBy?: string;
  invitedAt?: Date;
}
```

### UserInvitation Model (Missing Entirely)
```typescript
interface UserInvitation {
  id: string;
  email: string;
  role: UserRole;
  token: string;
  expiresAt: Date;
  acceptedAt?: Date;
  revokedAt?: Date;
  invitedBy: string;
  createdAt: Date;
}
```

### Enhanced License Model Updates
- [ ] Add missing refund-related fields
- [ ] Add audit trail fields
- [ ] Add device expansion history
- [ ] Add conversion tracking for trial licenses

### Enhanced Device Model Updates
- [ ] Add comprehensive metadata fields for better UX
- [ ] Add device fingerprinting fields
- [ ] Add security-related tracking fields

## Authentication & Authorization Documentation

### Role-Based Access Control (RBAC)
- [ ] **5-Tier Role Hierarchy**: VIEWER < USER < MANAGER < ADMIN < SUPER_ADMIN
- [ ] **Permission Matrix**: Detailed permissions for each role
- [ ] **Endpoint Access Control**: Which roles can access which endpoints
- [ ] **Hierarchical Permissions**: Higher roles inherit lower role permissions

### Session Management
- [ ] **Session Duration**: 7-day expiration with 1-day refresh
- [ ] **Cookie Configuration**: HttpOnly, Secure, SameSite settings
- [ ] **Session Refresh**: Automatic renewal on activity
- [ ] **Session Invalidation**: Sign-out and security events

## Rate Limiting Updates

### Missing Rate Limits
- [ ] **User Management**: Document rate limits for user CRUD operations
- [ ] **Authentication**: Document rate limits for auth endpoints
- [ ] **Admin Operations**: Document rate limits for admin-only endpoints
- [ ] **Invitation System**: Document rate limits for invitation operations

## Error Handling Improvements

### Standardized Error Responses
- [ ] **Consistent Format**: Ensure all endpoints use same error response structure
- [ ] **Error Codes**: Complete list of all possible error codes
- [ ] **HTTP Status Codes**: Correct mapping of business errors to HTTP status codes
- [ ] **Validation Errors**: Detailed field-level validation error responses

## Security Documentation Enhancements

### Authentication Security
- [ ] **Password Requirements**: Document password complexity rules
- [ ] **Account Lockout**: Document failed login attempt handling
- [ ] **Session Security**: Document session hijacking prevention
- [ ] **CSRF Protection**: Document cross-site request forgery prevention

### API Security
- [ ] **Input Validation**: Document all input validation rules
- [ ] **SQL Injection Prevention**: Document database security measures
- [ ] **Rate Limiting Security**: Document abuse prevention measures
- [ ] **Audit Logging**: Document security event logging
