import { z } from "zod";

// ============================================================================
// ENUMS
// ============================================================================

export const UserRole = z.enum([
	"SUPER_ADMIN",
	"ADMIN",
	"MANAGER",
	"USER",
	"VIEWER",
]);
export type UserRole = z.infer<typeof UserRole>;

export const InvitationStatus = z.enum([
	"PENDING",
	"ACCEPTED",
	"EXPIRED",
	"REVOKED",
]);
export type InvitationStatus = z.infer<typeof InvitationStatus>;

export const LicenseType = z.enum(["TRIAL", "PRO", "ENTERPRISE"]);
export type LicenseType = z.infer<typeof LicenseType>;

export const LicenseStatus = z.enum([
	"ACTIVE",
	"EXPIRED",
	"SUSPENDED",
	"REFUNDED",
	"CANCELLED",
]);
export type LicenseStatus = z.infer<typeof LicenseStatus>;

export const DeviceStatus = z.enum(["ACTIVE", "INACTIVE", "REMOVED"]);
export type DeviceStatus = z.infer<typeof DeviceStatus>;

export const PaymentStatus = z.enum([
	"PENDING",
	"PROCESSING",
	"SUCCEEDED",
	"FAILED",
	"CANCELLED",
	"REFUNDED",
]);
export type PaymentStatus = z.infer<typeof PaymentStatus>;

export const PaymentType = z.enum([
	"LICENSE_PURCHASE",
	"DEVICE_EXPANSION",
	"SUBSCRIPTION",
]);
export type PaymentType = z.infer<typeof PaymentType>;

export const DeviceExpansionStatus = z.enum(["PENDING", "PROCESSED", "FAILED"]);
export type DeviceExpansionStatus = z.infer<typeof DeviceExpansionStatus>;

export const RefundStatus = z.enum([
	"PENDING",
	"APPROVED",
	"REJECTED",
	"PROCESSED",
	"FAILED",
]);
export type RefundStatus = z.infer<typeof RefundStatus>;

export const AuditAction = z.enum([
	// License actions
	"LICENSE_CREATED",
	"TRIAL_LICENSE_CREATED",
	"LICENSE_ACTIVATED",
	"LICENSE_VALIDATED",
	"LICENSE_EXPIRED",
	"LICENSE_SUSPENDED",
	"LICENSE_REACTIVATED",

	// Device actions
	"DEVICE_REGISTERED",
	"DEVICE_UPDATED",
	"DEVICE_REMOVED",
	"DEVICE_EXPANSION_PURCHASED",
	"DEVICE_EXPANSION_PROCESSED",

	// Payment actions
	"PAYMENT_INITIATED",
	"PAYMENT_SUCCEEDED",
	"PAYMENT_FAILED",
	"PAYMENT_REFUNDED",

	// Refund actions
	"REFUND_REQUESTED",
	"REFUND_APPROVED",
	"REFUND_REJECTED",
	"REFUND_PROCESSED",
	"REFUND_FAILED",

	// User management actions
	"USER_CREATED",
	"USER_UPDATED",
	"USER_ROLE_CHANGED",
	"USER_ACTIVATED",
	"USER_DEACTIVATED",
	"USER_SUSPENDED",
	"USER_DELETED",
	"USER_LOGIN",
	"USER_LOGOUT",
	"USER_EMAIL_VERIFIED",

	// Invitation actions
	"INVITATION_SENT",
	"INVITATION_ACCEPTED",
	"INVITATION_EXPIRED",
	"INVITATION_REVOKED",
	"INVITATION_RESENT",
	"USER_CREATED_FROM_INVITATION",

	// Security actions
	"SUSPICIOUS_ACTIVITY_DETECTED",
	"RATE_LIMIT_EXCEEDED",
	"UNAUTHORIZED_ACCESS_ATTEMPT",
	"MULTIPLE_DEVICE_LIMIT_EXCEEDED",

	// System actions
	"WEBHOOK_PROCESSED",
	"WEBHOOK_FAILED",
	"DATA_EXPORT_REQUESTED",
	"DATA_DELETED",
]);
export type AuditAction = z.infer<typeof AuditAction>;

// ============================================================================
// USER MANAGEMENT SCHEMAS
// ============================================================================

export const UserSchema = z.object({
	id: z.string().cuid(),
	name: z.string().min(1, "Name is required"),
	email: z.string().email("Invalid email format"),
	emailVerified: z.boolean().default(false),
	image: z.string().url("Invalid image URL").nullable().optional(),
	role: UserRole.default("USER"),
	isActive: z.boolean().default(true),
	invitedBy: z.string().cuid().nullable().optional(),
	invitedAt: z.date().nullable().optional(),
	lastLoginAt: z.date().nullable().optional(),
	createdAt: z.date(),
	updatedAt: z.date(),
});
export type User = z.infer<typeof UserSchema>;

export const CreateUserSchema = UserSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
	lastLoginAt: true,
});
export type CreateUser = z.infer<typeof CreateUserSchema>;

export const UpdateUserSchema = UserSchema.partial().omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});
export type UpdateUser = z.infer<typeof UpdateUserSchema>;

export const UserInvitationSchema = z.object({
	id: z.string().cuid(),
	email: z.string().email("Invalid email format"),
	role: UserRole,
	token: z.string().min(1),
	status: InvitationStatus.default("PENDING"),
	expiresAt: z.date(),
	sentAt: z.date(),
	acceptedAt: z.date().nullable().optional(),
	sentBy: z.string().cuid(),
	acceptedBy: z.string().cuid().nullable().optional(),
});
export type UserInvitation = z.infer<typeof UserInvitationSchema>;

export const CreateUserInvitationSchema = z.object({
	email: z.string().email("Invalid email format"),
	role: UserRole,
	expiresAt: z.date().optional(), // Will default to 7 days if not provided
});
export type CreateUserInvitation = z.infer<typeof CreateUserInvitationSchema>;

// ============================================================================
// PAYMENT SCHEMAS
// ============================================================================

export const PaymentIntentSchema = z.object({
	id: z.string().cuid(),
	stripePaymentIntentId: z.string().min(1),
	stripeCheckoutSessionId: z.string().min(1).nullable().optional(),
	amount: z.number().int().positive("Amount must be positive"),
	currency: z.string().min(3).max(3).default("usd"),
	status: PaymentStatus.default("PENDING"),
	paymentType: PaymentType,
	customerEmail: z.string().email("Invalid customer email"),
	customerName: z.string().min(1).nullable().optional(),
	createdAt: z.date(),
	updatedAt: z.date(),
	processedAt: z.date().nullable().optional(),
});
export type PaymentIntent = z.infer<typeof PaymentIntentSchema>;

export const CreatePaymentIntentSchema = PaymentIntentSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
	processedAt: true,
});
export type CreatePaymentIntent = z.infer<typeof CreatePaymentIntentSchema>;

export const WebhookEventSchema = z.object({
	id: z.string().cuid(),
	stripeEventId: z.string().min(1),
	eventType: z.string().min(1),
	processed: z.boolean().default(false),
	processedAt: z.date().nullable().optional(),
	errorMessage: z.string().nullable().optional(),
	retryCount: z.number().int().min(0).default(0),
	createdAt: z.date(),
	paymentIntentId: z.string().cuid().nullable().optional(),
});
export type WebhookEvent = z.infer<typeof WebhookEventSchema>;

// ============================================================================
// LICENSE SCHEMAS
// ============================================================================

export const LicenseSchema = z.object({
	id: z.string().cuid(),
	licenseKey: z.string().min(1),
	licenseType: LicenseType,
	status: LicenseStatus.default("ACTIVE"),
	maxDevices: z.number().int().positive().default(2),
	usedDevices: z.number().int().min(0).default(0),
	createdAt: z.date(),
	updatedAt: z.date(),
	expiresAt: z.date().nullable().optional(),
	activatedAt: z.date().nullable().optional(),
	customerEmail: z.string().email("Invalid customer email"),
	customerName: z.string().min(1).nullable().optional(),
	createdBy: z.string().cuid().nullable().optional(),
	paymentIntentId: z.string().cuid().nullable().optional(),
	totalPaidAmount: z.number().int().min(0).default(0),
	refundedAt: z.date().nullable().optional(),
	refundReason: z.string().nullable().optional(),
	refundAmount: z.number().int().min(0).nullable().optional(),
	emailSentAt: z.date().nullable().optional(),
	emailDeliveryStatus: z.string().nullable().optional(),
});
export type License = z.infer<typeof LicenseSchema>;

export const CreateLicenseSchema = LicenseSchema.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
	usedDevices: true,
	activatedAt: true,
	totalPaidAmount: true,
	refundedAt: true,
	refundReason: true,
	refundAmount: true,
	emailSentAt: true,
	emailDeliveryStatus: true,
});
export type CreateLicense = z.infer<typeof CreateLicenseSchema>;

export const UpdateLicenseSchema = LicenseSchema.partial().omit({
	id: true,
	licenseKey: true,
	createdAt: true,
	updatedAt: true,
});
export type UpdateLicense = z.infer<typeof UpdateLicenseSchema>;

// License validation for app
export const ValidateLicenseSchema = z.object({
	licenseKey: z.string().min(1, "License key is required"),
	deviceInfo: z
		.object({
			deviceName: z.string().optional(),
			deviceType: z.string().optional(),
			deviceModel: z.string().optional(),
			operatingSystem: z.string().optional(),
			architecture: z.string().optional(),
			screenResolution: z.string().optional(),
			totalMemory: z.string().optional(),
			appVersion: z.string().optional(),
		})
		.optional(),
});
export type ValidateLicense = z.infer<typeof ValidateLicenseSchema>;

// ============================================================================
// DEVICE SCHEMAS
// ============================================================================

export const DeviceSchema = z.object({
	id: z.string().cuid(),
	licenseId: z.string().cuid(),
	deviceHash: z.string().min(1),
	salt: z.string().min(1),
	status: DeviceStatus.default("ACTIVE"),
	firstSeen: z.date(),
	lastSeen: z.date(),
	removedAt: z.date().nullable().optional(),
	appVersion: z.string().nullable().optional(),
	deviceName: z.string().nullable().optional(),
	deviceType: z.string().nullable().optional(),
	deviceModel: z.string().nullable().optional(),
	operatingSystem: z.string().nullable().optional(),
	architecture: z.string().nullable().optional(),
	screenResolution: z.string().nullable().optional(),
	totalMemory: z.string().nullable().optional(),
	userNickname: z.string().nullable().optional(),
	location: z.string().nullable().optional(),
	notes: z.string().nullable().optional(),
});
export type Device = z.infer<typeof DeviceSchema>;

export const RegisterDeviceSchema = z.object({
	licenseKey: z.string().min(1, "License key is required"),
	deviceInfo: z.object({
		deviceName: z.string().optional(),
		deviceType: z.string().optional(),
		deviceModel: z.string().optional(),
		operatingSystem: z.string().optional(),
		architecture: z.string().optional(),
		screenResolution: z.string().optional(),
		totalMemory: z.string().optional(),
		userNickname: z.string().optional(),
		location: z.string().optional(),
		notes: z.string().optional(),
		appVersion: z.string().optional(),
	}),
});
export type RegisterDevice = z.infer<typeof RegisterDeviceSchema>;

export const UpdateDeviceSchema = z.object({
	userNickname: z.string().nullable().optional(),
	location: z.string().nullable().optional(),
	notes: z.string().nullable().optional(),
	appVersion: z.string().optional(),
});
export type UpdateDevice = z.infer<typeof UpdateDeviceSchema>;

// ============================================================================
// DEVICE EXPANSION SCHEMAS
// ============================================================================

export const DeviceExpansionSchema = z.object({
	id: z.string().cuid(),
	licenseId: z.string().cuid(),
	paymentIntentId: z.string().cuid(),
	additionalDevices: z.number().int().positive("Must add at least 1 device"),
	amount: z.number().int().positive("Amount must be positive"),
	status: DeviceExpansionStatus.default("PENDING"),
	createdAt: z.date(),
	processedAt: z.date().nullable().optional(),
});
export type DeviceExpansion = z.infer<typeof DeviceExpansionSchema>;

export const CreateDeviceExpansionSchema = z.object({
	licenseKey: z.string().min(1, "License key is required"),
	additionalDevices: z.number().int().positive("Must add at least 1 device"),
	customerEmail: z.string().email("Invalid email format"),
});
export type CreateDeviceExpansion = z.infer<typeof CreateDeviceExpansionSchema>;

// ============================================================================
// REFUND SCHEMAS
// ============================================================================

export const RefundRequestSchema = z.object({
	id: z.string().cuid(),
	licenseId: z.string().cuid(),
	requestedBy: z.string().email("Invalid email format"),
	reason: z
		.string()
		.min(10, "Please provide a detailed reason (minimum 10 characters)"),
	status: RefundStatus.default("PENDING"),
	requestedAmount: z.number().int().positive().nullable().optional(),
	approvedAmount: z.number().int().positive().nullable().optional(),
	stripeRefundIds: z.array(z.string()).default([]),
	adminNotes: z.string().nullable().optional(),
	processedBy: z.string().cuid().nullable().optional(),
	createdAt: z.date(),
	updatedAt: z.date(),
	processedAt: z.date().nullable().optional(),
});
export type RefundRequest = z.infer<typeof RefundRequestSchema>;

export const CreateRefundRequestSchema = z.object({
	licenseKey: z.string().min(1, "License key is required"),
	customerEmail: z.string().email("Invalid email format"),
	reason: z
		.string()
		.min(10, "Please provide a detailed reason (minimum 10 characters)"),
	requestedAmount: z.number().int().positive().optional(),
});
export type CreateRefundRequest = z.infer<typeof CreateRefundRequestSchema>;

export const ProcessRefundSchema = z.object({
	status: z.enum(["APPROVED", "REJECTED"]),
	adminNotes: z.string().optional(),
	approvedAmount: z.number().int().positive().optional(),
});
export type ProcessRefund = z.infer<typeof ProcessRefundSchema>;

// ============================================================================
// AUDIT LOG SCHEMAS
// ============================================================================

export const AuditLogSchema = z.object({
	id: z.string().cuid(),
	action: AuditAction,
	licenseKey: z.string().nullable().optional(),
	deviceHash: z.string().nullable().optional(),
	licenseId: z.string().cuid().nullable().optional(),
	deviceId: z.string().cuid().nullable().optional(),
	actorId: z.string().cuid().nullable().optional(),
	actorEmail: z.string().email().nullable().optional(),
	targetId: z.string().cuid().nullable().optional(),
	customerEmail: z.string().email().nullable().optional(),
	ipAddress: z.string().nullable().optional(),
	userAgent: z.string().nullable().optional(),
	details: z.record(z.any()).nullable().optional(),
	createdAt: z.date(),
});
export type AuditLog = z.infer<typeof AuditLogSchema>;

export const CreateAuditLogSchema = AuditLogSchema.omit({
	id: true,
	createdAt: true,
});
export type CreateAuditLog = z.infer<typeof CreateAuditLogSchema>;

// ============================================================================
// API RESPONSE SCHEMAS
// ============================================================================

export const ApiSuccessSchema = z.object({
	success: z.literal(true),
	data: z.any(),
	message: z.string().optional(),
});
export type ApiSuccess<T = any> = {
	success: true;
	data: T;
	message?: string;
};

export const ApiErrorSchema = z.object({
	success: z.literal(false),
	error: z.object({
		code: z.string(),
		message: z.string(),
		details: z.record(z.any()).optional(),
	}),
});
export type ApiError = {
	success: false;
	error: {
		code: string;
		message: string;
		details?: Record<string, any>;
	};
};

export type ApiResponse<T = any> = ApiSuccess<T> | ApiError;

// ============================================================================
// PAGINATION SCHEMAS
// ============================================================================

export const PaginationSchema = z.object({
	page: z.number().int().min(1).default(1),
	limit: z.number().int().min(1).max(100).default(20),
	sortBy: z.string().optional(),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});
export type Pagination = z.infer<typeof PaginationSchema>;

export const PaginatedResponseSchema = z.object({
	data: z.array(z.any()),
	pagination: z.object({
		page: z.number().int(),
		limit: z.number().int(),
		total: z.number().int(),
		pages: z.number().int(),
		hasNext: z.boolean(),
		hasPrev: z.boolean(),
	}),
});
export type PaginatedResponse<T = any> = {
	data: T[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		pages: number;
		hasNext: boolean;
		hasPrev: boolean;
	};
};

// ============================================================================
// FILTER SCHEMAS
// ============================================================================

export const LicenseFiltersSchema = z.object({
	status: LicenseStatus.optional(),
	type: LicenseType.optional(),
	customerEmail: z.string().email().optional(),
	createdBy: z.string().cuid().optional(),
	createdAfter: z.date().optional(),
	createdBefore: z.date().optional(),
	expiresAfter: z.date().optional(),
	expiresBefore: z.date().optional(),
});
export type LicenseFilters = z.infer<typeof LicenseFiltersSchema>;

export const DeviceFiltersSchema = z.object({
	status: DeviceStatus.optional(),
	licenseId: z.string().cuid().optional(),
	lastSeenAfter: z.date().optional(),
	lastSeenBefore: z.date().optional(),
	appVersion: z.string().optional(),
});
export type DeviceFilters = z.infer<typeof DeviceFiltersSchema>;

export const RefundFiltersSchema = z.object({
	status: RefundStatus.optional(),
	requestedBy: z.string().email().optional(),
	processedBy: z.string().cuid().optional(),
	createdAfter: z.date().optional(),
	createdBefore: z.date().optional(),
});
export type RefundFilters = z.infer<typeof RefundFiltersSchema>;

export const AuditLogFiltersSchema = z.object({
	action: AuditAction.optional(),
	actorId: z.string().cuid().optional(),
	customerEmail: z.string().email().optional(),
	licenseId: z.string().cuid().optional(),
	ipAddress: z.string().optional(),
	createdAfter: z.date().optional(),
	createdBefore: z.date().optional(),
});
export type AuditLogFilters = z.infer<typeof AuditLogFiltersSchema>;

// ============================================================================
// DASHBOARD SCHEMAS
// ============================================================================

export const DashboardStatsSchema = z.object({
	totalLicenses: z.number().int(),
	activeLicenses: z.number().int(),
	totalDevices: z.number().int(),
	totalRevenue: z.number(),
	pendingRefunds: z.number().int(),
	trialLicenses: z.number().int(),
	proLicenses: z.number().int(),
	recentActivity: z.array(AuditLogSchema).max(10),
});
export type DashboardStats = z.infer<typeof DashboardStatsSchema>;

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export const parseApiResponse = <T>(response: unknown): ApiResponse<T> => {
	const successResult = ApiSuccessSchema.safeParse(response);
	if (successResult.success) {
		return successResult.data as ApiSuccess<T>;
	}

	const errorResult = ApiErrorSchema.safeParse(response);
	if (errorResult.success) {
		return errorResult.data;
	}

	// Fallback error
	return {
		success: false,
		error: {
			code: "PARSE_ERROR",
			message: "Invalid API response format",
		},
	};
};

export const createApiSuccess = <T>(
	data: T,
	message?: string,
): ApiSuccess<T> => ({
	success: true,
	data,
	message,
});

export const createApiError = (
	code: string,
	message: string,
	details?: Record<string, any>,
): ApiError => ({
	success: false,
	error: { code, message, details },
});
