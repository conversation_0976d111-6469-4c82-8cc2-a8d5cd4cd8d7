we need to redesign this server


what is the purpose of this server?
is to manage licenses and payments for the snapback app


flow the user follows to get a license?

1. user goes to the website
2. user chooses "download for free" and clicks "continue"
   2.1 user downloads the app and installs it
   2.2 user opens the app and starts using it
   
3. user clicks on either "download for free" or "buy pro"
  3.1 user chooses "buy pro" and clicks "continue to checkout"
  3.2 user is taken to stripe to complete payment
  3.3 user is taken back to the website and shown a success page
  3.4 user clicks "download" and installs the app
  3.5 user opens the app and enters their license key
  3.6 user is shown a success page and can start using the app




* A pro license can be used on 2 devices
* If user already has a pro license, they can buy another one, purchasing a new license using the same email address will add devices to the existing license otherwise it will create a new one
* When user enters their license key, the app does 2 things:
  * validates the license key
  * registers the device
* App constantly pings the server to check if the license is valid and to update the device information
* User can remove devices from the app, which will remove them from the server
* User has 30 days to request a refund


* Licese creation should only happen via webhook
* Refunds should only happen via webhook
* User's can request a refund. this should be reviewed by an admin and then processed.
      

* Currently we don't support trial license but we may support it in the future, so db should be designed with that in mind
* Currently theres only one type of user(owner) but we may support multiple users in the future, so db should be designed with that in mind
* If we do ever support multiple user types, they should be only able to access once invited by the owner or an admin


  


