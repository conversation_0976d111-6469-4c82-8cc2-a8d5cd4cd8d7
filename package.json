{"name": "snapbackapp-site", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "prepare": "husky", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "test": "turbo test", "test:run": "turbo test:run", "test:ui": "turbo test:ui", "test:coverage": "turbo test:coverage", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "db:push": "turbo -F server db:push", "db:studio": "turbo -F server db:studio", "db:generate": "turbo -F server db:generate", "db:migrate": "turbo -F server db:migrate", "db:seed": "turbo -F server db:seed", "db:start": "turbo -F server db:start", "db:watch": "turbo -F server db:watch", "db:stop": "turbo -F server db:stop", "db:reset": "turbo -F server db:reset", "db:down": "turbo -F server db:down", "stripe:listen": "turbo -F server stripe:listen"}, "devDependencies": {"@biomejs/biome": "^2.1.2", "husky": "^9.1.7", "lint-staged": "^16.1.2", "turbo": "^2.5.6"}, "lint-staged": {"*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}": ["biome check --write ."]}, "packageManager": "pnpm@9.15.2"}