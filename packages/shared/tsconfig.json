{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "noEmit": true, "declaration": true, "declarationMap": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": true, "baseUrl": ".", "paths": {"@snapback/shared": ["packages/shared/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}