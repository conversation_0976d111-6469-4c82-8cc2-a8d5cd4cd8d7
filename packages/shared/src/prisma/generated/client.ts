
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 *
 * 🟢 You can import this file directly.
 */

import * as process from 'node:process'
import * as path from 'node:path'
import { fileURLToPath } from 'node:url'
const __dirname = path.dirname(fileURLToPath(import.meta.url))

import * as runtime from "@prisma/client/runtime/library"
import * as $Enums from "./enums"
import * as $Class from "./internal/class"
import * as Prisma from "./internal/prismaNamespace"

export * as $Enums from './enums'
/**
 * ## Prisma Client
 * 
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export const PrismaClient = $Class.getPrismaClientClass(__dirname)
export type PrismaClient<ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions, Log = $Class.LogOptions<ClientOptions>, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = $Class.PrismaClient<ClientOptions, Log, ExtArgs>
export { Prisma }


// file annotations for bundling tools to include these files
path.join(__dirname, "libquery_engine-darwin-arm64.dylib.node")
path.join(process.cwd(), "prisma/generated/libquery_engine-darwin-arm64.dylib.node")

/**
 * Model User
 * 
 */
export type User = Prisma.UserModel
/**
 * Model Session
 * 
 */
export type Session = Prisma.SessionModel
/**
 * Model Account
 * 
 */
export type Account = Prisma.AccountModel
/**
 * Model Verification
 * 
 */
export type Verification = Prisma.VerificationModel
/**
 * Model UserInvitation
 * 
 */
export type UserInvitation = Prisma.UserInvitationModel
/**
 * Model PaymentIntent
 * 
 */
export type PaymentIntent = Prisma.PaymentIntentModel
/**
 * Model WebhookEvent
 * 
 */
export type WebhookEvent = Prisma.WebhookEventModel
/**
 * Model License
 * 
 */
export type License = Prisma.LicenseModel
/**
 * Model Device
 * 
 */
export type Device = Prisma.DeviceModel
/**
 * Model DeviceExpansion
 * 
 */
export type DeviceExpansion = Prisma.DeviceExpansionModel
/**
 * Model RefundRequest
 * 
 */
export type RefundRequest = Prisma.RefundRequestModel
/**
 * Model AuditLog
 * 
 */
export type AuditLog = Prisma.AuditLogModel
/**
 * Model RateLimit
 * 
 */
export type RateLimit = Prisma.RateLimitModel
/**
 * Model SupportTicket
 * 
 */
export type SupportTicket = Prisma.SupportTicketModel
/**
 * Model SupportMessage
 * 
 */
export type SupportMessage = Prisma.SupportMessageModel

export type UserRole = $Enums.UserRole
export const UserRole = $Enums.UserRole

export type InvitationStatus = $Enums.InvitationStatus
export const InvitationStatus = $Enums.InvitationStatus

export type LicenseType = $Enums.LicenseType
export const LicenseType = $Enums.LicenseType

export type LicenseStatus = $Enums.LicenseStatus
export const LicenseStatus = $Enums.LicenseStatus

export type DeviceStatus = $Enums.DeviceStatus
export const DeviceStatus = $Enums.DeviceStatus

export type PaymentStatus = $Enums.PaymentStatus
export const PaymentStatus = $Enums.PaymentStatus

export type PaymentType = $Enums.PaymentType
export const PaymentType = $Enums.PaymentType

export type DeviceExpansionStatus = $Enums.DeviceExpansionStatus
export const DeviceExpansionStatus = $Enums.DeviceExpansionStatus

export type RefundStatus = $Enums.RefundStatus
export const RefundStatus = $Enums.RefundStatus

export type AuditAction = $Enums.AuditAction
export const AuditAction = $Enums.AuditAction

export type TicketStatus = $Enums.TicketStatus
export const TicketStatus = $Enums.TicketStatus

export type TicketCategory = $Enums.TicketCategory
export const TicketCategory = $Enums.TicketCategory

export type TicketPriority = $Enums.TicketPriority
export const TicketPriority = $Enums.TicketPriority
