{"version": "1.0", "generatorVersion": "unknown", "generatedAt": "2025-09-08T22:06:52.071Z", "outputPath": "/Users/<USER>/Projects/snapbackapp-site/apps/server/prisma/generated/zod", "files": ["schemas/enums/TransactionIsolationLevel.schema.ts", "schemas/enums/UserScalarFieldEnum.schema.ts", "schemas/enums/SessionScalarFieldEnum.schema.ts", "schemas/enums/AccountScalarFieldEnum.schema.ts", "schemas/enums/VerificationScalarFieldEnum.schema.ts", "schemas/enums/UserInvitationScalarFieldEnum.schema.ts", "schemas/enums/PaymentIntentScalarFieldEnum.schema.ts", "schemas/enums/WebhookEventScalarFieldEnum.schema.ts", "schemas/enums/LicenseScalarFieldEnum.schema.ts", "schemas/enums/DeviceScalarFieldEnum.schema.ts", "schemas/enums/DeviceExpansionScalarFieldEnum.schema.ts", "schemas/enums/RefundRequestScalarFieldEnum.schema.ts", "schemas/enums/AuditLogScalarFieldEnum.schema.ts", "schemas/enums/RateLimitScalarFieldEnum.schema.ts", "schemas/enums/SortOrder.schema.ts", "schemas/enums/NullableJsonNullValueInput.schema.ts", "schemas/enums/QueryMode.schema.ts", "schemas/enums/NullsOrder.schema.ts", "schemas/enums/JsonNullValueFilter.schema.ts", "schemas/enums/UserRole.schema.ts", "schemas/enums/InvitationStatus.schema.ts", "schemas/enums/PaymentStatus.schema.ts", "schemas/enums/PaymentType.schema.ts", "schemas/enums/LicenseType.schema.ts", "schemas/enums/LicenseStatus.schema.ts", "schemas/enums/DeviceStatus.schema.ts", "schemas/enums/DeviceExpansionStatus.schema.ts", "schemas/enums/RefundStatus.schema.ts", "schemas/enums/AuditAction.schema.ts", "schemas/objects/UserWhereInput.schema.ts", "schemas/objects/UserOrderByWithRelationInput.schema.ts", "schemas/objects/UserWhereUniqueInput.schema.ts", "schemas/objects/UserOrderByWithAggregationInput.schema.ts", "schemas/objects/UserScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/SessionWhereInput.schema.ts", "schemas/objects/SessionOrderByWithRelationInput.schema.ts", "schemas/objects/SessionWhereUniqueInput.schema.ts", "schemas/objects/SessionOrderByWithAggregationInput.schema.ts", "schemas/objects/SessionScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/AccountWhereInput.schema.ts", "schemas/objects/AccountOrderByWithRelationInput.schema.ts", "schemas/objects/AccountWhereUniqueInput.schema.ts", "schemas/objects/AccountOrderByWithAggregationInput.schema.ts", "schemas/objects/AccountScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/VerificationWhereInput.schema.ts", "schemas/objects/VerificationOrderByWithRelationInput.schema.ts", "schemas/objects/VerificationWhereUniqueInput.schema.ts", "schemas/objects/VerificationOrderByWithAggregationInput.schema.ts", "schemas/objects/VerificationScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/UserInvitationWhereInput.schema.ts", "schemas/objects/UserInvitationOrderByWithRelationInput.schema.ts", "schemas/objects/UserInvitationWhereUniqueInput.schema.ts", "schemas/objects/UserInvitationOrderByWithAggregationInput.schema.ts", "schemas/objects/UserInvitationScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/PaymentIntentWhereInput.schema.ts", "schemas/objects/PaymentIntentOrderByWithRelationInput.schema.ts", "schemas/objects/PaymentIntentWhereUniqueInput.schema.ts", "schemas/objects/PaymentIntentOrderByWithAggregationInput.schema.ts", "schemas/objects/PaymentIntentScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/WebhookEventWhereInput.schema.ts", "schemas/objects/WebhookEventOrderByWithRelationInput.schema.ts", "schemas/objects/WebhookEventWhereUniqueInput.schema.ts", "schemas/objects/WebhookEventOrderByWithAggregationInput.schema.ts", "schemas/objects/WebhookEventScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/LicenseWhereInput.schema.ts", "schemas/objects/LicenseOrderByWithRelationInput.schema.ts", "schemas/objects/LicenseWhereUniqueInput.schema.ts", "schemas/objects/LicenseOrderByWithAggregationInput.schema.ts", "schemas/objects/LicenseScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/DeviceWhereInput.schema.ts", "schemas/objects/DeviceOrderByWithRelationInput.schema.ts", "schemas/objects/DeviceWhereUniqueInput.schema.ts", "schemas/objects/DeviceOrderByWithAggregationInput.schema.ts", "schemas/objects/DeviceScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/DeviceExpansionWhereInput.schema.ts", "schemas/objects/DeviceExpansionOrderByWithRelationInput.schema.ts", "schemas/objects/DeviceExpansionWhereUniqueInput.schema.ts", "schemas/objects/DeviceExpansionOrderByWithAggregationInput.schema.ts", "schemas/objects/DeviceExpansionScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/RefundRequestWhereInput.schema.ts", "schemas/objects/RefundRequestOrderByWithRelationInput.schema.ts", "schemas/objects/RefundRequestWhereUniqueInput.schema.ts", "schemas/objects/RefundRequestOrderByWithAggregationInput.schema.ts", "schemas/objects/RefundRequestScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/AuditLogWhereInput.schema.ts", "schemas/objects/AuditLogOrderByWithRelationInput.schema.ts", "schemas/objects/AuditLogWhereUniqueInput.schema.ts", "schemas/objects/AuditLogOrderByWithAggregationInput.schema.ts", "schemas/objects/AuditLogScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/RateLimitWhereInput.schema.ts", "schemas/objects/RateLimitOrderByWithRelationInput.schema.ts", "schemas/objects/RateLimitWhereUniqueInput.schema.ts", "schemas/objects/RateLimitOrderByWithAggregationInput.schema.ts", "schemas/objects/RateLimitScalarWhereWithAggregatesInput.schema.ts", "schemas/objects/UserCreateInput.schema.ts", "schemas/objects/UserUncheckedCreateInput.schema.ts", "schemas/objects/UserUpdateInput.schema.ts", "schemas/objects/UserUncheckedUpdateInput.schema.ts", "schemas/objects/UserCreateManyInput.schema.ts", "schemas/objects/UserUpdateManyMutationInput.schema.ts", "schemas/objects/UserUncheckedUpdateManyInput.schema.ts", "schemas/objects/SessionCreateInput.schema.ts", "schemas/objects/SessionUncheckedCreateInput.schema.ts", "schemas/objects/SessionUpdateInput.schema.ts", "schemas/objects/SessionUncheckedUpdateInput.schema.ts", "schemas/objects/SessionCreateManyInput.schema.ts", "schemas/objects/SessionUpdateManyMutationInput.schema.ts", "schemas/objects/SessionUncheckedUpdateManyInput.schema.ts", "schemas/objects/AccountCreateInput.schema.ts", "schemas/objects/AccountUncheckedCreateInput.schema.ts", "schemas/objects/AccountUpdateInput.schema.ts", "schemas/objects/AccountUncheckedUpdateInput.schema.ts", "schemas/objects/AccountCreateManyInput.schema.ts", "schemas/objects/AccountUpdateManyMutationInput.schema.ts", "schemas/objects/AccountUncheckedUpdateManyInput.schema.ts", "schemas/objects/VerificationCreateInput.schema.ts", "schemas/objects/VerificationUncheckedCreateInput.schema.ts", "schemas/objects/VerificationUpdateInput.schema.ts", "schemas/objects/VerificationUncheckedUpdateInput.schema.ts", "schemas/objects/VerificationCreateManyInput.schema.ts", "schemas/objects/VerificationUpdateManyMutationInput.schema.ts", "schemas/objects/VerificationUncheckedUpdateManyInput.schema.ts", "schemas/objects/UserInvitationCreateInput.schema.ts", "schemas/objects/UserInvitationUncheckedCreateInput.schema.ts", "schemas/objects/UserInvitationUpdateInput.schema.ts", "schemas/objects/UserInvitationUncheckedUpdateInput.schema.ts", "schemas/objects/UserInvitationCreateManyInput.schema.ts", "schemas/objects/UserInvitationUpdateManyMutationInput.schema.ts", "schemas/objects/UserInvitationUncheckedUpdateManyInput.schema.ts", "schemas/objects/PaymentIntentCreateInput.schema.ts", "schemas/objects/PaymentIntentUncheckedCreateInput.schema.ts", "schemas/objects/PaymentIntentUpdateInput.schema.ts", "schemas/objects/PaymentIntentUncheckedUpdateInput.schema.ts", "schemas/objects/PaymentIntentCreateManyInput.schema.ts", "schemas/objects/PaymentIntentUpdateManyMutationInput.schema.ts", "schemas/objects/PaymentIntentUncheckedUpdateManyInput.schema.ts", "schemas/objects/WebhookEventCreateInput.schema.ts", "schemas/objects/WebhookEventUncheckedCreateInput.schema.ts", "schemas/objects/WebhookEventUpdateInput.schema.ts", "schemas/objects/WebhookEventUncheckedUpdateInput.schema.ts", "schemas/objects/WebhookEventCreateManyInput.schema.ts", "schemas/objects/WebhookEventUpdateManyMutationInput.schema.ts", "schemas/objects/WebhookEventUncheckedUpdateManyInput.schema.ts", "schemas/objects/LicenseCreateInput.schema.ts", "schemas/objects/LicenseUncheckedCreateInput.schema.ts", "schemas/objects/LicenseUpdateInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateInput.schema.ts", "schemas/objects/LicenseCreateManyInput.schema.ts", "schemas/objects/LicenseUpdateManyMutationInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateManyInput.schema.ts", "schemas/objects/DeviceCreateInput.schema.ts", "schemas/objects/DeviceUncheckedCreateInput.schema.ts", "schemas/objects/DeviceUpdateInput.schema.ts", "schemas/objects/DeviceUncheckedUpdateInput.schema.ts", "schemas/objects/DeviceCreateManyInput.schema.ts", "schemas/objects/DeviceUpdateManyMutationInput.schema.ts", "schemas/objects/DeviceUncheckedUpdateManyInput.schema.ts", "schemas/objects/DeviceExpansionCreateInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedCreateInput.schema.ts", "schemas/objects/DeviceExpansionUpdateInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedUpdateInput.schema.ts", "schemas/objects/DeviceExpansionCreateManyInput.schema.ts", "schemas/objects/DeviceExpansionUpdateManyMutationInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedUpdateManyInput.schema.ts", "schemas/objects/RefundRequestCreateInput.schema.ts", "schemas/objects/RefundRequestUncheckedCreateInput.schema.ts", "schemas/objects/RefundRequestUpdateInput.schema.ts", "schemas/objects/RefundRequestUncheckedUpdateInput.schema.ts", "schemas/objects/RefundRequestCreateManyInput.schema.ts", "schemas/objects/RefundRequestUpdateManyMutationInput.schema.ts", "schemas/objects/RefundRequestUncheckedUpdateManyInput.schema.ts", "schemas/objects/AuditLogCreateInput.schema.ts", "schemas/objects/AuditLogUncheckedCreateInput.schema.ts", "schemas/objects/AuditLogUpdateInput.schema.ts", "schemas/objects/AuditLogUncheckedUpdateInput.schema.ts", "schemas/objects/AuditLogCreateManyInput.schema.ts", "schemas/objects/AuditLogUpdateManyMutationInput.schema.ts", "schemas/objects/AuditLogUncheckedUpdateManyInput.schema.ts", "schemas/objects/RateLimitCreateInput.schema.ts", "schemas/objects/RateLimitUncheckedCreateInput.schema.ts", "schemas/objects/RateLimitUpdateInput.schema.ts", "schemas/objects/RateLimitUncheckedUpdateInput.schema.ts", "schemas/objects/RateLimitCreateManyInput.schema.ts", "schemas/objects/RateLimitUpdateManyMutationInput.schema.ts", "schemas/objects/RateLimitUncheckedUpdateManyInput.schema.ts", "schemas/objects/StringFilter.schema.ts", "schemas/objects/BoolFilter.schema.ts", "schemas/objects/StringNullableFilter.schema.ts", "schemas/objects/EnumUserRoleFilter.schema.ts", "schemas/objects/DateTimeNullableFilter.schema.ts", "schemas/objects/DateTimeFilter.schema.ts", "schemas/objects/SessionListRelationFilter.schema.ts", "schemas/objects/AccountListRelationFilter.schema.ts", "schemas/objects/LicenseListRelationFilter.schema.ts", "schemas/objects/UserInvitationListRelationFilter.schema.ts", "schemas/objects/AuditLogListRelationFilter.schema.ts", "schemas/objects/RefundRequestListRelationFilter.schema.ts", "schemas/objects/SortOrderInput.schema.ts", "schemas/objects/SessionOrderByRelationAggregateInput.schema.ts", "schemas/objects/AccountOrderByRelationAggregateInput.schema.ts", "schemas/objects/LicenseOrderByRelationAggregateInput.schema.ts", "schemas/objects/UserInvitationOrderByRelationAggregateInput.schema.ts", "schemas/objects/AuditLogOrderByRelationAggregateInput.schema.ts", "schemas/objects/RefundRequestOrderByRelationAggregateInput.schema.ts", "schemas/objects/UserCountOrderByAggregateInput.schema.ts", "schemas/objects/UserMaxOrderByAggregateInput.schema.ts", "schemas/objects/UserMinOrderByAggregateInput.schema.ts", "schemas/objects/StringWithAggregatesFilter.schema.ts", "schemas/objects/BoolWithAggregatesFilter.schema.ts", "schemas/objects/StringNullableWithAggregatesFilter.schema.ts", "schemas/objects/EnumUserRoleWithAggregatesFilter.schema.ts", "schemas/objects/DateTimeNullableWithAggregatesFilter.schema.ts", "schemas/objects/DateTimeWithAggregatesFilter.schema.ts", "schemas/objects/UserScalarRelationFilter.schema.ts", "schemas/objects/SessionCountOrderByAggregateInput.schema.ts", "schemas/objects/SessionMaxOrderByAggregateInput.schema.ts", "schemas/objects/SessionMinOrderByAggregateInput.schema.ts", "schemas/objects/AccountCountOrderByAggregateInput.schema.ts", "schemas/objects/AccountMaxOrderByAggregateInput.schema.ts", "schemas/objects/AccountMinOrderByAggregateInput.schema.ts", "schemas/objects/VerificationCountOrderByAggregateInput.schema.ts", "schemas/objects/VerificationMaxOrderByAggregateInput.schema.ts", "schemas/objects/VerificationMinOrderByAggregateInput.schema.ts", "schemas/objects/EnumInvitationStatusFilter.schema.ts", "schemas/objects/UserNullableScalarRelationFilter.schema.ts", "schemas/objects/UserInvitationCountOrderByAggregateInput.schema.ts", "schemas/objects/UserInvitationMaxOrderByAggregateInput.schema.ts", "schemas/objects/UserInvitationMinOrderByAggregateInput.schema.ts", "schemas/objects/EnumInvitationStatusWithAggregatesFilter.schema.ts", "schemas/objects/IntFilter.schema.ts", "schemas/objects/EnumPaymentStatusFilter.schema.ts", "schemas/objects/EnumPaymentTypeFilter.schema.ts", "schemas/objects/DeviceExpansionListRelationFilter.schema.ts", "schemas/objects/WebhookEventListRelationFilter.schema.ts", "schemas/objects/DeviceExpansionOrderByRelationAggregateInput.schema.ts", "schemas/objects/WebhookEventOrderByRelationAggregateInput.schema.ts", "schemas/objects/PaymentIntentCountOrderByAggregateInput.schema.ts", "schemas/objects/PaymentIntentAvgOrderByAggregateInput.schema.ts", "schemas/objects/PaymentIntentMaxOrderByAggregateInput.schema.ts", "schemas/objects/PaymentIntentMinOrderByAggregateInput.schema.ts", "schemas/objects/PaymentIntentSumOrderByAggregateInput.schema.ts", "schemas/objects/IntWithAggregatesFilter.schema.ts", "schemas/objects/EnumPaymentStatusWithAggregatesFilter.schema.ts", "schemas/objects/EnumPaymentTypeWithAggregatesFilter.schema.ts", "schemas/objects/PaymentIntentNullableScalarRelationFilter.schema.ts", "schemas/objects/WebhookEventCountOrderByAggregateInput.schema.ts", "schemas/objects/WebhookEventAvgOrderByAggregateInput.schema.ts", "schemas/objects/WebhookEventMaxOrderByAggregateInput.schema.ts", "schemas/objects/WebhookEventMinOrderByAggregateInput.schema.ts", "schemas/objects/WebhookEventSumOrderByAggregateInput.schema.ts", "schemas/objects/EnumLicenseTypeFilter.schema.ts", "schemas/objects/EnumLicenseStatusFilter.schema.ts", "schemas/objects/IntNullableFilter.schema.ts", "schemas/objects/DeviceListRelationFilter.schema.ts", "schemas/objects/DeviceOrderByRelationAggregateInput.schema.ts", "schemas/objects/LicenseCountOrderByAggregateInput.schema.ts", "schemas/objects/LicenseAvgOrderByAggregateInput.schema.ts", "schemas/objects/LicenseMaxOrderByAggregateInput.schema.ts", "schemas/objects/LicenseMinOrderByAggregateInput.schema.ts", "schemas/objects/LicenseSumOrderByAggregateInput.schema.ts", "schemas/objects/EnumLicenseTypeWithAggregatesFilter.schema.ts", "schemas/objects/EnumLicenseStatusWithAggregatesFilter.schema.ts", "schemas/objects/IntNullableWithAggregatesFilter.schema.ts", "schemas/objects/EnumDeviceStatusFilter.schema.ts", "schemas/objects/LicenseScalarRelationFilter.schema.ts", "schemas/objects/DeviceLicenseIdDeviceHashCompoundUniqueInput.schema.ts", "schemas/objects/DeviceCountOrderByAggregateInput.schema.ts", "schemas/objects/DeviceMaxOrderByAggregateInput.schema.ts", "schemas/objects/DeviceMinOrderByAggregateInput.schema.ts", "schemas/objects/EnumDeviceStatusWithAggregatesFilter.schema.ts", "schemas/objects/EnumDeviceExpansionStatusFilter.schema.ts", "schemas/objects/PaymentIntentScalarRelationFilter.schema.ts", "schemas/objects/DeviceExpansionCountOrderByAggregateInput.schema.ts", "schemas/objects/DeviceExpansionAvgOrderByAggregateInput.schema.ts", "schemas/objects/DeviceExpansionMaxOrderByAggregateInput.schema.ts", "schemas/objects/DeviceExpansionMinOrderByAggregateInput.schema.ts", "schemas/objects/DeviceExpansionSumOrderByAggregateInput.schema.ts", "schemas/objects/EnumDeviceExpansionStatusWithAggregatesFilter.schema.ts", "schemas/objects/EnumRefundStatusFilter.schema.ts", "schemas/objects/StringNullableListFilter.schema.ts", "schemas/objects/RefundRequestCountOrderByAggregateInput.schema.ts", "schemas/objects/RefundRequestAvgOrderByAggregateInput.schema.ts", "schemas/objects/RefundRequestMaxOrderByAggregateInput.schema.ts", "schemas/objects/RefundRequestMinOrderByAggregateInput.schema.ts", "schemas/objects/RefundRequestSumOrderByAggregateInput.schema.ts", "schemas/objects/EnumRefundStatusWithAggregatesFilter.schema.ts", "schemas/objects/EnumAuditActionFilter.schema.ts", "schemas/objects/JsonNullableFilter.schema.ts", "schemas/objects/AuditLogCountOrderByAggregateInput.schema.ts", "schemas/objects/AuditLogMaxOrderByAggregateInput.schema.ts", "schemas/objects/AuditLogMinOrderByAggregateInput.schema.ts", "schemas/objects/EnumAuditActionWithAggregatesFilter.schema.ts", "schemas/objects/JsonNullableWithAggregatesFilter.schema.ts", "schemas/objects/RateLimitIdentifierActionCompoundUniqueInput.schema.ts", "schemas/objects/RateLimitCountOrderByAggregateInput.schema.ts", "schemas/objects/RateLimitAvgOrderByAggregateInput.schema.ts", "schemas/objects/RateLimitMaxOrderByAggregateInput.schema.ts", "schemas/objects/RateLimitMinOrderByAggregateInput.schema.ts", "schemas/objects/RateLimitSumOrderByAggregateInput.schema.ts", "schemas/objects/SessionCreateNestedManyWithoutUserInput.schema.ts", "schemas/objects/AccountCreateNestedManyWithoutUserInput.schema.ts", "schemas/objects/LicenseCreateNestedManyWithoutCreatedByUserInput.schema.ts", "schemas/objects/UserInvitationCreateNestedManyWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationCreateNestedManyWithoutAcceptedByUserInput.schema.ts", "schemas/objects/AuditLogCreateNestedManyWithoutActorInput.schema.ts", "schemas/objects/AuditLogCreateNestedManyWithoutTargetInput.schema.ts", "schemas/objects/RefundRequestCreateNestedManyWithoutProcessedByUserInput.schema.ts", "schemas/objects/SessionUncheckedCreateNestedManyWithoutUserInput.schema.ts", "schemas/objects/AccountUncheckedCreateNestedManyWithoutUserInput.schema.ts", "schemas/objects/LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput.schema.ts", "schemas/objects/UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput.schema.ts", "schemas/objects/AuditLogUncheckedCreateNestedManyWithoutActorInput.schema.ts", "schemas/objects/AuditLogUncheckedCreateNestedManyWithoutTargetInput.schema.ts", "schemas/objects/RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput.schema.ts", "schemas/objects/StringFieldUpdateOperationsInput.schema.ts", "schemas/objects/BoolFieldUpdateOperationsInput.schema.ts", "schemas/objects/NullableStringFieldUpdateOperationsInput.schema.ts", "schemas/objects/EnumUserRoleFieldUpdateOperationsInput.schema.ts", "schemas/objects/NullableDateTimeFieldUpdateOperationsInput.schema.ts", "schemas/objects/DateTimeFieldUpdateOperationsInput.schema.ts", "schemas/objects/SessionUpdateManyWithoutUserNestedInput.schema.ts", "schemas/objects/AccountUpdateManyWithoutUserNestedInput.schema.ts", "schemas/objects/LicenseUpdateManyWithoutCreatedByUserNestedInput.schema.ts", "schemas/objects/UserInvitationUpdateManyWithoutSentByUserNestedInput.schema.ts", "schemas/objects/UserInvitationUpdateManyWithoutAcceptedByUserNestedInput.schema.ts", "schemas/objects/AuditLogUpdateManyWithoutActorNestedInput.schema.ts", "schemas/objects/AuditLogUpdateManyWithoutTargetNestedInput.schema.ts", "schemas/objects/RefundRequestUpdateManyWithoutProcessedByUserNestedInput.schema.ts", "schemas/objects/SessionUncheckedUpdateManyWithoutUserNestedInput.schema.ts", "schemas/objects/AccountUncheckedUpdateManyWithoutUserNestedInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput.schema.ts", "schemas/objects/UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput.schema.ts", "schemas/objects/UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput.schema.ts", "schemas/objects/AuditLogUncheckedUpdateManyWithoutActorNestedInput.schema.ts", "schemas/objects/AuditLogUncheckedUpdateManyWithoutTargetNestedInput.schema.ts", "schemas/objects/RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput.schema.ts", "schemas/objects/UserCreateNestedOneWithoutSessionsInput.schema.ts", "schemas/objects/UserUpdateOneRequiredWithoutSessionsNestedInput.schema.ts", "schemas/objects/UserCreateNestedOneWithoutAccountsInput.schema.ts", "schemas/objects/UserUpdateOneRequiredWithoutAccountsNestedInput.schema.ts", "schemas/objects/UserCreateNestedOneWithoutSentInvitationsInput.schema.ts", "schemas/objects/UserCreateNestedOneWithoutReceivedInvitationsInput.schema.ts", "schemas/objects/EnumInvitationStatusFieldUpdateOperationsInput.schema.ts", "schemas/objects/UserUpdateOneRequiredWithoutSentInvitationsNestedInput.schema.ts", "schemas/objects/UserUpdateOneWithoutReceivedInvitationsNestedInput.schema.ts", "schemas/objects/LicenseCreateNestedManyWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionCreateNestedManyWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventCreateNestedManyWithoutPaymentIntentInput.schema.ts", "schemas/objects/LicenseUncheckedCreateNestedManyWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedCreateNestedManyWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventUncheckedCreateNestedManyWithoutPaymentIntentInput.schema.ts", "schemas/objects/IntFieldUpdateOperationsInput.schema.ts", "schemas/objects/EnumPaymentStatusFieldUpdateOperationsInput.schema.ts", "schemas/objects/EnumPaymentTypeFieldUpdateOperationsInput.schema.ts", "schemas/objects/LicenseUpdateManyWithoutPaymentIntentNestedInput.schema.ts", "schemas/objects/DeviceExpansionUpdateManyWithoutPaymentIntentNestedInput.schema.ts", "schemas/objects/WebhookEventUpdateManyWithoutPaymentIntentNestedInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateManyWithoutPaymentIntentNestedInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentNestedInput.schema.ts", "schemas/objects/WebhookEventUncheckedUpdateManyWithoutPaymentIntentNestedInput.schema.ts", "schemas/objects/PaymentIntentCreateNestedOneWithoutWebhookEventsInput.schema.ts", "schemas/objects/PaymentIntentUpdateOneWithoutWebhookEventsNestedInput.schema.ts", "schemas/objects/UserCreateNestedOneWithoutCreatedLicensesInput.schema.ts", "schemas/objects/PaymentIntentCreateNestedOneWithoutLicensesInput.schema.ts", "schemas/objects/DeviceCreateNestedManyWithoutLicenseInput.schema.ts", "schemas/objects/DeviceExpansionCreateNestedManyWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestCreateNestedManyWithoutLicenseInput.schema.ts", "schemas/objects/DeviceUncheckedCreateNestedManyWithoutLicenseInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedCreateNestedManyWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestUncheckedCreateNestedManyWithoutLicenseInput.schema.ts", "schemas/objects/EnumLicenseTypeFieldUpdateOperationsInput.schema.ts", "schemas/objects/EnumLicenseStatusFieldUpdateOperationsInput.schema.ts", "schemas/objects/NullableIntFieldUpdateOperationsInput.schema.ts", "schemas/objects/UserUpdateOneWithoutCreatedLicensesNestedInput.schema.ts", "schemas/objects/PaymentIntentUpdateOneWithoutLicensesNestedInput.schema.ts", "schemas/objects/DeviceUpdateManyWithoutLicenseNestedInput.schema.ts", "schemas/objects/DeviceExpansionUpdateManyWithoutLicenseNestedInput.schema.ts", "schemas/objects/RefundRequestUpdateManyWithoutLicenseNestedInput.schema.ts", "schemas/objects/DeviceUncheckedUpdateManyWithoutLicenseNestedInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedUpdateManyWithoutLicenseNestedInput.schema.ts", "schemas/objects/RefundRequestUncheckedUpdateManyWithoutLicenseNestedInput.schema.ts", "schemas/objects/LicenseCreateNestedOneWithoutDevicesInput.schema.ts", "schemas/objects/EnumDeviceStatusFieldUpdateOperationsInput.schema.ts", "schemas/objects/LicenseUpdateOneRequiredWithoutDevicesNestedInput.schema.ts", "schemas/objects/LicenseCreateNestedOneWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/PaymentIntentCreateNestedOneWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/EnumDeviceExpansionStatusFieldUpdateOperationsInput.schema.ts", "schemas/objects/LicenseUpdateOneRequiredWithoutDeviceExpansionsNestedInput.schema.ts", "schemas/objects/PaymentIntentUpdateOneRequiredWithoutDeviceExpansionsNestedInput.schema.ts", "schemas/objects/RefundRequestCreatestripeRefundIdsInput.schema.ts", "schemas/objects/UserCreateNestedOneWithoutProcessedRefundsInput.schema.ts", "schemas/objects/LicenseCreateNestedOneWithoutRefundRequestsInput.schema.ts", "schemas/objects/EnumRefundStatusFieldUpdateOperationsInput.schema.ts", "schemas/objects/RefundRequestUpdatestripeRefundIdsInput.schema.ts", "schemas/objects/UserUpdateOneWithoutProcessedRefundsNestedInput.schema.ts", "schemas/objects/LicenseUpdateOneRequiredWithoutRefundRequestsNestedInput.schema.ts", "schemas/objects/UserCreateNestedOneWithoutAuditLogsAsActorInput.schema.ts", "schemas/objects/UserCreateNestedOneWithoutAuditLogsAsTargetInput.schema.ts", "schemas/objects/EnumAuditActionFieldUpdateOperationsInput.schema.ts", "schemas/objects/UserUpdateOneWithoutAuditLogsAsActorNestedInput.schema.ts", "schemas/objects/UserUpdateOneWithoutAuditLogsAsTargetNestedInput.schema.ts", "schemas/objects/NestedStringFilter.schema.ts", "schemas/objects/NestedBoolFilter.schema.ts", "schemas/objects/NestedStringNullableFilter.schema.ts", "schemas/objects/NestedEnumUserRoleFilter.schema.ts", "schemas/objects/NestedDateTimeNullableFilter.schema.ts", "schemas/objects/NestedDateTimeFilter.schema.ts", "schemas/objects/NestedStringWithAggregatesFilter.schema.ts", "schemas/objects/NestedIntFilter.schema.ts", "schemas/objects/NestedBoolWithAggregatesFilter.schema.ts", "schemas/objects/NestedStringNullableWithAggregatesFilter.schema.ts", "schemas/objects/NestedIntNullableFilter.schema.ts", "schemas/objects/NestedEnumUserRoleWithAggregatesFilter.schema.ts", "schemas/objects/NestedDateTimeNullableWithAggregatesFilter.schema.ts", "schemas/objects/NestedDateTimeWithAggregatesFilter.schema.ts", "schemas/objects/NestedEnumInvitationStatusFilter.schema.ts", "schemas/objects/NestedEnumInvitationStatusWithAggregatesFilter.schema.ts", "schemas/objects/NestedEnumPaymentStatusFilter.schema.ts", "schemas/objects/NestedEnumPaymentTypeFilter.schema.ts", "schemas/objects/NestedIntWithAggregatesFilter.schema.ts", "schemas/objects/NestedFloatFilter.schema.ts", "schemas/objects/NestedEnumPaymentStatusWithAggregatesFilter.schema.ts", "schemas/objects/NestedEnumPaymentTypeWithAggregatesFilter.schema.ts", "schemas/objects/NestedEnumLicenseTypeFilter.schema.ts", "schemas/objects/NestedEnumLicenseStatusFilter.schema.ts", "schemas/objects/NestedEnumLicenseTypeWithAggregatesFilter.schema.ts", "schemas/objects/NestedEnumLicenseStatusWithAggregatesFilter.schema.ts", "schemas/objects/NestedIntNullableWithAggregatesFilter.schema.ts", "schemas/objects/NestedFloatNullableFilter.schema.ts", "schemas/objects/NestedEnumDeviceStatusFilter.schema.ts", "schemas/objects/NestedEnumDeviceStatusWithAggregatesFilter.schema.ts", "schemas/objects/NestedEnumDeviceExpansionStatusFilter.schema.ts", "schemas/objects/NestedEnumDeviceExpansionStatusWithAggregatesFilter.schema.ts", "schemas/objects/NestedEnumRefundStatusFilter.schema.ts", "schemas/objects/NestedEnumRefundStatusWithAggregatesFilter.schema.ts", "schemas/objects/NestedEnumAuditActionFilter.schema.ts", "schemas/objects/NestedEnumAuditActionWithAggregatesFilter.schema.ts", "schemas/objects/NestedJsonNullableFilter.schema.ts", "schemas/objects/SessionCreateWithoutUserInput.schema.ts", "schemas/objects/SessionUncheckedCreateWithoutUserInput.schema.ts", "schemas/objects/SessionCreateOrConnectWithoutUserInput.schema.ts", "schemas/objects/SessionCreateManyUserInputEnvelope.schema.ts", "schemas/objects/AccountCreateWithoutUserInput.schema.ts", "schemas/objects/AccountUncheckedCreateWithoutUserInput.schema.ts", "schemas/objects/AccountCreateOrConnectWithoutUserInput.schema.ts", "schemas/objects/AccountCreateManyUserInputEnvelope.schema.ts", "schemas/objects/LicenseCreateWithoutCreatedByUserInput.schema.ts", "schemas/objects/LicenseUncheckedCreateWithoutCreatedByUserInput.schema.ts", "schemas/objects/LicenseCreateOrConnectWithoutCreatedByUserInput.schema.ts", "schemas/objects/LicenseCreateManyCreatedByUserInputEnvelope.schema.ts", "schemas/objects/UserInvitationCreateWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationUncheckedCreateWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationCreateOrConnectWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationCreateManySentByUserInputEnvelope.schema.ts", "schemas/objects/UserInvitationCreateWithoutAcceptedByUserInput.schema.ts", "schemas/objects/UserInvitationUncheckedCreateWithoutAcceptedByUserInput.schema.ts", "schemas/objects/UserInvitationCreateOrConnectWithoutAcceptedByUserInput.schema.ts", "schemas/objects/UserInvitationCreateManyAcceptedByUserInputEnvelope.schema.ts", "schemas/objects/AuditLogCreateWithoutActorInput.schema.ts", "schemas/objects/AuditLogUncheckedCreateWithoutActorInput.schema.ts", "schemas/objects/AuditLogCreateOrConnectWithoutActorInput.schema.ts", "schemas/objects/AuditLogCreateManyActorInputEnvelope.schema.ts", "schemas/objects/AuditLogCreateWithoutTargetInput.schema.ts", "schemas/objects/AuditLogUncheckedCreateWithoutTargetInput.schema.ts", "schemas/objects/AuditLogCreateOrConnectWithoutTargetInput.schema.ts", "schemas/objects/AuditLogCreateManyTargetInputEnvelope.schema.ts", "schemas/objects/RefundRequestCreateWithoutProcessedByUserInput.schema.ts", "schemas/objects/RefundRequestUncheckedCreateWithoutProcessedByUserInput.schema.ts", "schemas/objects/RefundRequestCreateOrConnectWithoutProcessedByUserInput.schema.ts", "schemas/objects/RefundRequestCreateManyProcessedByUserInputEnvelope.schema.ts", "schemas/objects/SessionUpsertWithWhereUniqueWithoutUserInput.schema.ts", "schemas/objects/SessionUpdateWithWhereUniqueWithoutUserInput.schema.ts", "schemas/objects/SessionUpdateManyWithWhereWithoutUserInput.schema.ts", "schemas/objects/SessionScalarWhereInput.schema.ts", "schemas/objects/AccountUpsertWithWhereUniqueWithoutUserInput.schema.ts", "schemas/objects/AccountUpdateWithWhereUniqueWithoutUserInput.schema.ts", "schemas/objects/AccountUpdateManyWithWhereWithoutUserInput.schema.ts", "schemas/objects/AccountScalarWhereInput.schema.ts", "schemas/objects/LicenseUpsertWithWhereUniqueWithoutCreatedByUserInput.schema.ts", "schemas/objects/LicenseUpdateWithWhereUniqueWithoutCreatedByUserInput.schema.ts", "schemas/objects/LicenseUpdateManyWithWhereWithoutCreatedByUserInput.schema.ts", "schemas/objects/LicenseScalarWhereInput.schema.ts", "schemas/objects/UserInvitationUpsertWithWhereUniqueWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationUpdateWithWhereUniqueWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationUpdateManyWithWhereWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationScalarWhereInput.schema.ts", "schemas/objects/UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInput.schema.ts", "schemas/objects/UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInput.schema.ts", "schemas/objects/UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInput.schema.ts", "schemas/objects/AuditLogUpsertWithWhereUniqueWithoutActorInput.schema.ts", "schemas/objects/AuditLogUpdateWithWhereUniqueWithoutActorInput.schema.ts", "schemas/objects/AuditLogUpdateManyWithWhereWithoutActorInput.schema.ts", "schemas/objects/AuditLogScalarWhereInput.schema.ts", "schemas/objects/AuditLogUpsertWithWhereUniqueWithoutTargetInput.schema.ts", "schemas/objects/AuditLogUpdateWithWhereUniqueWithoutTargetInput.schema.ts", "schemas/objects/AuditLogUpdateManyWithWhereWithoutTargetInput.schema.ts", "schemas/objects/RefundRequestUpsertWithWhereUniqueWithoutProcessedByUserInput.schema.ts", "schemas/objects/RefundRequestUpdateWithWhereUniqueWithoutProcessedByUserInput.schema.ts", "schemas/objects/RefundRequestUpdateManyWithWhereWithoutProcessedByUserInput.schema.ts", "schemas/objects/RefundRequestScalarWhereInput.schema.ts", "schemas/objects/UserCreateWithoutSessionsInput.schema.ts", "schemas/objects/UserUncheckedCreateWithoutSessionsInput.schema.ts", "schemas/objects/UserCreateOrConnectWithoutSessionsInput.schema.ts", "schemas/objects/UserUpsertWithoutSessionsInput.schema.ts", "schemas/objects/UserUpdateToOneWithWhereWithoutSessionsInput.schema.ts", "schemas/objects/UserUpdateWithoutSessionsInput.schema.ts", "schemas/objects/UserUncheckedUpdateWithoutSessionsInput.schema.ts", "schemas/objects/UserCreateWithoutAccountsInput.schema.ts", "schemas/objects/UserUncheckedCreateWithoutAccountsInput.schema.ts", "schemas/objects/UserCreateOrConnectWithoutAccountsInput.schema.ts", "schemas/objects/UserUpsertWithoutAccountsInput.schema.ts", "schemas/objects/UserUpdateToOneWithWhereWithoutAccountsInput.schema.ts", "schemas/objects/UserUpdateWithoutAccountsInput.schema.ts", "schemas/objects/UserUncheckedUpdateWithoutAccountsInput.schema.ts", "schemas/objects/UserCreateWithoutSentInvitationsInput.schema.ts", "schemas/objects/UserUncheckedCreateWithoutSentInvitationsInput.schema.ts", "schemas/objects/UserCreateOrConnectWithoutSentInvitationsInput.schema.ts", "schemas/objects/UserCreateWithoutReceivedInvitationsInput.schema.ts", "schemas/objects/UserUncheckedCreateWithoutReceivedInvitationsInput.schema.ts", "schemas/objects/UserCreateOrConnectWithoutReceivedInvitationsInput.schema.ts", "schemas/objects/UserUpsertWithoutSentInvitationsInput.schema.ts", "schemas/objects/UserUpdateToOneWithWhereWithoutSentInvitationsInput.schema.ts", "schemas/objects/UserUpdateWithoutSentInvitationsInput.schema.ts", "schemas/objects/UserUncheckedUpdateWithoutSentInvitationsInput.schema.ts", "schemas/objects/UserUpsertWithoutReceivedInvitationsInput.schema.ts", "schemas/objects/UserUpdateToOneWithWhereWithoutReceivedInvitationsInput.schema.ts", "schemas/objects/UserUpdateWithoutReceivedInvitationsInput.schema.ts", "schemas/objects/UserUncheckedUpdateWithoutReceivedInvitationsInput.schema.ts", "schemas/objects/LicenseCreateWithoutPaymentIntentInput.schema.ts", "schemas/objects/LicenseUncheckedCreateWithoutPaymentIntentInput.schema.ts", "schemas/objects/LicenseCreateOrConnectWithoutPaymentIntentInput.schema.ts", "schemas/objects/LicenseCreateManyPaymentIntentInputEnvelope.schema.ts", "schemas/objects/DeviceExpansionCreateWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedCreateWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionCreateOrConnectWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionCreateManyPaymentIntentInputEnvelope.schema.ts", "schemas/objects/WebhookEventCreateWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventUncheckedCreateWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventCreateOrConnectWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventCreateManyPaymentIntentInputEnvelope.schema.ts", "schemas/objects/LicenseUpsertWithWhereUniqueWithoutPaymentIntentInput.schema.ts", "schemas/objects/LicenseUpdateWithWhereUniqueWithoutPaymentIntentInput.schema.ts", "schemas/objects/LicenseUpdateManyWithWhereWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionScalarWhereInput.schema.ts", "schemas/objects/WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventUpdateManyWithWhereWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventScalarWhereInput.schema.ts", "schemas/objects/PaymentIntentCreateWithoutWebhookEventsInput.schema.ts", "schemas/objects/PaymentIntentUncheckedCreateWithoutWebhookEventsInput.schema.ts", "schemas/objects/PaymentIntentCreateOrConnectWithoutWebhookEventsInput.schema.ts", "schemas/objects/PaymentIntentUpsertWithoutWebhookEventsInput.schema.ts", "schemas/objects/PaymentIntentUpdateToOneWithWhereWithoutWebhookEventsInput.schema.ts", "schemas/objects/PaymentIntentUpdateWithoutWebhookEventsInput.schema.ts", "schemas/objects/PaymentIntentUncheckedUpdateWithoutWebhookEventsInput.schema.ts", "schemas/objects/UserCreateWithoutCreatedLicensesInput.schema.ts", "schemas/objects/UserUncheckedCreateWithoutCreatedLicensesInput.schema.ts", "schemas/objects/UserCreateOrConnectWithoutCreatedLicensesInput.schema.ts", "schemas/objects/PaymentIntentCreateWithoutLicensesInput.schema.ts", "schemas/objects/PaymentIntentUncheckedCreateWithoutLicensesInput.schema.ts", "schemas/objects/PaymentIntentCreateOrConnectWithoutLicensesInput.schema.ts", "schemas/objects/DeviceCreateWithoutLicenseInput.schema.ts", "schemas/objects/DeviceUncheckedCreateWithoutLicenseInput.schema.ts", "schemas/objects/DeviceCreateOrConnectWithoutLicenseInput.schema.ts", "schemas/objects/DeviceCreateManyLicenseInputEnvelope.schema.ts", "schemas/objects/DeviceExpansionCreateWithoutLicenseInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedCreateWithoutLicenseInput.schema.ts", "schemas/objects/DeviceExpansionCreateOrConnectWithoutLicenseInput.schema.ts", "schemas/objects/DeviceExpansionCreateManyLicenseInputEnvelope.schema.ts", "schemas/objects/RefundRequestCreateWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestUncheckedCreateWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestCreateOrConnectWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestCreateManyLicenseInputEnvelope.schema.ts", "schemas/objects/UserUpsertWithoutCreatedLicensesInput.schema.ts", "schemas/objects/UserUpdateToOneWithWhereWithoutCreatedLicensesInput.schema.ts", "schemas/objects/UserUpdateWithoutCreatedLicensesInput.schema.ts", "schemas/objects/UserUncheckedUpdateWithoutCreatedLicensesInput.schema.ts", "schemas/objects/PaymentIntentUpsertWithoutLicensesInput.schema.ts", "schemas/objects/PaymentIntentUpdateToOneWithWhereWithoutLicensesInput.schema.ts", "schemas/objects/PaymentIntentUpdateWithoutLicensesInput.schema.ts", "schemas/objects/PaymentIntentUncheckedUpdateWithoutLicensesInput.schema.ts", "schemas/objects/DeviceUpsertWithWhereUniqueWithoutLicenseInput.schema.ts", "schemas/objects/DeviceUpdateWithWhereUniqueWithoutLicenseInput.schema.ts", "schemas/objects/DeviceUpdateManyWithWhereWithoutLicenseInput.schema.ts", "schemas/objects/DeviceScalarWhereInput.schema.ts", "schemas/objects/DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInput.schema.ts", "schemas/objects/DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInput.schema.ts", "schemas/objects/DeviceExpansionUpdateManyWithWhereWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestUpsertWithWhereUniqueWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestUpdateWithWhereUniqueWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestUpdateManyWithWhereWithoutLicenseInput.schema.ts", "schemas/objects/LicenseCreateWithoutDevicesInput.schema.ts", "schemas/objects/LicenseUncheckedCreateWithoutDevicesInput.schema.ts", "schemas/objects/LicenseCreateOrConnectWithoutDevicesInput.schema.ts", "schemas/objects/LicenseUpsertWithoutDevicesInput.schema.ts", "schemas/objects/LicenseUpdateToOneWithWhereWithoutDevicesInput.schema.ts", "schemas/objects/LicenseUpdateWithoutDevicesInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateWithoutDevicesInput.schema.ts", "schemas/objects/LicenseCreateWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/LicenseUncheckedCreateWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/LicenseCreateOrConnectWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/PaymentIntentCreateWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/PaymentIntentUncheckedCreateWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/PaymentIntentCreateOrConnectWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/LicenseUpsertWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/LicenseUpdateToOneWithWhereWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/LicenseUpdateWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/PaymentIntentUpsertWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/PaymentIntentUpdateToOneWithWhereWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/PaymentIntentUpdateWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/PaymentIntentUncheckedUpdateWithoutDeviceExpansionsInput.schema.ts", "schemas/objects/UserCreateWithoutProcessedRefundsInput.schema.ts", "schemas/objects/UserUncheckedCreateWithoutProcessedRefundsInput.schema.ts", "schemas/objects/UserCreateOrConnectWithoutProcessedRefundsInput.schema.ts", "schemas/objects/LicenseCreateWithoutRefundRequestsInput.schema.ts", "schemas/objects/LicenseUncheckedCreateWithoutRefundRequestsInput.schema.ts", "schemas/objects/LicenseCreateOrConnectWithoutRefundRequestsInput.schema.ts", "schemas/objects/UserUpsertWithoutProcessedRefundsInput.schema.ts", "schemas/objects/UserUpdateToOneWithWhereWithoutProcessedRefundsInput.schema.ts", "schemas/objects/UserUpdateWithoutProcessedRefundsInput.schema.ts", "schemas/objects/UserUncheckedUpdateWithoutProcessedRefundsInput.schema.ts", "schemas/objects/LicenseUpsertWithoutRefundRequestsInput.schema.ts", "schemas/objects/LicenseUpdateToOneWithWhereWithoutRefundRequestsInput.schema.ts", "schemas/objects/LicenseUpdateWithoutRefundRequestsInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateWithoutRefundRequestsInput.schema.ts", "schemas/objects/UserCreateWithoutAuditLogsAsActorInput.schema.ts", "schemas/objects/UserUncheckedCreateWithoutAuditLogsAsActorInput.schema.ts", "schemas/objects/UserCreateOrConnectWithoutAuditLogsAsActorInput.schema.ts", "schemas/objects/UserCreateWithoutAuditLogsAsTargetInput.schema.ts", "schemas/objects/UserUncheckedCreateWithoutAuditLogsAsTargetInput.schema.ts", "schemas/objects/UserCreateOrConnectWithoutAuditLogsAsTargetInput.schema.ts", "schemas/objects/UserUpsertWithoutAuditLogsAsActorInput.schema.ts", "schemas/objects/UserUpdateToOneWithWhereWithoutAuditLogsAsActorInput.schema.ts", "schemas/objects/UserUpdateWithoutAuditLogsAsActorInput.schema.ts", "schemas/objects/UserUncheckedUpdateWithoutAuditLogsAsActorInput.schema.ts", "schemas/objects/UserUpsertWithoutAuditLogsAsTargetInput.schema.ts", "schemas/objects/UserUpdateToOneWithWhereWithoutAuditLogsAsTargetInput.schema.ts", "schemas/objects/UserUpdateWithoutAuditLogsAsTargetInput.schema.ts", "schemas/objects/UserUncheckedUpdateWithoutAuditLogsAsTargetInput.schema.ts", "schemas/objects/SessionCreateManyUserInput.schema.ts", "schemas/objects/AccountCreateManyUserInput.schema.ts", "schemas/objects/LicenseCreateManyCreatedByUserInput.schema.ts", "schemas/objects/UserInvitationCreateManySentByUserInput.schema.ts", "schemas/objects/UserInvitationCreateManyAcceptedByUserInput.schema.ts", "schemas/objects/AuditLogCreateManyActorInput.schema.ts", "schemas/objects/AuditLogCreateManyTargetInput.schema.ts", "schemas/objects/RefundRequestCreateManyProcessedByUserInput.schema.ts", "schemas/objects/SessionUpdateWithoutUserInput.schema.ts", "schemas/objects/SessionUncheckedUpdateWithoutUserInput.schema.ts", "schemas/objects/SessionUncheckedUpdateManyWithoutUserInput.schema.ts", "schemas/objects/AccountUpdateWithoutUserInput.schema.ts", "schemas/objects/AccountUncheckedUpdateWithoutUserInput.schema.ts", "schemas/objects/AccountUncheckedUpdateManyWithoutUserInput.schema.ts", "schemas/objects/LicenseUpdateWithoutCreatedByUserInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateWithoutCreatedByUserInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateManyWithoutCreatedByUserInput.schema.ts", "schemas/objects/UserInvitationUpdateWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationUncheckedUpdateWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationUncheckedUpdateManyWithoutSentByUserInput.schema.ts", "schemas/objects/UserInvitationUpdateWithoutAcceptedByUserInput.schema.ts", "schemas/objects/UserInvitationUncheckedUpdateWithoutAcceptedByUserInput.schema.ts", "schemas/objects/UserInvitationUncheckedUpdateManyWithoutAcceptedByUserInput.schema.ts", "schemas/objects/AuditLogUpdateWithoutActorInput.schema.ts", "schemas/objects/AuditLogUncheckedUpdateWithoutActorInput.schema.ts", "schemas/objects/AuditLogUncheckedUpdateManyWithoutActorInput.schema.ts", "schemas/objects/AuditLogUpdateWithoutTargetInput.schema.ts", "schemas/objects/AuditLogUncheckedUpdateWithoutTargetInput.schema.ts", "schemas/objects/AuditLogUncheckedUpdateManyWithoutTargetInput.schema.ts", "schemas/objects/RefundRequestUpdateWithoutProcessedByUserInput.schema.ts", "schemas/objects/RefundRequestUncheckedUpdateWithoutProcessedByUserInput.schema.ts", "schemas/objects/RefundRequestUncheckedUpdateManyWithoutProcessedByUserInput.schema.ts", "schemas/objects/LicenseCreateManyPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionCreateManyPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventCreateManyPaymentIntentInput.schema.ts", "schemas/objects/LicenseUpdateWithoutPaymentIntentInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateWithoutPaymentIntentInput.schema.ts", "schemas/objects/LicenseUncheckedUpdateManyWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionUpdateWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedUpdateWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventUpdateWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventUncheckedUpdateWithoutPaymentIntentInput.schema.ts", "schemas/objects/WebhookEventUncheckedUpdateManyWithoutPaymentIntentInput.schema.ts", "schemas/objects/DeviceCreateManyLicenseInput.schema.ts", "schemas/objects/DeviceExpansionCreateManyLicenseInput.schema.ts", "schemas/objects/RefundRequestCreateManyLicenseInput.schema.ts", "schemas/objects/DeviceUpdateWithoutLicenseInput.schema.ts", "schemas/objects/DeviceUncheckedUpdateWithoutLicenseInput.schema.ts", "schemas/objects/DeviceUncheckedUpdateManyWithoutLicenseInput.schema.ts", "schemas/objects/DeviceExpansionUpdateWithoutLicenseInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedUpdateWithoutLicenseInput.schema.ts", "schemas/objects/DeviceExpansionUncheckedUpdateManyWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestUpdateWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestUncheckedUpdateWithoutLicenseInput.schema.ts", "schemas/objects/RefundRequestUncheckedUpdateManyWithoutLicenseInput.schema.ts", "schemas/objects/UserCountAggregateInput.schema.ts", "schemas/objects/UserMinAggregateInput.schema.ts", "schemas/objects/UserMaxAggregateInput.schema.ts", "schemas/objects/SessionCountAggregateInput.schema.ts", "schemas/objects/SessionMinAggregateInput.schema.ts", "schemas/objects/SessionMaxAggregateInput.schema.ts", "schemas/objects/AccountCountAggregateInput.schema.ts", "schemas/objects/AccountMinAggregateInput.schema.ts", "schemas/objects/AccountMaxAggregateInput.schema.ts", "schemas/objects/VerificationCountAggregateInput.schema.ts", "schemas/objects/VerificationMinAggregateInput.schema.ts", "schemas/objects/VerificationMaxAggregateInput.schema.ts", "schemas/objects/UserInvitationCountAggregateInput.schema.ts", "schemas/objects/UserInvitationMinAggregateInput.schema.ts", "schemas/objects/UserInvitationMaxAggregateInput.schema.ts", "schemas/objects/PaymentIntentCountAggregateInput.schema.ts", "schemas/objects/PaymentIntentAvgAggregateInput.schema.ts", "schemas/objects/PaymentIntentSumAggregateInput.schema.ts", "schemas/objects/PaymentIntentMinAggregateInput.schema.ts", "schemas/objects/PaymentIntentMaxAggregateInput.schema.ts", "schemas/objects/WebhookEventCountAggregateInput.schema.ts", "schemas/objects/WebhookEventAvgAggregateInput.schema.ts", "schemas/objects/WebhookEventSumAggregateInput.schema.ts", "schemas/objects/WebhookEventMinAggregateInput.schema.ts", "schemas/objects/WebhookEventMaxAggregateInput.schema.ts", "schemas/objects/LicenseCountAggregateInput.schema.ts", "schemas/objects/LicenseAvgAggregateInput.schema.ts", "schemas/objects/LicenseSumAggregateInput.schema.ts", "schemas/objects/LicenseMinAggregateInput.schema.ts", "schemas/objects/LicenseMaxAggregateInput.schema.ts", "schemas/objects/DeviceCountAggregateInput.schema.ts", "schemas/objects/DeviceMinAggregateInput.schema.ts", "schemas/objects/DeviceMaxAggregateInput.schema.ts", "schemas/objects/DeviceExpansionCountAggregateInput.schema.ts", "schemas/objects/DeviceExpansionAvgAggregateInput.schema.ts", "schemas/objects/DeviceExpansionSumAggregateInput.schema.ts", "schemas/objects/DeviceExpansionMinAggregateInput.schema.ts", "schemas/objects/DeviceExpansionMaxAggregateInput.schema.ts", "schemas/objects/RefundRequestCountAggregateInput.schema.ts", "schemas/objects/RefundRequestAvgAggregateInput.schema.ts", "schemas/objects/RefundRequestSumAggregateInput.schema.ts", "schemas/objects/RefundRequestMinAggregateInput.schema.ts", "schemas/objects/RefundRequestMaxAggregateInput.schema.ts", "schemas/objects/AuditLogCountAggregateInput.schema.ts", "schemas/objects/AuditLogMinAggregateInput.schema.ts", "schemas/objects/AuditLogMaxAggregateInput.schema.ts", "schemas/objects/RateLimitCountAggregateInput.schema.ts", "schemas/objects/RateLimitAvgAggregateInput.schema.ts", "schemas/objects/RateLimitSumAggregateInput.schema.ts", "schemas/objects/RateLimitMinAggregateInput.schema.ts", "schemas/objects/RateLimitMaxAggregateInput.schema.ts", "schemas/objects/UserCountOutputTypeSelect.schema.ts", "schemas/objects/PaymentIntentCountOutputTypeSelect.schema.ts", "schemas/objects/LicenseCountOutputTypeSelect.schema.ts", "schemas/objects/UserCountOutputTypeArgs.schema.ts", "schemas/objects/PaymentIntentCountOutputTypeArgs.schema.ts", "schemas/objects/LicenseCountOutputTypeArgs.schema.ts", "schemas/objects/UserSelect.schema.ts", "schemas/objects/SessionSelect.schema.ts", "schemas/objects/AccountSelect.schema.ts", "schemas/objects/VerificationSelect.schema.ts", "schemas/objects/UserInvitationSelect.schema.ts", "schemas/objects/PaymentIntentSelect.schema.ts", "schemas/objects/WebhookEventSelect.schema.ts", "schemas/objects/LicenseSelect.schema.ts", "schemas/objects/DeviceSelect.schema.ts", "schemas/objects/DeviceExpansionSelect.schema.ts", "schemas/objects/RefundRequestSelect.schema.ts", "schemas/objects/AuditLogSelect.schema.ts", "schemas/objects/RateLimitSelect.schema.ts", "schemas/objects/UserArgs.schema.ts", "schemas/objects/SessionArgs.schema.ts", "schemas/objects/AccountArgs.schema.ts", "schemas/objects/VerificationArgs.schema.ts", "schemas/objects/UserInvitationArgs.schema.ts", "schemas/objects/PaymentIntentArgs.schema.ts", "schemas/objects/WebhookEventArgs.schema.ts", "schemas/objects/LicenseArgs.schema.ts", "schemas/objects/DeviceArgs.schema.ts", "schemas/objects/DeviceExpansionArgs.schema.ts", "schemas/objects/RefundRequestArgs.schema.ts", "schemas/objects/AuditLogArgs.schema.ts", "schemas/objects/RateLimitArgs.schema.ts", "schemas/objects/UserInclude.schema.ts", "schemas/objects/SessionInclude.schema.ts", "schemas/objects/AccountInclude.schema.ts", "schemas/objects/UserInvitationInclude.schema.ts", "schemas/objects/PaymentIntentInclude.schema.ts", "schemas/objects/WebhookEventInclude.schema.ts", "schemas/objects/LicenseInclude.schema.ts", "schemas/objects/DeviceInclude.schema.ts", "schemas/objects/DeviceExpansionInclude.schema.ts", "schemas/objects/RefundRequestInclude.schema.ts", "schemas/objects/AuditLogInclude.schema.ts", "schemas/findUniqueUser.schema.ts", "schemas/findUniqueOrThrowUser.schema.ts", "schemas/findFirstUser.schema.ts", "schemas/findFirstOrThrowUser.schema.ts", "schemas/findManyUser.schema.ts", "schemas/countUser.schema.ts", "schemas/createOneUser.schema.ts", "schemas/createManyUser.schema.ts", "schemas/createManyAndReturnUser.schema.ts", "schemas/deleteOneUser.schema.ts", "schemas/deleteManyUser.schema.ts", "schemas/updateOneUser.schema.ts", "schemas/updateManyUser.schema.ts", "schemas/updateManyAndReturnUser.schema.ts", "schemas/upsertOneUser.schema.ts", "schemas/aggregateUser.schema.ts", "schemas/groupByUser.schema.ts", "schemas/findUniqueSession.schema.ts", "schemas/findUniqueOrThrowSession.schema.ts", "schemas/findFirstSession.schema.ts", "schemas/findFirstOrThrowSession.schema.ts", "schemas/findManySession.schema.ts", "schemas/countSession.schema.ts", "schemas/createOneSession.schema.ts", "schemas/createManySession.schema.ts", "schemas/createManyAndReturnSession.schema.ts", "schemas/deleteOneSession.schema.ts", "schemas/deleteManySession.schema.ts", "schemas/updateOneSession.schema.ts", "schemas/updateManySession.schema.ts", "schemas/updateManyAndReturnSession.schema.ts", "schemas/upsertOneSession.schema.ts", "schemas/aggregateSession.schema.ts", "schemas/groupBySession.schema.ts", "schemas/findUniqueAccount.schema.ts", "schemas/findUniqueOrThrowAccount.schema.ts", "schemas/findFirstAccount.schema.ts", "schemas/findFirstOrThrowAccount.schema.ts", "schemas/findManyAccount.schema.ts", "schemas/countAccount.schema.ts", "schemas/createOneAccount.schema.ts", "schemas/createManyAccount.schema.ts", "schemas/createManyAndReturnAccount.schema.ts", "schemas/deleteOneAccount.schema.ts", "schemas/deleteManyAccount.schema.ts", "schemas/updateOneAccount.schema.ts", "schemas/updateManyAccount.schema.ts", "schemas/updateManyAndReturnAccount.schema.ts", "schemas/upsertOneAccount.schema.ts", "schemas/aggregateAccount.schema.ts", "schemas/groupByAccount.schema.ts", "schemas/findUniqueVerification.schema.ts", "schemas/findUniqueOrThrowVerification.schema.ts", "schemas/findFirstVerification.schema.ts", "schemas/findFirstOrThrowVerification.schema.ts", "schemas/findManyVerification.schema.ts", "schemas/countVerification.schema.ts", "schemas/createOneVerification.schema.ts", "schemas/createManyVerification.schema.ts", "schemas/createManyAndReturnVerification.schema.ts", "schemas/deleteOneVerification.schema.ts", "schemas/deleteManyVerification.schema.ts", "schemas/updateOneVerification.schema.ts", "schemas/updateManyVerification.schema.ts", "schemas/updateManyAndReturnVerification.schema.ts", "schemas/upsertOneVerification.schema.ts", "schemas/aggregateVerification.schema.ts", "schemas/groupByVerification.schema.ts", "schemas/findUniqueUserInvitation.schema.ts", "schemas/findUniqueOrThrowUserInvitation.schema.ts", "schemas/findFirstUserInvitation.schema.ts", "schemas/findFirstOrThrowUserInvitation.schema.ts", "schemas/findManyUserInvitation.schema.ts", "schemas/countUserInvitation.schema.ts", "schemas/createOneUserInvitation.schema.ts", "schemas/createManyUserInvitation.schema.ts", "schemas/createManyAndReturnUserInvitation.schema.ts", "schemas/deleteOneUserInvitation.schema.ts", "schemas/deleteManyUserInvitation.schema.ts", "schemas/updateOneUserInvitation.schema.ts", "schemas/updateManyUserInvitation.schema.ts", "schemas/updateManyAndReturnUserInvitation.schema.ts", "schemas/upsertOneUserInvitation.schema.ts", "schemas/aggregateUserInvitation.schema.ts", "schemas/groupByUserInvitation.schema.ts", "schemas/findUniquePaymentIntent.schema.ts", "schemas/findUniqueOrThrowPaymentIntent.schema.ts", "schemas/findFirstPaymentIntent.schema.ts", "schemas/findFirstOrThrowPaymentIntent.schema.ts", "schemas/findManyPaymentIntent.schema.ts", "schemas/countPaymentIntent.schema.ts", "schemas/createOnePaymentIntent.schema.ts", "schemas/createManyPaymentIntent.schema.ts", "schemas/createManyAndReturnPaymentIntent.schema.ts", "schemas/deleteOnePaymentIntent.schema.ts", "schemas/deleteManyPaymentIntent.schema.ts", "schemas/updateOnePaymentIntent.schema.ts", "schemas/updateManyPaymentIntent.schema.ts", "schemas/updateManyAndReturnPaymentIntent.schema.ts", "schemas/upsertOnePaymentIntent.schema.ts", "schemas/aggregatePaymentIntent.schema.ts", "schemas/groupByPaymentIntent.schema.ts", "schemas/findUniqueWebhookEvent.schema.ts", "schemas/findUniqueOrThrowWebhookEvent.schema.ts", "schemas/findFirstWebhookEvent.schema.ts", "schemas/findFirstOrThrowWebhookEvent.schema.ts", "schemas/findManyWebhookEvent.schema.ts", "schemas/countWebhookEvent.schema.ts", "schemas/createOneWebhookEvent.schema.ts", "schemas/createManyWebhookEvent.schema.ts", "schemas/createManyAndReturnWebhookEvent.schema.ts", "schemas/deleteOneWebhookEvent.schema.ts", "schemas/deleteManyWebhookEvent.schema.ts", "schemas/updateOneWebhookEvent.schema.ts", "schemas/updateManyWebhookEvent.schema.ts", "schemas/updateManyAndReturnWebhookEvent.schema.ts", "schemas/upsertOneWebhookEvent.schema.ts", "schemas/aggregateWebhookEvent.schema.ts", "schemas/groupByWebhookEvent.schema.ts", "schemas/findUniqueLicense.schema.ts", "schemas/findUniqueOrThrowLicense.schema.ts", "schemas/findFirstLicense.schema.ts", "schemas/findFirstOrThrowLicense.schema.ts", "schemas/findManyLicense.schema.ts", "schemas/countLicense.schema.ts", "schemas/createOneLicense.schema.ts", "schemas/createManyLicense.schema.ts", "schemas/createManyAndReturnLicense.schema.ts", "schemas/deleteOneLicense.schema.ts", "schemas/deleteManyLicense.schema.ts", "schemas/updateOneLicense.schema.ts", "schemas/updateManyLicense.schema.ts", "schemas/updateManyAndReturnLicense.schema.ts", "schemas/upsertOneLicense.schema.ts", "schemas/aggregateLicense.schema.ts", "schemas/groupByLicense.schema.ts", "schemas/findUniqueDevice.schema.ts", "schemas/findUniqueOrThrowDevice.schema.ts", "schemas/findFirstDevice.schema.ts", "schemas/findFirstOrThrowDevice.schema.ts", "schemas/findManyDevice.schema.ts", "schemas/countDevice.schema.ts", "schemas/createOneDevice.schema.ts", "schemas/createManyDevice.schema.ts", "schemas/createManyAndReturnDevice.schema.ts", "schemas/deleteOneDevice.schema.ts", "schemas/deleteManyDevice.schema.ts", "schemas/updateOneDevice.schema.ts", "schemas/updateManyDevice.schema.ts", "schemas/updateManyAndReturnDevice.schema.ts", "schemas/upsertOneDevice.schema.ts", "schemas/aggregateDevice.schema.ts", "schemas/groupByDevice.schema.ts", "schemas/findUniqueDeviceExpansion.schema.ts", "schemas/findUniqueOrThrowDeviceExpansion.schema.ts", "schemas/findFirstDeviceExpansion.schema.ts", "schemas/findFirstOrThrowDeviceExpansion.schema.ts", "schemas/findManyDeviceExpansion.schema.ts", "schemas/countDeviceExpansion.schema.ts", "schemas/createOneDeviceExpansion.schema.ts", "schemas/createManyDeviceExpansion.schema.ts", "schemas/createManyAndReturnDeviceExpansion.schema.ts", "schemas/deleteOneDeviceExpansion.schema.ts", "schemas/deleteManyDeviceExpansion.schema.ts", "schemas/updateOneDeviceExpansion.schema.ts", "schemas/updateManyDeviceExpansion.schema.ts", "schemas/updateManyAndReturnDeviceExpansion.schema.ts", "schemas/upsertOneDeviceExpansion.schema.ts", "schemas/aggregateDeviceExpansion.schema.ts", "schemas/groupByDeviceExpansion.schema.ts", "schemas/findUniqueRefundRequest.schema.ts", "schemas/findUniqueOrThrowRefundRequest.schema.ts", "schemas/findFirstRefundRequest.schema.ts", "schemas/findFirstOrThrowRefundRequest.schema.ts", "schemas/findManyRefundRequest.schema.ts", "schemas/countRefundRequest.schema.ts", "schemas/createOneRefundRequest.schema.ts", "schemas/createManyRefundRequest.schema.ts", "schemas/createManyAndReturnRefundRequest.schema.ts", "schemas/deleteOneRefundRequest.schema.ts", "schemas/deleteManyRefundRequest.schema.ts", "schemas/updateOneRefundRequest.schema.ts", "schemas/updateManyRefundRequest.schema.ts", "schemas/updateManyAndReturnRefundRequest.schema.ts", "schemas/upsertOneRefundRequest.schema.ts", "schemas/aggregateRefundRequest.schema.ts", "schemas/groupByRefundRequest.schema.ts", "schemas/findUniqueAuditLog.schema.ts", "schemas/findUniqueOrThrowAuditLog.schema.ts", "schemas/findFirstAuditLog.schema.ts", "schemas/findFirstOrThrowAuditLog.schema.ts", "schemas/findManyAuditLog.schema.ts", "schemas/countAuditLog.schema.ts", "schemas/createOneAuditLog.schema.ts", "schemas/createManyAuditLog.schema.ts", "schemas/createManyAndReturnAuditLog.schema.ts", "schemas/deleteOneAuditLog.schema.ts", "schemas/deleteManyAuditLog.schema.ts", "schemas/updateOneAuditLog.schema.ts", "schemas/updateManyAuditLog.schema.ts", "schemas/updateManyAndReturnAuditLog.schema.ts", "schemas/upsertOneAuditLog.schema.ts", "schemas/aggregateAuditLog.schema.ts", "schemas/groupByAuditLog.schema.ts", "schemas/findUniqueRateLimit.schema.ts", "schemas/findUniqueOrThrowRateLimit.schema.ts", "schemas/findFirstRateLimit.schema.ts", "schemas/findFirstOrThrowRateLimit.schema.ts", "schemas/findManyRateLimit.schema.ts", "schemas/countRateLimit.schema.ts", "schemas/createOneRateLimit.schema.ts", "schemas/createManyRateLimit.schema.ts", "schemas/createManyAndReturnRateLimit.schema.ts", "schemas/deleteOneRateLimit.schema.ts", "schemas/deleteManyRateLimit.schema.ts", "schemas/updateOneRateLimit.schema.ts", "schemas/updateManyRateLimit.schema.ts", "schemas/updateManyAndReturnRateLimit.schema.ts", "schemas/upsertOneRateLimit.schema.ts", "schemas/aggregateRateLimit.schema.ts", "schemas/groupByRateLimit.schema.ts", "schemas/results/UserFindUniqueResult.schema.ts", "schemas/results/UserFindFirstResult.schema.ts", "schemas/results/UserFindManyResult.schema.ts", "schemas/results/UserCreateResult.schema.ts", "schemas/results/UserCreateManyResult.schema.ts", "schemas/results/UserUpdateResult.schema.ts", "schemas/results/UserUpdateManyResult.schema.ts", "schemas/results/UserUpsertResult.schema.ts", "schemas/results/UserDeleteResult.schema.ts", "schemas/results/UserDeleteManyResult.schema.ts", "schemas/results/UserAggregateResult.schema.ts", "schemas/results/UserGroupByResult.schema.ts", "schemas/results/UserCountResult.schema.ts", "schemas/results/SessionFindUniqueResult.schema.ts", "schemas/results/SessionFindFirstResult.schema.ts", "schemas/results/SessionFindManyResult.schema.ts", "schemas/results/SessionCreateResult.schema.ts", "schemas/results/SessionCreateManyResult.schema.ts", "schemas/results/SessionUpdateResult.schema.ts", "schemas/results/SessionUpdateManyResult.schema.ts", "schemas/results/SessionUpsertResult.schema.ts", "schemas/results/SessionDeleteResult.schema.ts", "schemas/results/SessionDeleteManyResult.schema.ts", "schemas/results/SessionAggregateResult.schema.ts", "schemas/results/SessionGroupByResult.schema.ts", "schemas/results/SessionCountResult.schema.ts", "schemas/results/AccountFindUniqueResult.schema.ts", "schemas/results/AccountFindFirstResult.schema.ts", "schemas/results/AccountFindManyResult.schema.ts", "schemas/results/AccountCreateResult.schema.ts", "schemas/results/AccountCreateManyResult.schema.ts", "schemas/results/AccountUpdateResult.schema.ts", "schemas/results/AccountUpdateManyResult.schema.ts", "schemas/results/AccountUpsertResult.schema.ts", "schemas/results/AccountDeleteResult.schema.ts", "schemas/results/AccountDeleteManyResult.schema.ts", "schemas/results/AccountAggregateResult.schema.ts", "schemas/results/AccountGroupByResult.schema.ts", "schemas/results/AccountCountResult.schema.ts", "schemas/results/VerificationFindUniqueResult.schema.ts", "schemas/results/VerificationFindFirstResult.schema.ts", "schemas/results/VerificationFindManyResult.schema.ts", "schemas/results/VerificationCreateResult.schema.ts", "schemas/results/VerificationCreateManyResult.schema.ts", "schemas/results/VerificationUpdateResult.schema.ts", "schemas/results/VerificationUpdateManyResult.schema.ts", "schemas/results/VerificationUpsertResult.schema.ts", "schemas/results/VerificationDeleteResult.schema.ts", "schemas/results/VerificationDeleteManyResult.schema.ts", "schemas/results/VerificationAggregateResult.schema.ts", "schemas/results/VerificationGroupByResult.schema.ts", "schemas/results/VerificationCountResult.schema.ts", "schemas/results/UserInvitationFindUniqueResult.schema.ts", "schemas/results/UserInvitationFindFirstResult.schema.ts", "schemas/results/UserInvitationFindManyResult.schema.ts", "schemas/results/UserInvitationCreateResult.schema.ts", "schemas/results/UserInvitationCreateManyResult.schema.ts", "schemas/results/UserInvitationUpdateResult.schema.ts", "schemas/results/UserInvitationUpdateManyResult.schema.ts", "schemas/results/UserInvitationUpsertResult.schema.ts", "schemas/results/UserInvitationDeleteResult.schema.ts", "schemas/results/UserInvitationDeleteManyResult.schema.ts", "schemas/results/UserInvitationAggregateResult.schema.ts", "schemas/results/UserInvitationGroupByResult.schema.ts", "schemas/results/UserInvitationCountResult.schema.ts", "schemas/results/PaymentIntentFindUniqueResult.schema.ts", "schemas/results/PaymentIntentFindFirstResult.schema.ts", "schemas/results/PaymentIntentFindManyResult.schema.ts", "schemas/results/PaymentIntentCreateResult.schema.ts", "schemas/results/PaymentIntentCreateManyResult.schema.ts", "schemas/results/PaymentIntentUpdateResult.schema.ts", "schemas/results/PaymentIntentUpdateManyResult.schema.ts", "schemas/results/PaymentIntentUpsertResult.schema.ts", "schemas/results/PaymentIntentDeleteResult.schema.ts", "schemas/results/PaymentIntentDeleteManyResult.schema.ts", "schemas/results/PaymentIntentAggregateResult.schema.ts", "schemas/results/PaymentIntentGroupByResult.schema.ts", "schemas/results/PaymentIntentCountResult.schema.ts", "schemas/results/WebhookEventFindUniqueResult.schema.ts", "schemas/results/WebhookEventFindFirstResult.schema.ts", "schemas/results/WebhookEventFindManyResult.schema.ts", "schemas/results/WebhookEventCreateResult.schema.ts", "schemas/results/WebhookEventCreateManyResult.schema.ts", "schemas/results/WebhookEventUpdateResult.schema.ts", "schemas/results/WebhookEventUpdateManyResult.schema.ts", "schemas/results/WebhookEventUpsertResult.schema.ts", "schemas/results/WebhookEventDeleteResult.schema.ts", "schemas/results/WebhookEventDeleteManyResult.schema.ts", "schemas/results/WebhookEventAggregateResult.schema.ts", "schemas/results/WebhookEventGroupByResult.schema.ts", "schemas/results/WebhookEventCountResult.schema.ts", "schemas/results/LicenseFindUniqueResult.schema.ts", "schemas/results/LicenseFindFirstResult.schema.ts", "schemas/results/LicenseFindManyResult.schema.ts", "schemas/results/LicenseCreateResult.schema.ts", "schemas/results/LicenseCreateManyResult.schema.ts", "schemas/results/LicenseUpdateResult.schema.ts", "schemas/results/LicenseUpdateManyResult.schema.ts", "schemas/results/LicenseUpsertResult.schema.ts", "schemas/results/LicenseDeleteResult.schema.ts", "schemas/results/LicenseDeleteManyResult.schema.ts", "schemas/results/LicenseAggregateResult.schema.ts", "schemas/results/LicenseGroupByResult.schema.ts", "schemas/results/LicenseCountResult.schema.ts", "schemas/results/DeviceFindUniqueResult.schema.ts", "schemas/results/DeviceFindFirstResult.schema.ts", "schemas/results/DeviceFindManyResult.schema.ts", "schemas/results/DeviceCreateResult.schema.ts", "schemas/results/DeviceCreateManyResult.schema.ts", "schemas/results/DeviceUpdateResult.schema.ts", "schemas/results/DeviceUpdateManyResult.schema.ts", "schemas/results/DeviceUpsertResult.schema.ts", "schemas/results/DeviceDeleteResult.schema.ts", "schemas/results/DeviceDeleteManyResult.schema.ts", "schemas/results/DeviceAggregateResult.schema.ts", "schemas/results/DeviceGroupByResult.schema.ts", "schemas/results/DeviceCountResult.schema.ts", "schemas/results/DeviceExpansionFindUniqueResult.schema.ts", "schemas/results/DeviceExpansionFindFirstResult.schema.ts", "schemas/results/DeviceExpansionFindManyResult.schema.ts", "schemas/results/DeviceExpansionCreateResult.schema.ts", "schemas/results/DeviceExpansionCreateManyResult.schema.ts", "schemas/results/DeviceExpansionUpdateResult.schema.ts", "schemas/results/DeviceExpansionUpdateManyResult.schema.ts", "schemas/results/DeviceExpansionUpsertResult.schema.ts", "schemas/results/DeviceExpansionDeleteResult.schema.ts", "schemas/results/DeviceExpansionDeleteManyResult.schema.ts", "schemas/results/DeviceExpansionAggregateResult.schema.ts", "schemas/results/DeviceExpansionGroupByResult.schema.ts", "schemas/results/DeviceExpansionCountResult.schema.ts", "schemas/results/RefundRequestFindUniqueResult.schema.ts", "schemas/results/RefundRequestFindFirstResult.schema.ts", "schemas/results/RefundRequestFindManyResult.schema.ts", "schemas/results/RefundRequestCreateResult.schema.ts", "schemas/results/RefundRequestCreateManyResult.schema.ts", "schemas/results/RefundRequestUpdateResult.schema.ts", "schemas/results/RefundRequestUpdateManyResult.schema.ts", "schemas/results/RefundRequestUpsertResult.schema.ts", "schemas/results/RefundRequestDeleteResult.schema.ts", "schemas/results/RefundRequestDeleteManyResult.schema.ts", "schemas/results/RefundRequestAggregateResult.schema.ts", "schemas/results/RefundRequestGroupByResult.schema.ts", "schemas/results/RefundRequestCountResult.schema.ts", "schemas/results/AuditLogFindUniqueResult.schema.ts", "schemas/results/AuditLogFindFirstResult.schema.ts", "schemas/results/AuditLogFindManyResult.schema.ts", "schemas/results/AuditLogCreateResult.schema.ts", "schemas/results/AuditLogCreateManyResult.schema.ts", "schemas/results/AuditLogUpdateResult.schema.ts", "schemas/results/AuditLogUpdateManyResult.schema.ts", "schemas/results/AuditLogUpsertResult.schema.ts", "schemas/results/AuditLogDeleteResult.schema.ts", "schemas/results/AuditLogDeleteManyResult.schema.ts", "schemas/results/AuditLogAggregateResult.schema.ts", "schemas/results/AuditLogGroupByResult.schema.ts", "schemas/results/AuditLogCountResult.schema.ts", "schemas/results/RateLimitFindUniqueResult.schema.ts", "schemas/results/RateLimitFindFirstResult.schema.ts", "schemas/results/RateLimitFindManyResult.schema.ts", "schemas/results/RateLimitCreateResult.schema.ts", "schemas/results/RateLimitCreateManyResult.schema.ts", "schemas/results/RateLimitUpdateResult.schema.ts", "schemas/results/RateLimitUpdateManyResult.schema.ts", "schemas/results/RateLimitUpsertResult.schema.ts", "schemas/results/RateLimitDeleteResult.schema.ts", "schemas/results/RateLimitDeleteManyResult.schema.ts", "schemas/results/RateLimitAggregateResult.schema.ts", "schemas/results/RateLimitGroupByResult.schema.ts", "schemas/results/RateLimitCountResult.schema.ts", "schemas/results/index.ts", "schemas/index.ts", "schemas/variants/pure/User.pure.ts", "schemas/variants/pure/Session.pure.ts", "schemas/variants/pure/Account.pure.ts", "schemas/variants/pure/Verification.pure.ts", "schemas/variants/pure/UserInvitation.pure.ts", "schemas/variants/pure/PaymentIntent.pure.ts", "schemas/variants/pure/WebhookEvent.pure.ts", "schemas/variants/pure/License.pure.ts", "schemas/variants/pure/Device.pure.ts", "schemas/variants/pure/DeviceExpansion.pure.ts", "schemas/variants/pure/RefundRequest.pure.ts", "schemas/variants/pure/AuditLog.pure.ts", "schemas/variants/pure/RateLimit.pure.ts", "schemas/variants/pure/index.ts", "schemas/variants/input/User.input.ts", "schemas/variants/input/Session.input.ts", "schemas/variants/input/Account.input.ts", "schemas/variants/input/Verification.input.ts", "schemas/variants/input/UserInvitation.input.ts", "schemas/variants/input/PaymentIntent.input.ts", "schemas/variants/input/WebhookEvent.input.ts", "schemas/variants/input/License.input.ts", "schemas/variants/input/Device.input.ts", "schemas/variants/input/DeviceExpansion.input.ts", "schemas/variants/input/RefundRequest.input.ts", "schemas/variants/input/AuditLog.input.ts", "schemas/variants/input/RateLimit.input.ts", "schemas/variants/input/index.ts", "schemas/variants/result/User.result.ts", "schemas/variants/result/Session.result.ts", "schemas/variants/result/Account.result.ts", "schemas/variants/result/Verification.result.ts", "schemas/variants/result/UserInvitation.result.ts", "schemas/variants/result/PaymentIntent.result.ts", "schemas/variants/result/WebhookEvent.result.ts", "schemas/variants/result/License.result.ts", "schemas/variants/result/Device.result.ts", "schemas/variants/result/DeviceExpansion.result.ts", "schemas/variants/result/RefundRequest.result.ts", "schemas/variants/result/AuditLog.result.ts", "schemas/variants/result/RateLimit.result.ts", "schemas/variants/result/index.ts", "schemas/variants/index.ts"], "directories": ["schemas/enums", "schemas/objects", "schemas", "schemas/results", "schemas/variants/pure", "schemas/variants/input", "schemas/variants/result", "schemas/variants"], "singleFileMode": false}