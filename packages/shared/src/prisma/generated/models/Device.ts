
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Device` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Device
 * 
 */
export type DeviceModel = runtime.Types.Result.DefaultSelection<Prisma.$DevicePayload>

export type AggregateDevice = {
  _count: DeviceCountAggregateOutputType | null
  _min: DeviceMinAggregateOutputType | null
  _max: DeviceMaxAggregateOutputType | null
}

export type DeviceMinAggregateOutputType = {
  id: string | null
  licenseId: string | null
  deviceHash: string | null
  salt: string | null
  status: $Enums.DeviceStatus | null
  firstSeen: Date | null
  lastSeen: Date | null
  removedAt: Date | null
  appVersion: string | null
  deviceName: string | null
  deviceType: string | null
  deviceModel: string | null
  operatingSystem: string | null
  architecture: string | null
  screenResolution: string | null
  totalMemory: string | null
  userNickname: string | null
  location: string | null
  notes: string | null
}

export type DeviceMaxAggregateOutputType = {
  id: string | null
  licenseId: string | null
  deviceHash: string | null
  salt: string | null
  status: $Enums.DeviceStatus | null
  firstSeen: Date | null
  lastSeen: Date | null
  removedAt: Date | null
  appVersion: string | null
  deviceName: string | null
  deviceType: string | null
  deviceModel: string | null
  operatingSystem: string | null
  architecture: string | null
  screenResolution: string | null
  totalMemory: string | null
  userNickname: string | null
  location: string | null
  notes: string | null
}

export type DeviceCountAggregateOutputType = {
  id: number
  licenseId: number
  deviceHash: number
  salt: number
  status: number
  firstSeen: number
  lastSeen: number
  removedAt: number
  appVersion: number
  deviceName: number
  deviceType: number
  deviceModel: number
  operatingSystem: number
  architecture: number
  screenResolution: number
  totalMemory: number
  userNickname: number
  location: number
  notes: number
  _all: number
}


export type DeviceMinAggregateInputType = {
  id?: true
  licenseId?: true
  deviceHash?: true
  salt?: true
  status?: true
  firstSeen?: true
  lastSeen?: true
  removedAt?: true
  appVersion?: true
  deviceName?: true
  deviceType?: true
  deviceModel?: true
  operatingSystem?: true
  architecture?: true
  screenResolution?: true
  totalMemory?: true
  userNickname?: true
  location?: true
  notes?: true
}

export type DeviceMaxAggregateInputType = {
  id?: true
  licenseId?: true
  deviceHash?: true
  salt?: true
  status?: true
  firstSeen?: true
  lastSeen?: true
  removedAt?: true
  appVersion?: true
  deviceName?: true
  deviceType?: true
  deviceModel?: true
  operatingSystem?: true
  architecture?: true
  screenResolution?: true
  totalMemory?: true
  userNickname?: true
  location?: true
  notes?: true
}

export type DeviceCountAggregateInputType = {
  id?: true
  licenseId?: true
  deviceHash?: true
  salt?: true
  status?: true
  firstSeen?: true
  lastSeen?: true
  removedAt?: true
  appVersion?: true
  deviceName?: true
  deviceType?: true
  deviceModel?: true
  operatingSystem?: true
  architecture?: true
  screenResolution?: true
  totalMemory?: true
  userNickname?: true
  location?: true
  notes?: true
  _all?: true
}

export type DeviceAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Device to aggregate.
   */
  where?: Prisma.DeviceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Devices to fetch.
   */
  orderBy?: Prisma.DeviceOrderByWithRelationInput | Prisma.DeviceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.DeviceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Devices from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Devices.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Devices
  **/
  _count?: true | DeviceCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: DeviceMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: DeviceMaxAggregateInputType
}

export type GetDeviceAggregateType<T extends DeviceAggregateArgs> = {
      [P in keyof T & keyof AggregateDevice]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateDevice[P]>
    : Prisma.GetScalarType<T[P], AggregateDevice[P]>
}




export type DeviceGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.DeviceWhereInput
  orderBy?: Prisma.DeviceOrderByWithAggregationInput | Prisma.DeviceOrderByWithAggregationInput[]
  by: Prisma.DeviceScalarFieldEnum[] | Prisma.DeviceScalarFieldEnum
  having?: Prisma.DeviceScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: DeviceCountAggregateInputType | true
  _min?: DeviceMinAggregateInputType
  _max?: DeviceMaxAggregateInputType
}

export type DeviceGroupByOutputType = {
  id: string
  licenseId: string
  deviceHash: string
  salt: string
  status: $Enums.DeviceStatus
  firstSeen: Date
  lastSeen: Date
  removedAt: Date | null
  appVersion: string | null
  deviceName: string | null
  deviceType: string | null
  deviceModel: string | null
  operatingSystem: string | null
  architecture: string | null
  screenResolution: string | null
  totalMemory: string | null
  userNickname: string | null
  location: string | null
  notes: string | null
  _count: DeviceCountAggregateOutputType | null
  _min: DeviceMinAggregateOutputType | null
  _max: DeviceMaxAggregateOutputType | null
}

type GetDeviceGroupByPayload<T extends DeviceGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<DeviceGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof DeviceGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], DeviceGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], DeviceGroupByOutputType[P]>
      }
    >
  >



export type DeviceWhereInput = {
  AND?: Prisma.DeviceWhereInput | Prisma.DeviceWhereInput[]
  OR?: Prisma.DeviceWhereInput[]
  NOT?: Prisma.DeviceWhereInput | Prisma.DeviceWhereInput[]
  id?: Prisma.StringFilter<"Device"> | string
  licenseId?: Prisma.StringFilter<"Device"> | string
  deviceHash?: Prisma.StringFilter<"Device"> | string
  salt?: Prisma.StringFilter<"Device"> | string
  status?: Prisma.EnumDeviceStatusFilter<"Device"> | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeFilter<"Device"> | Date | string
  lastSeen?: Prisma.DateTimeFilter<"Device"> | Date | string
  removedAt?: Prisma.DateTimeNullableFilter<"Device"> | Date | string | null
  appVersion?: Prisma.StringNullableFilter<"Device"> | string | null
  deviceName?: Prisma.StringNullableFilter<"Device"> | string | null
  deviceType?: Prisma.StringNullableFilter<"Device"> | string | null
  deviceModel?: Prisma.StringNullableFilter<"Device"> | string | null
  operatingSystem?: Prisma.StringNullableFilter<"Device"> | string | null
  architecture?: Prisma.StringNullableFilter<"Device"> | string | null
  screenResolution?: Prisma.StringNullableFilter<"Device"> | string | null
  totalMemory?: Prisma.StringNullableFilter<"Device"> | string | null
  userNickname?: Prisma.StringNullableFilter<"Device"> | string | null
  location?: Prisma.StringNullableFilter<"Device"> | string | null
  notes?: Prisma.StringNullableFilter<"Device"> | string | null
  license?: Prisma.XOR<Prisma.LicenseScalarRelationFilter, Prisma.LicenseWhereInput>
}

export type DeviceOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  licenseId?: Prisma.SortOrder
  deviceHash?: Prisma.SortOrder
  salt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  firstSeen?: Prisma.SortOrder
  lastSeen?: Prisma.SortOrder
  removedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  appVersion?: Prisma.SortOrderInput | Prisma.SortOrder
  deviceName?: Prisma.SortOrderInput | Prisma.SortOrder
  deviceType?: Prisma.SortOrderInput | Prisma.SortOrder
  deviceModel?: Prisma.SortOrderInput | Prisma.SortOrder
  operatingSystem?: Prisma.SortOrderInput | Prisma.SortOrder
  architecture?: Prisma.SortOrderInput | Prisma.SortOrder
  screenResolution?: Prisma.SortOrderInput | Prisma.SortOrder
  totalMemory?: Prisma.SortOrderInput | Prisma.SortOrder
  userNickname?: Prisma.SortOrderInput | Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  notes?: Prisma.SortOrderInput | Prisma.SortOrder
  license?: Prisma.LicenseOrderByWithRelationInput
}

export type DeviceWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  licenseId_deviceHash?: Prisma.DeviceLicenseIdDeviceHashCompoundUniqueInput
  AND?: Prisma.DeviceWhereInput | Prisma.DeviceWhereInput[]
  OR?: Prisma.DeviceWhereInput[]
  NOT?: Prisma.DeviceWhereInput | Prisma.DeviceWhereInput[]
  licenseId?: Prisma.StringFilter<"Device"> | string
  deviceHash?: Prisma.StringFilter<"Device"> | string
  salt?: Prisma.StringFilter<"Device"> | string
  status?: Prisma.EnumDeviceStatusFilter<"Device"> | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeFilter<"Device"> | Date | string
  lastSeen?: Prisma.DateTimeFilter<"Device"> | Date | string
  removedAt?: Prisma.DateTimeNullableFilter<"Device"> | Date | string | null
  appVersion?: Prisma.StringNullableFilter<"Device"> | string | null
  deviceName?: Prisma.StringNullableFilter<"Device"> | string | null
  deviceType?: Prisma.StringNullableFilter<"Device"> | string | null
  deviceModel?: Prisma.StringNullableFilter<"Device"> | string | null
  operatingSystem?: Prisma.StringNullableFilter<"Device"> | string | null
  architecture?: Prisma.StringNullableFilter<"Device"> | string | null
  screenResolution?: Prisma.StringNullableFilter<"Device"> | string | null
  totalMemory?: Prisma.StringNullableFilter<"Device"> | string | null
  userNickname?: Prisma.StringNullableFilter<"Device"> | string | null
  location?: Prisma.StringNullableFilter<"Device"> | string | null
  notes?: Prisma.StringNullableFilter<"Device"> | string | null
  license?: Prisma.XOR<Prisma.LicenseScalarRelationFilter, Prisma.LicenseWhereInput>
}, "id" | "licenseId_deviceHash">

export type DeviceOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  licenseId?: Prisma.SortOrder
  deviceHash?: Prisma.SortOrder
  salt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  firstSeen?: Prisma.SortOrder
  lastSeen?: Prisma.SortOrder
  removedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  appVersion?: Prisma.SortOrderInput | Prisma.SortOrder
  deviceName?: Prisma.SortOrderInput | Prisma.SortOrder
  deviceType?: Prisma.SortOrderInput | Prisma.SortOrder
  deviceModel?: Prisma.SortOrderInput | Prisma.SortOrder
  operatingSystem?: Prisma.SortOrderInput | Prisma.SortOrder
  architecture?: Prisma.SortOrderInput | Prisma.SortOrder
  screenResolution?: Prisma.SortOrderInput | Prisma.SortOrder
  totalMemory?: Prisma.SortOrderInput | Prisma.SortOrder
  userNickname?: Prisma.SortOrderInput | Prisma.SortOrder
  location?: Prisma.SortOrderInput | Prisma.SortOrder
  notes?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.DeviceCountOrderByAggregateInput
  _max?: Prisma.DeviceMaxOrderByAggregateInput
  _min?: Prisma.DeviceMinOrderByAggregateInput
}

export type DeviceScalarWhereWithAggregatesInput = {
  AND?: Prisma.DeviceScalarWhereWithAggregatesInput | Prisma.DeviceScalarWhereWithAggregatesInput[]
  OR?: Prisma.DeviceScalarWhereWithAggregatesInput[]
  NOT?: Prisma.DeviceScalarWhereWithAggregatesInput | Prisma.DeviceScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Device"> | string
  licenseId?: Prisma.StringWithAggregatesFilter<"Device"> | string
  deviceHash?: Prisma.StringWithAggregatesFilter<"Device"> | string
  salt?: Prisma.StringWithAggregatesFilter<"Device"> | string
  status?: Prisma.EnumDeviceStatusWithAggregatesFilter<"Device"> | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeWithAggregatesFilter<"Device"> | Date | string
  lastSeen?: Prisma.DateTimeWithAggregatesFilter<"Device"> | Date | string
  removedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Device"> | Date | string | null
  appVersion?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
  deviceName?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
  deviceType?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
  deviceModel?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
  operatingSystem?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
  architecture?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
  screenResolution?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
  totalMemory?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
  userNickname?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
  location?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
  notes?: Prisma.StringNullableWithAggregatesFilter<"Device"> | string | null
}

export type DeviceCreateInput = {
  id?: string
  deviceHash: string
  salt: string
  status?: $Enums.DeviceStatus
  firstSeen?: Date | string
  lastSeen?: Date | string
  removedAt?: Date | string | null
  appVersion?: string | null
  deviceName?: string | null
  deviceType?: string | null
  deviceModel?: string | null
  operatingSystem?: string | null
  architecture?: string | null
  screenResolution?: string | null
  totalMemory?: string | null
  userNickname?: string | null
  location?: string | null
  notes?: string | null
  license: Prisma.LicenseCreateNestedOneWithoutDevicesInput
}

export type DeviceUncheckedCreateInput = {
  id?: string
  licenseId: string
  deviceHash: string
  salt: string
  status?: $Enums.DeviceStatus
  firstSeen?: Date | string
  lastSeen?: Date | string
  removedAt?: Date | string | null
  appVersion?: string | null
  deviceName?: string | null
  deviceType?: string | null
  deviceModel?: string | null
  operatingSystem?: string | null
  architecture?: string | null
  screenResolution?: string | null
  totalMemory?: string | null
  userNickname?: string | null
  location?: string | null
  notes?: string | null
}

export type DeviceUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  deviceHash?: Prisma.StringFieldUpdateOperationsInput | string
  salt?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumDeviceStatusFieldUpdateOperationsInput | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  removedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  appVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceModel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  operatingSystem?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  architecture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  screenResolution?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalMemory?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userNickname?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  notes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  license?: Prisma.LicenseUpdateOneRequiredWithoutDevicesNestedInput
}

export type DeviceUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseId?: Prisma.StringFieldUpdateOperationsInput | string
  deviceHash?: Prisma.StringFieldUpdateOperationsInput | string
  salt?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumDeviceStatusFieldUpdateOperationsInput | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  removedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  appVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceModel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  operatingSystem?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  architecture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  screenResolution?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalMemory?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userNickname?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  notes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type DeviceCreateManyInput = {
  id?: string
  licenseId: string
  deviceHash: string
  salt: string
  status?: $Enums.DeviceStatus
  firstSeen?: Date | string
  lastSeen?: Date | string
  removedAt?: Date | string | null
  appVersion?: string | null
  deviceName?: string | null
  deviceType?: string | null
  deviceModel?: string | null
  operatingSystem?: string | null
  architecture?: string | null
  screenResolution?: string | null
  totalMemory?: string | null
  userNickname?: string | null
  location?: string | null
  notes?: string | null
}

export type DeviceUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  deviceHash?: Prisma.StringFieldUpdateOperationsInput | string
  salt?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumDeviceStatusFieldUpdateOperationsInput | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  removedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  appVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceModel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  operatingSystem?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  architecture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  screenResolution?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalMemory?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userNickname?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  notes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type DeviceUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseId?: Prisma.StringFieldUpdateOperationsInput | string
  deviceHash?: Prisma.StringFieldUpdateOperationsInput | string
  salt?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumDeviceStatusFieldUpdateOperationsInput | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  removedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  appVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceModel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  operatingSystem?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  architecture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  screenResolution?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalMemory?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userNickname?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  notes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type DeviceListRelationFilter = {
  every?: Prisma.DeviceWhereInput
  some?: Prisma.DeviceWhereInput
  none?: Prisma.DeviceWhereInput
}

export type DeviceOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type DeviceLicenseIdDeviceHashCompoundUniqueInput = {
  licenseId: string
  deviceHash: string
}

export type DeviceCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  licenseId?: Prisma.SortOrder
  deviceHash?: Prisma.SortOrder
  salt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  firstSeen?: Prisma.SortOrder
  lastSeen?: Prisma.SortOrder
  removedAt?: Prisma.SortOrder
  appVersion?: Prisma.SortOrder
  deviceName?: Prisma.SortOrder
  deviceType?: Prisma.SortOrder
  deviceModel?: Prisma.SortOrder
  operatingSystem?: Prisma.SortOrder
  architecture?: Prisma.SortOrder
  screenResolution?: Prisma.SortOrder
  totalMemory?: Prisma.SortOrder
  userNickname?: Prisma.SortOrder
  location?: Prisma.SortOrder
  notes?: Prisma.SortOrder
}

export type DeviceMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  licenseId?: Prisma.SortOrder
  deviceHash?: Prisma.SortOrder
  salt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  firstSeen?: Prisma.SortOrder
  lastSeen?: Prisma.SortOrder
  removedAt?: Prisma.SortOrder
  appVersion?: Prisma.SortOrder
  deviceName?: Prisma.SortOrder
  deviceType?: Prisma.SortOrder
  deviceModel?: Prisma.SortOrder
  operatingSystem?: Prisma.SortOrder
  architecture?: Prisma.SortOrder
  screenResolution?: Prisma.SortOrder
  totalMemory?: Prisma.SortOrder
  userNickname?: Prisma.SortOrder
  location?: Prisma.SortOrder
  notes?: Prisma.SortOrder
}

export type DeviceMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  licenseId?: Prisma.SortOrder
  deviceHash?: Prisma.SortOrder
  salt?: Prisma.SortOrder
  status?: Prisma.SortOrder
  firstSeen?: Prisma.SortOrder
  lastSeen?: Prisma.SortOrder
  removedAt?: Prisma.SortOrder
  appVersion?: Prisma.SortOrder
  deviceName?: Prisma.SortOrder
  deviceType?: Prisma.SortOrder
  deviceModel?: Prisma.SortOrder
  operatingSystem?: Prisma.SortOrder
  architecture?: Prisma.SortOrder
  screenResolution?: Prisma.SortOrder
  totalMemory?: Prisma.SortOrder
  userNickname?: Prisma.SortOrder
  location?: Prisma.SortOrder
  notes?: Prisma.SortOrder
}

export type DeviceCreateNestedManyWithoutLicenseInput = {
  create?: Prisma.XOR<Prisma.DeviceCreateWithoutLicenseInput, Prisma.DeviceUncheckedCreateWithoutLicenseInput> | Prisma.DeviceCreateWithoutLicenseInput[] | Prisma.DeviceUncheckedCreateWithoutLicenseInput[]
  connectOrCreate?: Prisma.DeviceCreateOrConnectWithoutLicenseInput | Prisma.DeviceCreateOrConnectWithoutLicenseInput[]
  createMany?: Prisma.DeviceCreateManyLicenseInputEnvelope
  connect?: Prisma.DeviceWhereUniqueInput | Prisma.DeviceWhereUniqueInput[]
}

export type DeviceUncheckedCreateNestedManyWithoutLicenseInput = {
  create?: Prisma.XOR<Prisma.DeviceCreateWithoutLicenseInput, Prisma.DeviceUncheckedCreateWithoutLicenseInput> | Prisma.DeviceCreateWithoutLicenseInput[] | Prisma.DeviceUncheckedCreateWithoutLicenseInput[]
  connectOrCreate?: Prisma.DeviceCreateOrConnectWithoutLicenseInput | Prisma.DeviceCreateOrConnectWithoutLicenseInput[]
  createMany?: Prisma.DeviceCreateManyLicenseInputEnvelope
  connect?: Prisma.DeviceWhereUniqueInput | Prisma.DeviceWhereUniqueInput[]
}

export type DeviceUpdateManyWithoutLicenseNestedInput = {
  create?: Prisma.XOR<Prisma.DeviceCreateWithoutLicenseInput, Prisma.DeviceUncheckedCreateWithoutLicenseInput> | Prisma.DeviceCreateWithoutLicenseInput[] | Prisma.DeviceUncheckedCreateWithoutLicenseInput[]
  connectOrCreate?: Prisma.DeviceCreateOrConnectWithoutLicenseInput | Prisma.DeviceCreateOrConnectWithoutLicenseInput[]
  upsert?: Prisma.DeviceUpsertWithWhereUniqueWithoutLicenseInput | Prisma.DeviceUpsertWithWhereUniqueWithoutLicenseInput[]
  createMany?: Prisma.DeviceCreateManyLicenseInputEnvelope
  set?: Prisma.DeviceWhereUniqueInput | Prisma.DeviceWhereUniqueInput[]
  disconnect?: Prisma.DeviceWhereUniqueInput | Prisma.DeviceWhereUniqueInput[]
  delete?: Prisma.DeviceWhereUniqueInput | Prisma.DeviceWhereUniqueInput[]
  connect?: Prisma.DeviceWhereUniqueInput | Prisma.DeviceWhereUniqueInput[]
  update?: Prisma.DeviceUpdateWithWhereUniqueWithoutLicenseInput | Prisma.DeviceUpdateWithWhereUniqueWithoutLicenseInput[]
  updateMany?: Prisma.DeviceUpdateManyWithWhereWithoutLicenseInput | Prisma.DeviceUpdateManyWithWhereWithoutLicenseInput[]
  deleteMany?: Prisma.DeviceScalarWhereInput | Prisma.DeviceScalarWhereInput[]
}

export type DeviceUncheckedUpdateManyWithoutLicenseNestedInput = {
  create?: Prisma.XOR<Prisma.DeviceCreateWithoutLicenseInput, Prisma.DeviceUncheckedCreateWithoutLicenseInput> | Prisma.DeviceCreateWithoutLicenseInput[] | Prisma.DeviceUncheckedCreateWithoutLicenseInput[]
  connectOrCreate?: Prisma.DeviceCreateOrConnectWithoutLicenseInput | Prisma.DeviceCreateOrConnectWithoutLicenseInput[]
  upsert?: Prisma.DeviceUpsertWithWhereUniqueWithoutLicenseInput | Prisma.DeviceUpsertWithWhereUniqueWithoutLicenseInput[]
  createMany?: Prisma.DeviceCreateManyLicenseInputEnvelope
  set?: Prisma.DeviceWhereUniqueInput | Prisma.DeviceWhereUniqueInput[]
  disconnect?: Prisma.DeviceWhereUniqueInput | Prisma.DeviceWhereUniqueInput[]
  delete?: Prisma.DeviceWhereUniqueInput | Prisma.DeviceWhereUniqueInput[]
  connect?: Prisma.DeviceWhereUniqueInput | Prisma.DeviceWhereUniqueInput[]
  update?: Prisma.DeviceUpdateWithWhereUniqueWithoutLicenseInput | Prisma.DeviceUpdateWithWhereUniqueWithoutLicenseInput[]
  updateMany?: Prisma.DeviceUpdateManyWithWhereWithoutLicenseInput | Prisma.DeviceUpdateManyWithWhereWithoutLicenseInput[]
  deleteMany?: Prisma.DeviceScalarWhereInput | Prisma.DeviceScalarWhereInput[]
}

export type EnumDeviceStatusFieldUpdateOperationsInput = {
  set?: $Enums.DeviceStatus
}

export type DeviceCreateWithoutLicenseInput = {
  id?: string
  deviceHash: string
  salt: string
  status?: $Enums.DeviceStatus
  firstSeen?: Date | string
  lastSeen?: Date | string
  removedAt?: Date | string | null
  appVersion?: string | null
  deviceName?: string | null
  deviceType?: string | null
  deviceModel?: string | null
  operatingSystem?: string | null
  architecture?: string | null
  screenResolution?: string | null
  totalMemory?: string | null
  userNickname?: string | null
  location?: string | null
  notes?: string | null
}

export type DeviceUncheckedCreateWithoutLicenseInput = {
  id?: string
  deviceHash: string
  salt: string
  status?: $Enums.DeviceStatus
  firstSeen?: Date | string
  lastSeen?: Date | string
  removedAt?: Date | string | null
  appVersion?: string | null
  deviceName?: string | null
  deviceType?: string | null
  deviceModel?: string | null
  operatingSystem?: string | null
  architecture?: string | null
  screenResolution?: string | null
  totalMemory?: string | null
  userNickname?: string | null
  location?: string | null
  notes?: string | null
}

export type DeviceCreateOrConnectWithoutLicenseInput = {
  where: Prisma.DeviceWhereUniqueInput
  create: Prisma.XOR<Prisma.DeviceCreateWithoutLicenseInput, Prisma.DeviceUncheckedCreateWithoutLicenseInput>
}

export type DeviceCreateManyLicenseInputEnvelope = {
  data: Prisma.DeviceCreateManyLicenseInput | Prisma.DeviceCreateManyLicenseInput[]
  skipDuplicates?: boolean
}

export type DeviceUpsertWithWhereUniqueWithoutLicenseInput = {
  where: Prisma.DeviceWhereUniqueInput
  update: Prisma.XOR<Prisma.DeviceUpdateWithoutLicenseInput, Prisma.DeviceUncheckedUpdateWithoutLicenseInput>
  create: Prisma.XOR<Prisma.DeviceCreateWithoutLicenseInput, Prisma.DeviceUncheckedCreateWithoutLicenseInput>
}

export type DeviceUpdateWithWhereUniqueWithoutLicenseInput = {
  where: Prisma.DeviceWhereUniqueInput
  data: Prisma.XOR<Prisma.DeviceUpdateWithoutLicenseInput, Prisma.DeviceUncheckedUpdateWithoutLicenseInput>
}

export type DeviceUpdateManyWithWhereWithoutLicenseInput = {
  where: Prisma.DeviceScalarWhereInput
  data: Prisma.XOR<Prisma.DeviceUpdateManyMutationInput, Prisma.DeviceUncheckedUpdateManyWithoutLicenseInput>
}

export type DeviceScalarWhereInput = {
  AND?: Prisma.DeviceScalarWhereInput | Prisma.DeviceScalarWhereInput[]
  OR?: Prisma.DeviceScalarWhereInput[]
  NOT?: Prisma.DeviceScalarWhereInput | Prisma.DeviceScalarWhereInput[]
  id?: Prisma.StringFilter<"Device"> | string
  licenseId?: Prisma.StringFilter<"Device"> | string
  deviceHash?: Prisma.StringFilter<"Device"> | string
  salt?: Prisma.StringFilter<"Device"> | string
  status?: Prisma.EnumDeviceStatusFilter<"Device"> | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeFilter<"Device"> | Date | string
  lastSeen?: Prisma.DateTimeFilter<"Device"> | Date | string
  removedAt?: Prisma.DateTimeNullableFilter<"Device"> | Date | string | null
  appVersion?: Prisma.StringNullableFilter<"Device"> | string | null
  deviceName?: Prisma.StringNullableFilter<"Device"> | string | null
  deviceType?: Prisma.StringNullableFilter<"Device"> | string | null
  deviceModel?: Prisma.StringNullableFilter<"Device"> | string | null
  operatingSystem?: Prisma.StringNullableFilter<"Device"> | string | null
  architecture?: Prisma.StringNullableFilter<"Device"> | string | null
  screenResolution?: Prisma.StringNullableFilter<"Device"> | string | null
  totalMemory?: Prisma.StringNullableFilter<"Device"> | string | null
  userNickname?: Prisma.StringNullableFilter<"Device"> | string | null
  location?: Prisma.StringNullableFilter<"Device"> | string | null
  notes?: Prisma.StringNullableFilter<"Device"> | string | null
}

export type DeviceCreateManyLicenseInput = {
  id?: string
  deviceHash: string
  salt: string
  status?: $Enums.DeviceStatus
  firstSeen?: Date | string
  lastSeen?: Date | string
  removedAt?: Date | string | null
  appVersion?: string | null
  deviceName?: string | null
  deviceType?: string | null
  deviceModel?: string | null
  operatingSystem?: string | null
  architecture?: string | null
  screenResolution?: string | null
  totalMemory?: string | null
  userNickname?: string | null
  location?: string | null
  notes?: string | null
}

export type DeviceUpdateWithoutLicenseInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  deviceHash?: Prisma.StringFieldUpdateOperationsInput | string
  salt?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumDeviceStatusFieldUpdateOperationsInput | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  removedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  appVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceModel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  operatingSystem?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  architecture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  screenResolution?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalMemory?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userNickname?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  notes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type DeviceUncheckedUpdateWithoutLicenseInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  deviceHash?: Prisma.StringFieldUpdateOperationsInput | string
  salt?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumDeviceStatusFieldUpdateOperationsInput | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  removedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  appVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceModel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  operatingSystem?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  architecture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  screenResolution?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalMemory?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userNickname?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  notes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type DeviceUncheckedUpdateManyWithoutLicenseInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  deviceHash?: Prisma.StringFieldUpdateOperationsInput | string
  salt?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumDeviceStatusFieldUpdateOperationsInput | $Enums.DeviceStatus
  firstSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  lastSeen?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  removedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  appVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceModel?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  operatingSystem?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  architecture?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  screenResolution?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalMemory?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userNickname?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  location?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  notes?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}



export type DeviceSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  licenseId?: boolean
  deviceHash?: boolean
  salt?: boolean
  status?: boolean
  firstSeen?: boolean
  lastSeen?: boolean
  removedAt?: boolean
  appVersion?: boolean
  deviceName?: boolean
  deviceType?: boolean
  deviceModel?: boolean
  operatingSystem?: boolean
  architecture?: boolean
  screenResolution?: boolean
  totalMemory?: boolean
  userNickname?: boolean
  location?: boolean
  notes?: boolean
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}, ExtArgs["result"]["device"]>

export type DeviceSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  licenseId?: boolean
  deviceHash?: boolean
  salt?: boolean
  status?: boolean
  firstSeen?: boolean
  lastSeen?: boolean
  removedAt?: boolean
  appVersion?: boolean
  deviceName?: boolean
  deviceType?: boolean
  deviceModel?: boolean
  operatingSystem?: boolean
  architecture?: boolean
  screenResolution?: boolean
  totalMemory?: boolean
  userNickname?: boolean
  location?: boolean
  notes?: boolean
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}, ExtArgs["result"]["device"]>

export type DeviceSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  licenseId?: boolean
  deviceHash?: boolean
  salt?: boolean
  status?: boolean
  firstSeen?: boolean
  lastSeen?: boolean
  removedAt?: boolean
  appVersion?: boolean
  deviceName?: boolean
  deviceType?: boolean
  deviceModel?: boolean
  operatingSystem?: boolean
  architecture?: boolean
  screenResolution?: boolean
  totalMemory?: boolean
  userNickname?: boolean
  location?: boolean
  notes?: boolean
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}, ExtArgs["result"]["device"]>

export type DeviceSelectScalar = {
  id?: boolean
  licenseId?: boolean
  deviceHash?: boolean
  salt?: boolean
  status?: boolean
  firstSeen?: boolean
  lastSeen?: boolean
  removedAt?: boolean
  appVersion?: boolean
  deviceName?: boolean
  deviceType?: boolean
  deviceModel?: boolean
  operatingSystem?: boolean
  architecture?: boolean
  screenResolution?: boolean
  totalMemory?: boolean
  userNickname?: boolean
  location?: boolean
  notes?: boolean
}

export type DeviceOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "licenseId" | "deviceHash" | "salt" | "status" | "firstSeen" | "lastSeen" | "removedAt" | "appVersion" | "deviceName" | "deviceType" | "deviceModel" | "operatingSystem" | "architecture" | "screenResolution" | "totalMemory" | "userNickname" | "location" | "notes", ExtArgs["result"]["device"]>
export type DeviceInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}
export type DeviceIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}
export type DeviceIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  license?: boolean | Prisma.LicenseDefaultArgs<ExtArgs>
}

export type $DevicePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Device"
  objects: {
    license: Prisma.$LicensePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    licenseId: string
    deviceHash: string
    salt: string
    status: $Enums.DeviceStatus
    firstSeen: Date
    lastSeen: Date
    removedAt: Date | null
    appVersion: string | null
    deviceName: string | null
    deviceType: string | null
    deviceModel: string | null
    operatingSystem: string | null
    architecture: string | null
    screenResolution: string | null
    totalMemory: string | null
    userNickname: string | null
    location: string | null
    notes: string | null
  }, ExtArgs["result"]["device"]>
  composites: {}
}

export type DeviceGetPayload<S extends boolean | null | undefined | DeviceDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$DevicePayload, S>

export type DeviceCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<DeviceFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: DeviceCountAggregateInputType | true
  }

export interface DeviceDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Device'], meta: { name: 'Device' } }
  /**
   * Find zero or one Device that matches the filter.
   * @param {DeviceFindUniqueArgs} args - Arguments to find a Device
   * @example
   * // Get one Device
   * const device = await prisma.device.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends DeviceFindUniqueArgs>(args: Prisma.SelectSubset<T, DeviceFindUniqueArgs<ExtArgs>>): Prisma.Prisma__DeviceClient<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Device that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {DeviceFindUniqueOrThrowArgs} args - Arguments to find a Device
   * @example
   * // Get one Device
   * const device = await prisma.device.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends DeviceFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, DeviceFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__DeviceClient<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Device that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DeviceFindFirstArgs} args - Arguments to find a Device
   * @example
   * // Get one Device
   * const device = await prisma.device.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends DeviceFindFirstArgs>(args?: Prisma.SelectSubset<T, DeviceFindFirstArgs<ExtArgs>>): Prisma.Prisma__DeviceClient<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Device that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DeviceFindFirstOrThrowArgs} args - Arguments to find a Device
   * @example
   * // Get one Device
   * const device = await prisma.device.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends DeviceFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, DeviceFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__DeviceClient<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Devices that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DeviceFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Devices
   * const devices = await prisma.device.findMany()
   * 
   * // Get first 10 Devices
   * const devices = await prisma.device.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const deviceWithIdOnly = await prisma.device.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends DeviceFindManyArgs>(args?: Prisma.SelectSubset<T, DeviceFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Device.
   * @param {DeviceCreateArgs} args - Arguments to create a Device.
   * @example
   * // Create one Device
   * const Device = await prisma.device.create({
   *   data: {
   *     // ... data to create a Device
   *   }
   * })
   * 
   */
  create<T extends DeviceCreateArgs>(args: Prisma.SelectSubset<T, DeviceCreateArgs<ExtArgs>>): Prisma.Prisma__DeviceClient<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Devices.
   * @param {DeviceCreateManyArgs} args - Arguments to create many Devices.
   * @example
   * // Create many Devices
   * const device = await prisma.device.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends DeviceCreateManyArgs>(args?: Prisma.SelectSubset<T, DeviceCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Devices and returns the data saved in the database.
   * @param {DeviceCreateManyAndReturnArgs} args - Arguments to create many Devices.
   * @example
   * // Create many Devices
   * const device = await prisma.device.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Devices and only return the `id`
   * const deviceWithIdOnly = await prisma.device.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends DeviceCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, DeviceCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Device.
   * @param {DeviceDeleteArgs} args - Arguments to delete one Device.
   * @example
   * // Delete one Device
   * const Device = await prisma.device.delete({
   *   where: {
   *     // ... filter to delete one Device
   *   }
   * })
   * 
   */
  delete<T extends DeviceDeleteArgs>(args: Prisma.SelectSubset<T, DeviceDeleteArgs<ExtArgs>>): Prisma.Prisma__DeviceClient<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Device.
   * @param {DeviceUpdateArgs} args - Arguments to update one Device.
   * @example
   * // Update one Device
   * const device = await prisma.device.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends DeviceUpdateArgs>(args: Prisma.SelectSubset<T, DeviceUpdateArgs<ExtArgs>>): Prisma.Prisma__DeviceClient<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Devices.
   * @param {DeviceDeleteManyArgs} args - Arguments to filter Devices to delete.
   * @example
   * // Delete a few Devices
   * const { count } = await prisma.device.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends DeviceDeleteManyArgs>(args?: Prisma.SelectSubset<T, DeviceDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Devices.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DeviceUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Devices
   * const device = await prisma.device.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends DeviceUpdateManyArgs>(args: Prisma.SelectSubset<T, DeviceUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Devices and returns the data updated in the database.
   * @param {DeviceUpdateManyAndReturnArgs} args - Arguments to update many Devices.
   * @example
   * // Update many Devices
   * const device = await prisma.device.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Devices and only return the `id`
   * const deviceWithIdOnly = await prisma.device.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends DeviceUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, DeviceUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Device.
   * @param {DeviceUpsertArgs} args - Arguments to update or create a Device.
   * @example
   * // Update or create a Device
   * const device = await prisma.device.upsert({
   *   create: {
   *     // ... data to create a Device
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Device we want to update
   *   }
   * })
   */
  upsert<T extends DeviceUpsertArgs>(args: Prisma.SelectSubset<T, DeviceUpsertArgs<ExtArgs>>): Prisma.Prisma__DeviceClient<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Devices.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DeviceCountArgs} args - Arguments to filter Devices to count.
   * @example
   * // Count the number of Devices
   * const count = await prisma.device.count({
   *   where: {
   *     // ... the filter for the Devices we want to count
   *   }
   * })
  **/
  count<T extends DeviceCountArgs>(
    args?: Prisma.Subset<T, DeviceCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], DeviceCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Device.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DeviceAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends DeviceAggregateArgs>(args: Prisma.Subset<T, DeviceAggregateArgs>): Prisma.PrismaPromise<GetDeviceAggregateType<T>>

  /**
   * Group by Device.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {DeviceGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends DeviceGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: DeviceGroupByArgs['orderBy'] }
      : { orderBy?: DeviceGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, DeviceGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDeviceGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Device model
 */
readonly fields: DeviceFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Device.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__DeviceClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  license<T extends Prisma.LicenseDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.LicenseDefaultArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Device model
 */
export interface DeviceFieldRefs {
  readonly id: Prisma.FieldRef<"Device", 'String'>
  readonly licenseId: Prisma.FieldRef<"Device", 'String'>
  readonly deviceHash: Prisma.FieldRef<"Device", 'String'>
  readonly salt: Prisma.FieldRef<"Device", 'String'>
  readonly status: Prisma.FieldRef<"Device", 'DeviceStatus'>
  readonly firstSeen: Prisma.FieldRef<"Device", 'DateTime'>
  readonly lastSeen: Prisma.FieldRef<"Device", 'DateTime'>
  readonly removedAt: Prisma.FieldRef<"Device", 'DateTime'>
  readonly appVersion: Prisma.FieldRef<"Device", 'String'>
  readonly deviceName: Prisma.FieldRef<"Device", 'String'>
  readonly deviceType: Prisma.FieldRef<"Device", 'String'>
  readonly deviceModel: Prisma.FieldRef<"Device", 'String'>
  readonly operatingSystem: Prisma.FieldRef<"Device", 'String'>
  readonly architecture: Prisma.FieldRef<"Device", 'String'>
  readonly screenResolution: Prisma.FieldRef<"Device", 'String'>
  readonly totalMemory: Prisma.FieldRef<"Device", 'String'>
  readonly userNickname: Prisma.FieldRef<"Device", 'String'>
  readonly location: Prisma.FieldRef<"Device", 'String'>
  readonly notes: Prisma.FieldRef<"Device", 'String'>
}
    

// Custom InputTypes
/**
 * Device findUnique
 */
export type DeviceFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
  /**
   * Filter, which Device to fetch.
   */
  where: Prisma.DeviceWhereUniqueInput
}

/**
 * Device findUniqueOrThrow
 */
export type DeviceFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
  /**
   * Filter, which Device to fetch.
   */
  where: Prisma.DeviceWhereUniqueInput
}

/**
 * Device findFirst
 */
export type DeviceFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
  /**
   * Filter, which Device to fetch.
   */
  where?: Prisma.DeviceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Devices to fetch.
   */
  orderBy?: Prisma.DeviceOrderByWithRelationInput | Prisma.DeviceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Devices.
   */
  cursor?: Prisma.DeviceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Devices from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Devices.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Devices.
   */
  distinct?: Prisma.DeviceScalarFieldEnum | Prisma.DeviceScalarFieldEnum[]
}

/**
 * Device findFirstOrThrow
 */
export type DeviceFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
  /**
   * Filter, which Device to fetch.
   */
  where?: Prisma.DeviceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Devices to fetch.
   */
  orderBy?: Prisma.DeviceOrderByWithRelationInput | Prisma.DeviceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Devices.
   */
  cursor?: Prisma.DeviceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Devices from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Devices.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Devices.
   */
  distinct?: Prisma.DeviceScalarFieldEnum | Prisma.DeviceScalarFieldEnum[]
}

/**
 * Device findMany
 */
export type DeviceFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
  /**
   * Filter, which Devices to fetch.
   */
  where?: Prisma.DeviceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Devices to fetch.
   */
  orderBy?: Prisma.DeviceOrderByWithRelationInput | Prisma.DeviceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Devices.
   */
  cursor?: Prisma.DeviceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Devices from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Devices.
   */
  skip?: number
  distinct?: Prisma.DeviceScalarFieldEnum | Prisma.DeviceScalarFieldEnum[]
}

/**
 * Device create
 */
export type DeviceCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
  /**
   * The data needed to create a Device.
   */
  data: Prisma.XOR<Prisma.DeviceCreateInput, Prisma.DeviceUncheckedCreateInput>
}

/**
 * Device createMany
 */
export type DeviceCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Devices.
   */
  data: Prisma.DeviceCreateManyInput | Prisma.DeviceCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Device createManyAndReturn
 */
export type DeviceCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * The data used to create many Devices.
   */
  data: Prisma.DeviceCreateManyInput | Prisma.DeviceCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Device update
 */
export type DeviceUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
  /**
   * The data needed to update a Device.
   */
  data: Prisma.XOR<Prisma.DeviceUpdateInput, Prisma.DeviceUncheckedUpdateInput>
  /**
   * Choose, which Device to update.
   */
  where: Prisma.DeviceWhereUniqueInput
}

/**
 * Device updateMany
 */
export type DeviceUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Devices.
   */
  data: Prisma.XOR<Prisma.DeviceUpdateManyMutationInput, Prisma.DeviceUncheckedUpdateManyInput>
  /**
   * Filter which Devices to update
   */
  where?: Prisma.DeviceWhereInput
  /**
   * Limit how many Devices to update.
   */
  limit?: number
}

/**
 * Device updateManyAndReturn
 */
export type DeviceUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * The data used to update Devices.
   */
  data: Prisma.XOR<Prisma.DeviceUpdateManyMutationInput, Prisma.DeviceUncheckedUpdateManyInput>
  /**
   * Filter which Devices to update
   */
  where?: Prisma.DeviceWhereInput
  /**
   * Limit how many Devices to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Device upsert
 */
export type DeviceUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
  /**
   * The filter to search for the Device to update in case it exists.
   */
  where: Prisma.DeviceWhereUniqueInput
  /**
   * In case the Device found by the `where` argument doesn't exist, create a new Device with this data.
   */
  create: Prisma.XOR<Prisma.DeviceCreateInput, Prisma.DeviceUncheckedCreateInput>
  /**
   * In case the Device was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.DeviceUpdateInput, Prisma.DeviceUncheckedUpdateInput>
}

/**
 * Device delete
 */
export type DeviceDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
  /**
   * Filter which Device to delete.
   */
  where: Prisma.DeviceWhereUniqueInput
}

/**
 * Device deleteMany
 */
export type DeviceDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Devices to delete
   */
  where?: Prisma.DeviceWhereInput
  /**
   * Limit how many Devices to delete.
   */
  limit?: number
}

/**
 * Device without action
 */
export type DeviceDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
}
