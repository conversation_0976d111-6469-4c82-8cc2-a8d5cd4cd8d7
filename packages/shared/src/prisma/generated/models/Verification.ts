
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Verification` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Verification
 * 
 */
export type VerificationModel = runtime.Types.Result.DefaultSelection<Prisma.$VerificationPayload>

export type AggregateVerification = {
  _count: VerificationCountAggregateOutputType | null
  _min: VerificationMinAggregateOutputType | null
  _max: VerificationMaxAggregateOutputType | null
}

export type VerificationMinAggregateOutputType = {
  id: string | null
  identifier: string | null
  value: string | null
  expiresAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type VerificationMaxAggregateOutputType = {
  id: string | null
  identifier: string | null
  value: string | null
  expiresAt: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type VerificationCountAggregateOutputType = {
  id: number
  identifier: number
  value: number
  expiresAt: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type VerificationMinAggregateInputType = {
  id?: true
  identifier?: true
  value?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
}

export type VerificationMaxAggregateInputType = {
  id?: true
  identifier?: true
  value?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
}

export type VerificationCountAggregateInputType = {
  id?: true
  identifier?: true
  value?: true
  expiresAt?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type VerificationAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Verification to aggregate.
   */
  where?: Prisma.VerificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Verifications to fetch.
   */
  orderBy?: Prisma.VerificationOrderByWithRelationInput | Prisma.VerificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.VerificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Verifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Verifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Verifications
  **/
  _count?: true | VerificationCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: VerificationMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: VerificationMaxAggregateInputType
}

export type GetVerificationAggregateType<T extends VerificationAggregateArgs> = {
      [P in keyof T & keyof AggregateVerification]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateVerification[P]>
    : Prisma.GetScalarType<T[P], AggregateVerification[P]>
}




export type VerificationGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.VerificationWhereInput
  orderBy?: Prisma.VerificationOrderByWithAggregationInput | Prisma.VerificationOrderByWithAggregationInput[]
  by: Prisma.VerificationScalarFieldEnum[] | Prisma.VerificationScalarFieldEnum
  having?: Prisma.VerificationScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: VerificationCountAggregateInputType | true
  _min?: VerificationMinAggregateInputType
  _max?: VerificationMaxAggregateInputType
}

export type VerificationGroupByOutputType = {
  id: string
  identifier: string
  value: string
  expiresAt: Date
  createdAt: Date
  updatedAt: Date
  _count: VerificationCountAggregateOutputType | null
  _min: VerificationMinAggregateOutputType | null
  _max: VerificationMaxAggregateOutputType | null
}

type GetVerificationGroupByPayload<T extends VerificationGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<VerificationGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof VerificationGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], VerificationGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], VerificationGroupByOutputType[P]>
      }
    >
  >



export type VerificationWhereInput = {
  AND?: Prisma.VerificationWhereInput | Prisma.VerificationWhereInput[]
  OR?: Prisma.VerificationWhereInput[]
  NOT?: Prisma.VerificationWhereInput | Prisma.VerificationWhereInput[]
  id?: Prisma.StringFilter<"Verification"> | string
  identifier?: Prisma.StringFilter<"Verification"> | string
  value?: Prisma.StringFilter<"Verification"> | string
  expiresAt?: Prisma.DateTimeFilter<"Verification"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"Verification"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Verification"> | Date | string
}

export type VerificationOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  value?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type VerificationWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.VerificationWhereInput | Prisma.VerificationWhereInput[]
  OR?: Prisma.VerificationWhereInput[]
  NOT?: Prisma.VerificationWhereInput | Prisma.VerificationWhereInput[]
  identifier?: Prisma.StringFilter<"Verification"> | string
  value?: Prisma.StringFilter<"Verification"> | string
  expiresAt?: Prisma.DateTimeFilter<"Verification"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"Verification"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Verification"> | Date | string
}, "id">

export type VerificationOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  value?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.VerificationCountOrderByAggregateInput
  _max?: Prisma.VerificationMaxOrderByAggregateInput
  _min?: Prisma.VerificationMinOrderByAggregateInput
}

export type VerificationScalarWhereWithAggregatesInput = {
  AND?: Prisma.VerificationScalarWhereWithAggregatesInput | Prisma.VerificationScalarWhereWithAggregatesInput[]
  OR?: Prisma.VerificationScalarWhereWithAggregatesInput[]
  NOT?: Prisma.VerificationScalarWhereWithAggregatesInput | Prisma.VerificationScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Verification"> | string
  identifier?: Prisma.StringWithAggregatesFilter<"Verification"> | string
  value?: Prisma.StringWithAggregatesFilter<"Verification"> | string
  expiresAt?: Prisma.DateTimeWithAggregatesFilter<"Verification"> | Date | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Verification"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Verification"> | Date | string
}

export type VerificationCreateInput = {
  id?: string
  identifier: string
  value: string
  expiresAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type VerificationUncheckedCreateInput = {
  id?: string
  identifier: string
  value: string
  expiresAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type VerificationUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type VerificationUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type VerificationCreateManyInput = {
  id?: string
  identifier: string
  value: string
  expiresAt: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type VerificationUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type VerificationUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  value?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type VerificationCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  value?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type VerificationMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  value?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type VerificationMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  value?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}



export type VerificationSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  identifier?: boolean
  value?: boolean
  expiresAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["verification"]>

export type VerificationSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  identifier?: boolean
  value?: boolean
  expiresAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["verification"]>

export type VerificationSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  identifier?: boolean
  value?: boolean
  expiresAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["verification"]>

export type VerificationSelectScalar = {
  id?: boolean
  identifier?: boolean
  value?: boolean
  expiresAt?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type VerificationOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "identifier" | "value" | "expiresAt" | "createdAt" | "updatedAt", ExtArgs["result"]["verification"]>

export type $VerificationPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Verification"
  objects: {}
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    identifier: string
    value: string
    expiresAt: Date
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["verification"]>
  composites: {}
}

export type VerificationGetPayload<S extends boolean | null | undefined | VerificationDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$VerificationPayload, S>

export type VerificationCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<VerificationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: VerificationCountAggregateInputType | true
  }

export interface VerificationDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Verification'], meta: { name: 'Verification' } }
  /**
   * Find zero or one Verification that matches the filter.
   * @param {VerificationFindUniqueArgs} args - Arguments to find a Verification
   * @example
   * // Get one Verification
   * const verification = await prisma.verification.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends VerificationFindUniqueArgs>(args: Prisma.SelectSubset<T, VerificationFindUniqueArgs<ExtArgs>>): Prisma.Prisma__VerificationClient<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Verification that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {VerificationFindUniqueOrThrowArgs} args - Arguments to find a Verification
   * @example
   * // Get one Verification
   * const verification = await prisma.verification.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends VerificationFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, VerificationFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__VerificationClient<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Verification that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VerificationFindFirstArgs} args - Arguments to find a Verification
   * @example
   * // Get one Verification
   * const verification = await prisma.verification.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends VerificationFindFirstArgs>(args?: Prisma.SelectSubset<T, VerificationFindFirstArgs<ExtArgs>>): Prisma.Prisma__VerificationClient<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Verification that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VerificationFindFirstOrThrowArgs} args - Arguments to find a Verification
   * @example
   * // Get one Verification
   * const verification = await prisma.verification.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends VerificationFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, VerificationFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__VerificationClient<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Verifications that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VerificationFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Verifications
   * const verifications = await prisma.verification.findMany()
   * 
   * // Get first 10 Verifications
   * const verifications = await prisma.verification.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const verificationWithIdOnly = await prisma.verification.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends VerificationFindManyArgs>(args?: Prisma.SelectSubset<T, VerificationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Verification.
   * @param {VerificationCreateArgs} args - Arguments to create a Verification.
   * @example
   * // Create one Verification
   * const Verification = await prisma.verification.create({
   *   data: {
   *     // ... data to create a Verification
   *   }
   * })
   * 
   */
  create<T extends VerificationCreateArgs>(args: Prisma.SelectSubset<T, VerificationCreateArgs<ExtArgs>>): Prisma.Prisma__VerificationClient<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Verifications.
   * @param {VerificationCreateManyArgs} args - Arguments to create many Verifications.
   * @example
   * // Create many Verifications
   * const verification = await prisma.verification.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends VerificationCreateManyArgs>(args?: Prisma.SelectSubset<T, VerificationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Verifications and returns the data saved in the database.
   * @param {VerificationCreateManyAndReturnArgs} args - Arguments to create many Verifications.
   * @example
   * // Create many Verifications
   * const verification = await prisma.verification.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Verifications and only return the `id`
   * const verificationWithIdOnly = await prisma.verification.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends VerificationCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, VerificationCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Verification.
   * @param {VerificationDeleteArgs} args - Arguments to delete one Verification.
   * @example
   * // Delete one Verification
   * const Verification = await prisma.verification.delete({
   *   where: {
   *     // ... filter to delete one Verification
   *   }
   * })
   * 
   */
  delete<T extends VerificationDeleteArgs>(args: Prisma.SelectSubset<T, VerificationDeleteArgs<ExtArgs>>): Prisma.Prisma__VerificationClient<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Verification.
   * @param {VerificationUpdateArgs} args - Arguments to update one Verification.
   * @example
   * // Update one Verification
   * const verification = await prisma.verification.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends VerificationUpdateArgs>(args: Prisma.SelectSubset<T, VerificationUpdateArgs<ExtArgs>>): Prisma.Prisma__VerificationClient<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Verifications.
   * @param {VerificationDeleteManyArgs} args - Arguments to filter Verifications to delete.
   * @example
   * // Delete a few Verifications
   * const { count } = await prisma.verification.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends VerificationDeleteManyArgs>(args?: Prisma.SelectSubset<T, VerificationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Verifications.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VerificationUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Verifications
   * const verification = await prisma.verification.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends VerificationUpdateManyArgs>(args: Prisma.SelectSubset<T, VerificationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Verifications and returns the data updated in the database.
   * @param {VerificationUpdateManyAndReturnArgs} args - Arguments to update many Verifications.
   * @example
   * // Update many Verifications
   * const verification = await prisma.verification.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Verifications and only return the `id`
   * const verificationWithIdOnly = await prisma.verification.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends VerificationUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, VerificationUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Verification.
   * @param {VerificationUpsertArgs} args - Arguments to update or create a Verification.
   * @example
   * // Update or create a Verification
   * const verification = await prisma.verification.upsert({
   *   create: {
   *     // ... data to create a Verification
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Verification we want to update
   *   }
   * })
   */
  upsert<T extends VerificationUpsertArgs>(args: Prisma.SelectSubset<T, VerificationUpsertArgs<ExtArgs>>): Prisma.Prisma__VerificationClient<runtime.Types.Result.GetResult<Prisma.$VerificationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Verifications.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VerificationCountArgs} args - Arguments to filter Verifications to count.
   * @example
   * // Count the number of Verifications
   * const count = await prisma.verification.count({
   *   where: {
   *     // ... the filter for the Verifications we want to count
   *   }
   * })
  **/
  count<T extends VerificationCountArgs>(
    args?: Prisma.Subset<T, VerificationCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], VerificationCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Verification.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VerificationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends VerificationAggregateArgs>(args: Prisma.Subset<T, VerificationAggregateArgs>): Prisma.PrismaPromise<GetVerificationAggregateType<T>>

  /**
   * Group by Verification.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VerificationGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends VerificationGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: VerificationGroupByArgs['orderBy'] }
      : { orderBy?: VerificationGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, VerificationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetVerificationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Verification model
 */
readonly fields: VerificationFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Verification.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__VerificationClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Verification model
 */
export interface VerificationFieldRefs {
  readonly id: Prisma.FieldRef<"Verification", 'String'>
  readonly identifier: Prisma.FieldRef<"Verification", 'String'>
  readonly value: Prisma.FieldRef<"Verification", 'String'>
  readonly expiresAt: Prisma.FieldRef<"Verification", 'DateTime'>
  readonly createdAt: Prisma.FieldRef<"Verification", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Verification", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Verification findUnique
 */
export type VerificationFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * Filter, which Verification to fetch.
   */
  where: Prisma.VerificationWhereUniqueInput
}

/**
 * Verification findUniqueOrThrow
 */
export type VerificationFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * Filter, which Verification to fetch.
   */
  where: Prisma.VerificationWhereUniqueInput
}

/**
 * Verification findFirst
 */
export type VerificationFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * Filter, which Verification to fetch.
   */
  where?: Prisma.VerificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Verifications to fetch.
   */
  orderBy?: Prisma.VerificationOrderByWithRelationInput | Prisma.VerificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Verifications.
   */
  cursor?: Prisma.VerificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Verifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Verifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Verifications.
   */
  distinct?: Prisma.VerificationScalarFieldEnum | Prisma.VerificationScalarFieldEnum[]
}

/**
 * Verification findFirstOrThrow
 */
export type VerificationFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * Filter, which Verification to fetch.
   */
  where?: Prisma.VerificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Verifications to fetch.
   */
  orderBy?: Prisma.VerificationOrderByWithRelationInput | Prisma.VerificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Verifications.
   */
  cursor?: Prisma.VerificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Verifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Verifications.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Verifications.
   */
  distinct?: Prisma.VerificationScalarFieldEnum | Prisma.VerificationScalarFieldEnum[]
}

/**
 * Verification findMany
 */
export type VerificationFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * Filter, which Verifications to fetch.
   */
  where?: Prisma.VerificationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Verifications to fetch.
   */
  orderBy?: Prisma.VerificationOrderByWithRelationInput | Prisma.VerificationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Verifications.
   */
  cursor?: Prisma.VerificationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Verifications from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Verifications.
   */
  skip?: number
  distinct?: Prisma.VerificationScalarFieldEnum | Prisma.VerificationScalarFieldEnum[]
}

/**
 * Verification create
 */
export type VerificationCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * The data needed to create a Verification.
   */
  data: Prisma.XOR<Prisma.VerificationCreateInput, Prisma.VerificationUncheckedCreateInput>
}

/**
 * Verification createMany
 */
export type VerificationCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Verifications.
   */
  data: Prisma.VerificationCreateManyInput | Prisma.VerificationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Verification createManyAndReturn
 */
export type VerificationCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * The data used to create many Verifications.
   */
  data: Prisma.VerificationCreateManyInput | Prisma.VerificationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Verification update
 */
export type VerificationUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * The data needed to update a Verification.
   */
  data: Prisma.XOR<Prisma.VerificationUpdateInput, Prisma.VerificationUncheckedUpdateInput>
  /**
   * Choose, which Verification to update.
   */
  where: Prisma.VerificationWhereUniqueInput
}

/**
 * Verification updateMany
 */
export type VerificationUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Verifications.
   */
  data: Prisma.XOR<Prisma.VerificationUpdateManyMutationInput, Prisma.VerificationUncheckedUpdateManyInput>
  /**
   * Filter which Verifications to update
   */
  where?: Prisma.VerificationWhereInput
  /**
   * Limit how many Verifications to update.
   */
  limit?: number
}

/**
 * Verification updateManyAndReturn
 */
export type VerificationUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * The data used to update Verifications.
   */
  data: Prisma.XOR<Prisma.VerificationUpdateManyMutationInput, Prisma.VerificationUncheckedUpdateManyInput>
  /**
   * Filter which Verifications to update
   */
  where?: Prisma.VerificationWhereInput
  /**
   * Limit how many Verifications to update.
   */
  limit?: number
}

/**
 * Verification upsert
 */
export type VerificationUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * The filter to search for the Verification to update in case it exists.
   */
  where: Prisma.VerificationWhereUniqueInput
  /**
   * In case the Verification found by the `where` argument doesn't exist, create a new Verification with this data.
   */
  create: Prisma.XOR<Prisma.VerificationCreateInput, Prisma.VerificationUncheckedCreateInput>
  /**
   * In case the Verification was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.VerificationUpdateInput, Prisma.VerificationUncheckedUpdateInput>
}

/**
 * Verification delete
 */
export type VerificationDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
  /**
   * Filter which Verification to delete.
   */
  where: Prisma.VerificationWhereUniqueInput
}

/**
 * Verification deleteMany
 */
export type VerificationDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Verifications to delete
   */
  where?: Prisma.VerificationWhereInput
  /**
   * Limit how many Verifications to delete.
   */
  limit?: number
}

/**
 * Verification without action
 */
export type VerificationDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Verification
   */
  select?: Prisma.VerificationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Verification
   */
  omit?: Prisma.VerificationOmit<ExtArgs> | null
}
