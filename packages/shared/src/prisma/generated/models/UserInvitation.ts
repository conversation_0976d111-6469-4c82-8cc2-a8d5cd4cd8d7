
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `UserInvitation` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model UserInvitation
 * 
 */
export type UserInvitationModel = runtime.Types.Result.DefaultSelection<Prisma.$UserInvitationPayload>

export type AggregateUserInvitation = {
  _count: UserInvitationCountAggregateOutputType | null
  _min: UserInvitationMinAggregateOutputType | null
  _max: UserInvitationMaxAggregateOutputType | null
}

export type UserInvitationMinAggregateOutputType = {
  id: string | null
  email: string | null
  role: $Enums.UserRole | null
  token: string | null
  status: $Enums.InvitationStatus | null
  expiresAt: Date | null
  sentAt: Date | null
  acceptedAt: Date | null
  sentBy: string | null
  acceptedBy: string | null
}

export type UserInvitationMaxAggregateOutputType = {
  id: string | null
  email: string | null
  role: $Enums.UserRole | null
  token: string | null
  status: $Enums.InvitationStatus | null
  expiresAt: Date | null
  sentAt: Date | null
  acceptedAt: Date | null
  sentBy: string | null
  acceptedBy: string | null
}

export type UserInvitationCountAggregateOutputType = {
  id: number
  email: number
  role: number
  token: number
  status: number
  expiresAt: number
  sentAt: number
  acceptedAt: number
  sentBy: number
  acceptedBy: number
  _all: number
}


export type UserInvitationMinAggregateInputType = {
  id?: true
  email?: true
  role?: true
  token?: true
  status?: true
  expiresAt?: true
  sentAt?: true
  acceptedAt?: true
  sentBy?: true
  acceptedBy?: true
}

export type UserInvitationMaxAggregateInputType = {
  id?: true
  email?: true
  role?: true
  token?: true
  status?: true
  expiresAt?: true
  sentAt?: true
  acceptedAt?: true
  sentBy?: true
  acceptedBy?: true
}

export type UserInvitationCountAggregateInputType = {
  id?: true
  email?: true
  role?: true
  token?: true
  status?: true
  expiresAt?: true
  sentAt?: true
  acceptedAt?: true
  sentBy?: true
  acceptedBy?: true
  _all?: true
}

export type UserInvitationAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which UserInvitation to aggregate.
   */
  where?: Prisma.UserInvitationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UserInvitations to fetch.
   */
  orderBy?: Prisma.UserInvitationOrderByWithRelationInput | Prisma.UserInvitationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.UserInvitationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UserInvitations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UserInvitations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned UserInvitations
  **/
  _count?: true | UserInvitationCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: UserInvitationMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: UserInvitationMaxAggregateInputType
}

export type GetUserInvitationAggregateType<T extends UserInvitationAggregateArgs> = {
      [P in keyof T & keyof AggregateUserInvitation]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUserInvitation[P]>
    : Prisma.GetScalarType<T[P], AggregateUserInvitation[P]>
}




export type UserInvitationGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UserInvitationWhereInput
  orderBy?: Prisma.UserInvitationOrderByWithAggregationInput | Prisma.UserInvitationOrderByWithAggregationInput[]
  by: Prisma.UserInvitationScalarFieldEnum[] | Prisma.UserInvitationScalarFieldEnum
  having?: Prisma.UserInvitationScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: UserInvitationCountAggregateInputType | true
  _min?: UserInvitationMinAggregateInputType
  _max?: UserInvitationMaxAggregateInputType
}

export type UserInvitationGroupByOutputType = {
  id: string
  email: string
  role: $Enums.UserRole
  token: string
  status: $Enums.InvitationStatus
  expiresAt: Date
  sentAt: Date
  acceptedAt: Date | null
  sentBy: string
  acceptedBy: string | null
  _count: UserInvitationCountAggregateOutputType | null
  _min: UserInvitationMinAggregateOutputType | null
  _max: UserInvitationMaxAggregateOutputType | null
}

type GetUserInvitationGroupByPayload<T extends UserInvitationGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<UserInvitationGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof UserInvitationGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], UserInvitationGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], UserInvitationGroupByOutputType[P]>
      }
    >
  >



export type UserInvitationWhereInput = {
  AND?: Prisma.UserInvitationWhereInput | Prisma.UserInvitationWhereInput[]
  OR?: Prisma.UserInvitationWhereInput[]
  NOT?: Prisma.UserInvitationWhereInput | Prisma.UserInvitationWhereInput[]
  id?: Prisma.StringFilter<"UserInvitation"> | string
  email?: Prisma.StringFilter<"UserInvitation"> | string
  role?: Prisma.EnumUserRoleFilter<"UserInvitation"> | $Enums.UserRole
  token?: Prisma.StringFilter<"UserInvitation"> | string
  status?: Prisma.EnumInvitationStatusFilter<"UserInvitation"> | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFilter<"UserInvitation"> | Date | string
  sentAt?: Prisma.DateTimeFilter<"UserInvitation"> | Date | string
  acceptedAt?: Prisma.DateTimeNullableFilter<"UserInvitation"> | Date | string | null
  sentBy?: Prisma.StringFilter<"UserInvitation"> | string
  acceptedBy?: Prisma.StringNullableFilter<"UserInvitation"> | string | null
  sentByUser?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  acceptedByUser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.UserWhereInput> | null
}

export type UserInvitationOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  email?: Prisma.SortOrder
  role?: Prisma.SortOrder
  token?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  sentAt?: Prisma.SortOrder
  acceptedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  sentBy?: Prisma.SortOrder
  acceptedBy?: Prisma.SortOrderInput | Prisma.SortOrder
  sentByUser?: Prisma.UserOrderByWithRelationInput
  acceptedByUser?: Prisma.UserOrderByWithRelationInput
}

export type UserInvitationWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  token?: string
  AND?: Prisma.UserInvitationWhereInput | Prisma.UserInvitationWhereInput[]
  OR?: Prisma.UserInvitationWhereInput[]
  NOT?: Prisma.UserInvitationWhereInput | Prisma.UserInvitationWhereInput[]
  email?: Prisma.StringFilter<"UserInvitation"> | string
  role?: Prisma.EnumUserRoleFilter<"UserInvitation"> | $Enums.UserRole
  status?: Prisma.EnumInvitationStatusFilter<"UserInvitation"> | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFilter<"UserInvitation"> | Date | string
  sentAt?: Prisma.DateTimeFilter<"UserInvitation"> | Date | string
  acceptedAt?: Prisma.DateTimeNullableFilter<"UserInvitation"> | Date | string | null
  sentBy?: Prisma.StringFilter<"UserInvitation"> | string
  acceptedBy?: Prisma.StringNullableFilter<"UserInvitation"> | string | null
  sentByUser?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  acceptedByUser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.UserWhereInput> | null
}, "id" | "token">

export type UserInvitationOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  email?: Prisma.SortOrder
  role?: Prisma.SortOrder
  token?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  sentAt?: Prisma.SortOrder
  acceptedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  sentBy?: Prisma.SortOrder
  acceptedBy?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.UserInvitationCountOrderByAggregateInput
  _max?: Prisma.UserInvitationMaxOrderByAggregateInput
  _min?: Prisma.UserInvitationMinOrderByAggregateInput
}

export type UserInvitationScalarWhereWithAggregatesInput = {
  AND?: Prisma.UserInvitationScalarWhereWithAggregatesInput | Prisma.UserInvitationScalarWhereWithAggregatesInput[]
  OR?: Prisma.UserInvitationScalarWhereWithAggregatesInput[]
  NOT?: Prisma.UserInvitationScalarWhereWithAggregatesInput | Prisma.UserInvitationScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"UserInvitation"> | string
  email?: Prisma.StringWithAggregatesFilter<"UserInvitation"> | string
  role?: Prisma.EnumUserRoleWithAggregatesFilter<"UserInvitation"> | $Enums.UserRole
  token?: Prisma.StringWithAggregatesFilter<"UserInvitation"> | string
  status?: Prisma.EnumInvitationStatusWithAggregatesFilter<"UserInvitation"> | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeWithAggregatesFilter<"UserInvitation"> | Date | string
  sentAt?: Prisma.DateTimeWithAggregatesFilter<"UserInvitation"> | Date | string
  acceptedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"UserInvitation"> | Date | string | null
  sentBy?: Prisma.StringWithAggregatesFilter<"UserInvitation"> | string
  acceptedBy?: Prisma.StringNullableWithAggregatesFilter<"UserInvitation"> | string | null
}

export type UserInvitationCreateInput = {
  id?: string
  email: string
  role: $Enums.UserRole
  token: string
  status?: $Enums.InvitationStatus
  expiresAt: Date | string
  sentAt?: Date | string
  acceptedAt?: Date | string | null
  sentByUser: Prisma.UserCreateNestedOneWithoutSentInvitationsInput
  acceptedByUser?: Prisma.UserCreateNestedOneWithoutReceivedInvitationsInput
}

export type UserInvitationUncheckedCreateInput = {
  id?: string
  email: string
  role: $Enums.UserRole
  token: string
  status?: $Enums.InvitationStatus
  expiresAt: Date | string
  sentAt?: Date | string
  acceptedAt?: Date | string | null
  sentBy: string
  acceptedBy?: string | null
}

export type UserInvitationUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  token?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumInvitationStatusFieldUpdateOperationsInput | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sentAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  sentByUser?: Prisma.UserUpdateOneRequiredWithoutSentInvitationsNestedInput
  acceptedByUser?: Prisma.UserUpdateOneWithoutReceivedInvitationsNestedInput
}

export type UserInvitationUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  token?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumInvitationStatusFieldUpdateOperationsInput | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sentAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  sentBy?: Prisma.StringFieldUpdateOperationsInput | string
  acceptedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type UserInvitationCreateManyInput = {
  id?: string
  email: string
  role: $Enums.UserRole
  token: string
  status?: $Enums.InvitationStatus
  expiresAt: Date | string
  sentAt?: Date | string
  acceptedAt?: Date | string | null
  sentBy: string
  acceptedBy?: string | null
}

export type UserInvitationUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  token?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumInvitationStatusFieldUpdateOperationsInput | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sentAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
}

export type UserInvitationUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  token?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumInvitationStatusFieldUpdateOperationsInput | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sentAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  sentBy?: Prisma.StringFieldUpdateOperationsInput | string
  acceptedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type UserInvitationListRelationFilter = {
  every?: Prisma.UserInvitationWhereInput
  some?: Prisma.UserInvitationWhereInput
  none?: Prisma.UserInvitationWhereInput
}

export type UserInvitationOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type UserInvitationCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  email?: Prisma.SortOrder
  role?: Prisma.SortOrder
  token?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  sentAt?: Prisma.SortOrder
  acceptedAt?: Prisma.SortOrder
  sentBy?: Prisma.SortOrder
  acceptedBy?: Prisma.SortOrder
}

export type UserInvitationMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  email?: Prisma.SortOrder
  role?: Prisma.SortOrder
  token?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  sentAt?: Prisma.SortOrder
  acceptedAt?: Prisma.SortOrder
  sentBy?: Prisma.SortOrder
  acceptedBy?: Prisma.SortOrder
}

export type UserInvitationMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  email?: Prisma.SortOrder
  role?: Prisma.SortOrder
  token?: Prisma.SortOrder
  status?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  sentAt?: Prisma.SortOrder
  acceptedAt?: Prisma.SortOrder
  sentBy?: Prisma.SortOrder
  acceptedBy?: Prisma.SortOrder
}

export type UserInvitationCreateNestedManyWithoutSentByUserInput = {
  create?: Prisma.XOR<Prisma.UserInvitationCreateWithoutSentByUserInput, Prisma.UserInvitationUncheckedCreateWithoutSentByUserInput> | Prisma.UserInvitationCreateWithoutSentByUserInput[] | Prisma.UserInvitationUncheckedCreateWithoutSentByUserInput[]
  connectOrCreate?: Prisma.UserInvitationCreateOrConnectWithoutSentByUserInput | Prisma.UserInvitationCreateOrConnectWithoutSentByUserInput[]
  createMany?: Prisma.UserInvitationCreateManySentByUserInputEnvelope
  connect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
}

export type UserInvitationCreateNestedManyWithoutAcceptedByUserInput = {
  create?: Prisma.XOR<Prisma.UserInvitationCreateWithoutAcceptedByUserInput, Prisma.UserInvitationUncheckedCreateWithoutAcceptedByUserInput> | Prisma.UserInvitationCreateWithoutAcceptedByUserInput[] | Prisma.UserInvitationUncheckedCreateWithoutAcceptedByUserInput[]
  connectOrCreate?: Prisma.UserInvitationCreateOrConnectWithoutAcceptedByUserInput | Prisma.UserInvitationCreateOrConnectWithoutAcceptedByUserInput[]
  createMany?: Prisma.UserInvitationCreateManyAcceptedByUserInputEnvelope
  connect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
}

export type UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput = {
  create?: Prisma.XOR<Prisma.UserInvitationCreateWithoutSentByUserInput, Prisma.UserInvitationUncheckedCreateWithoutSentByUserInput> | Prisma.UserInvitationCreateWithoutSentByUserInput[] | Prisma.UserInvitationUncheckedCreateWithoutSentByUserInput[]
  connectOrCreate?: Prisma.UserInvitationCreateOrConnectWithoutSentByUserInput | Prisma.UserInvitationCreateOrConnectWithoutSentByUserInput[]
  createMany?: Prisma.UserInvitationCreateManySentByUserInputEnvelope
  connect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
}

export type UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput = {
  create?: Prisma.XOR<Prisma.UserInvitationCreateWithoutAcceptedByUserInput, Prisma.UserInvitationUncheckedCreateWithoutAcceptedByUserInput> | Prisma.UserInvitationCreateWithoutAcceptedByUserInput[] | Prisma.UserInvitationUncheckedCreateWithoutAcceptedByUserInput[]
  connectOrCreate?: Prisma.UserInvitationCreateOrConnectWithoutAcceptedByUserInput | Prisma.UserInvitationCreateOrConnectWithoutAcceptedByUserInput[]
  createMany?: Prisma.UserInvitationCreateManyAcceptedByUserInputEnvelope
  connect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
}

export type UserInvitationUpdateManyWithoutSentByUserNestedInput = {
  create?: Prisma.XOR<Prisma.UserInvitationCreateWithoutSentByUserInput, Prisma.UserInvitationUncheckedCreateWithoutSentByUserInput> | Prisma.UserInvitationCreateWithoutSentByUserInput[] | Prisma.UserInvitationUncheckedCreateWithoutSentByUserInput[]
  connectOrCreate?: Prisma.UserInvitationCreateOrConnectWithoutSentByUserInput | Prisma.UserInvitationCreateOrConnectWithoutSentByUserInput[]
  upsert?: Prisma.UserInvitationUpsertWithWhereUniqueWithoutSentByUserInput | Prisma.UserInvitationUpsertWithWhereUniqueWithoutSentByUserInput[]
  createMany?: Prisma.UserInvitationCreateManySentByUserInputEnvelope
  set?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  disconnect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  delete?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  connect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  update?: Prisma.UserInvitationUpdateWithWhereUniqueWithoutSentByUserInput | Prisma.UserInvitationUpdateWithWhereUniqueWithoutSentByUserInput[]
  updateMany?: Prisma.UserInvitationUpdateManyWithWhereWithoutSentByUserInput | Prisma.UserInvitationUpdateManyWithWhereWithoutSentByUserInput[]
  deleteMany?: Prisma.UserInvitationScalarWhereInput | Prisma.UserInvitationScalarWhereInput[]
}

export type UserInvitationUpdateManyWithoutAcceptedByUserNestedInput = {
  create?: Prisma.XOR<Prisma.UserInvitationCreateWithoutAcceptedByUserInput, Prisma.UserInvitationUncheckedCreateWithoutAcceptedByUserInput> | Prisma.UserInvitationCreateWithoutAcceptedByUserInput[] | Prisma.UserInvitationUncheckedCreateWithoutAcceptedByUserInput[]
  connectOrCreate?: Prisma.UserInvitationCreateOrConnectWithoutAcceptedByUserInput | Prisma.UserInvitationCreateOrConnectWithoutAcceptedByUserInput[]
  upsert?: Prisma.UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInput | Prisma.UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInput[]
  createMany?: Prisma.UserInvitationCreateManyAcceptedByUserInputEnvelope
  set?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  disconnect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  delete?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  connect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  update?: Prisma.UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInput | Prisma.UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInput[]
  updateMany?: Prisma.UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInput | Prisma.UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInput[]
  deleteMany?: Prisma.UserInvitationScalarWhereInput | Prisma.UserInvitationScalarWhereInput[]
}

export type UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput = {
  create?: Prisma.XOR<Prisma.UserInvitationCreateWithoutSentByUserInput, Prisma.UserInvitationUncheckedCreateWithoutSentByUserInput> | Prisma.UserInvitationCreateWithoutSentByUserInput[] | Prisma.UserInvitationUncheckedCreateWithoutSentByUserInput[]
  connectOrCreate?: Prisma.UserInvitationCreateOrConnectWithoutSentByUserInput | Prisma.UserInvitationCreateOrConnectWithoutSentByUserInput[]
  upsert?: Prisma.UserInvitationUpsertWithWhereUniqueWithoutSentByUserInput | Prisma.UserInvitationUpsertWithWhereUniqueWithoutSentByUserInput[]
  createMany?: Prisma.UserInvitationCreateManySentByUserInputEnvelope
  set?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  disconnect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  delete?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  connect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  update?: Prisma.UserInvitationUpdateWithWhereUniqueWithoutSentByUserInput | Prisma.UserInvitationUpdateWithWhereUniqueWithoutSentByUserInput[]
  updateMany?: Prisma.UserInvitationUpdateManyWithWhereWithoutSentByUserInput | Prisma.UserInvitationUpdateManyWithWhereWithoutSentByUserInput[]
  deleteMany?: Prisma.UserInvitationScalarWhereInput | Prisma.UserInvitationScalarWhereInput[]
}

export type UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput = {
  create?: Prisma.XOR<Prisma.UserInvitationCreateWithoutAcceptedByUserInput, Prisma.UserInvitationUncheckedCreateWithoutAcceptedByUserInput> | Prisma.UserInvitationCreateWithoutAcceptedByUserInput[] | Prisma.UserInvitationUncheckedCreateWithoutAcceptedByUserInput[]
  connectOrCreate?: Prisma.UserInvitationCreateOrConnectWithoutAcceptedByUserInput | Prisma.UserInvitationCreateOrConnectWithoutAcceptedByUserInput[]
  upsert?: Prisma.UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInput | Prisma.UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInput[]
  createMany?: Prisma.UserInvitationCreateManyAcceptedByUserInputEnvelope
  set?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  disconnect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  delete?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  connect?: Prisma.UserInvitationWhereUniqueInput | Prisma.UserInvitationWhereUniqueInput[]
  update?: Prisma.UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInput | Prisma.UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInput[]
  updateMany?: Prisma.UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInput | Prisma.UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInput[]
  deleteMany?: Prisma.UserInvitationScalarWhereInput | Prisma.UserInvitationScalarWhereInput[]
}

export type EnumInvitationStatusFieldUpdateOperationsInput = {
  set?: $Enums.InvitationStatus
}

export type UserInvitationCreateWithoutSentByUserInput = {
  id?: string
  email: string
  role: $Enums.UserRole
  token: string
  status?: $Enums.InvitationStatus
  expiresAt: Date | string
  sentAt?: Date | string
  acceptedAt?: Date | string | null
  acceptedByUser?: Prisma.UserCreateNestedOneWithoutReceivedInvitationsInput
}

export type UserInvitationUncheckedCreateWithoutSentByUserInput = {
  id?: string
  email: string
  role: $Enums.UserRole
  token: string
  status?: $Enums.InvitationStatus
  expiresAt: Date | string
  sentAt?: Date | string
  acceptedAt?: Date | string | null
  acceptedBy?: string | null
}

export type UserInvitationCreateOrConnectWithoutSentByUserInput = {
  where: Prisma.UserInvitationWhereUniqueInput
  create: Prisma.XOR<Prisma.UserInvitationCreateWithoutSentByUserInput, Prisma.UserInvitationUncheckedCreateWithoutSentByUserInput>
}

export type UserInvitationCreateManySentByUserInputEnvelope = {
  data: Prisma.UserInvitationCreateManySentByUserInput | Prisma.UserInvitationCreateManySentByUserInput[]
  skipDuplicates?: boolean
}

export type UserInvitationCreateWithoutAcceptedByUserInput = {
  id?: string
  email: string
  role: $Enums.UserRole
  token: string
  status?: $Enums.InvitationStatus
  expiresAt: Date | string
  sentAt?: Date | string
  acceptedAt?: Date | string | null
  sentByUser: Prisma.UserCreateNestedOneWithoutSentInvitationsInput
}

export type UserInvitationUncheckedCreateWithoutAcceptedByUserInput = {
  id?: string
  email: string
  role: $Enums.UserRole
  token: string
  status?: $Enums.InvitationStatus
  expiresAt: Date | string
  sentAt?: Date | string
  acceptedAt?: Date | string | null
  sentBy: string
}

export type UserInvitationCreateOrConnectWithoutAcceptedByUserInput = {
  where: Prisma.UserInvitationWhereUniqueInput
  create: Prisma.XOR<Prisma.UserInvitationCreateWithoutAcceptedByUserInput, Prisma.UserInvitationUncheckedCreateWithoutAcceptedByUserInput>
}

export type UserInvitationCreateManyAcceptedByUserInputEnvelope = {
  data: Prisma.UserInvitationCreateManyAcceptedByUserInput | Prisma.UserInvitationCreateManyAcceptedByUserInput[]
  skipDuplicates?: boolean
}

export type UserInvitationUpsertWithWhereUniqueWithoutSentByUserInput = {
  where: Prisma.UserInvitationWhereUniqueInput
  update: Prisma.XOR<Prisma.UserInvitationUpdateWithoutSentByUserInput, Prisma.UserInvitationUncheckedUpdateWithoutSentByUserInput>
  create: Prisma.XOR<Prisma.UserInvitationCreateWithoutSentByUserInput, Prisma.UserInvitationUncheckedCreateWithoutSentByUserInput>
}

export type UserInvitationUpdateWithWhereUniqueWithoutSentByUserInput = {
  where: Prisma.UserInvitationWhereUniqueInput
  data: Prisma.XOR<Prisma.UserInvitationUpdateWithoutSentByUserInput, Prisma.UserInvitationUncheckedUpdateWithoutSentByUserInput>
}

export type UserInvitationUpdateManyWithWhereWithoutSentByUserInput = {
  where: Prisma.UserInvitationScalarWhereInput
  data: Prisma.XOR<Prisma.UserInvitationUpdateManyMutationInput, Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserInput>
}

export type UserInvitationScalarWhereInput = {
  AND?: Prisma.UserInvitationScalarWhereInput | Prisma.UserInvitationScalarWhereInput[]
  OR?: Prisma.UserInvitationScalarWhereInput[]
  NOT?: Prisma.UserInvitationScalarWhereInput | Prisma.UserInvitationScalarWhereInput[]
  id?: Prisma.StringFilter<"UserInvitation"> | string
  email?: Prisma.StringFilter<"UserInvitation"> | string
  role?: Prisma.EnumUserRoleFilter<"UserInvitation"> | $Enums.UserRole
  token?: Prisma.StringFilter<"UserInvitation"> | string
  status?: Prisma.EnumInvitationStatusFilter<"UserInvitation"> | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFilter<"UserInvitation"> | Date | string
  sentAt?: Prisma.DateTimeFilter<"UserInvitation"> | Date | string
  acceptedAt?: Prisma.DateTimeNullableFilter<"UserInvitation"> | Date | string | null
  sentBy?: Prisma.StringFilter<"UserInvitation"> | string
  acceptedBy?: Prisma.StringNullableFilter<"UserInvitation"> | string | null
}

export type UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInput = {
  where: Prisma.UserInvitationWhereUniqueInput
  update: Prisma.XOR<Prisma.UserInvitationUpdateWithoutAcceptedByUserInput, Prisma.UserInvitationUncheckedUpdateWithoutAcceptedByUserInput>
  create: Prisma.XOR<Prisma.UserInvitationCreateWithoutAcceptedByUserInput, Prisma.UserInvitationUncheckedCreateWithoutAcceptedByUserInput>
}

export type UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInput = {
  where: Prisma.UserInvitationWhereUniqueInput
  data: Prisma.XOR<Prisma.UserInvitationUpdateWithoutAcceptedByUserInput, Prisma.UserInvitationUncheckedUpdateWithoutAcceptedByUserInput>
}

export type UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInput = {
  where: Prisma.UserInvitationScalarWhereInput
  data: Prisma.XOR<Prisma.UserInvitationUpdateManyMutationInput, Prisma.UserInvitationUncheckedUpdateManyWithoutAcceptedByUserInput>
}

export type UserInvitationCreateManySentByUserInput = {
  id?: string
  email: string
  role: $Enums.UserRole
  token: string
  status?: $Enums.InvitationStatus
  expiresAt: Date | string
  sentAt?: Date | string
  acceptedAt?: Date | string | null
  acceptedBy?: string | null
}

export type UserInvitationCreateManyAcceptedByUserInput = {
  id?: string
  email: string
  role: $Enums.UserRole
  token: string
  status?: $Enums.InvitationStatus
  expiresAt: Date | string
  sentAt?: Date | string
  acceptedAt?: Date | string | null
  sentBy: string
}

export type UserInvitationUpdateWithoutSentByUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  token?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumInvitationStatusFieldUpdateOperationsInput | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sentAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  acceptedByUser?: Prisma.UserUpdateOneWithoutReceivedInvitationsNestedInput
}

export type UserInvitationUncheckedUpdateWithoutSentByUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  token?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumInvitationStatusFieldUpdateOperationsInput | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sentAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  acceptedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type UserInvitationUncheckedUpdateManyWithoutSentByUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  token?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumInvitationStatusFieldUpdateOperationsInput | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sentAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  acceptedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type UserInvitationUpdateWithoutAcceptedByUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  token?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumInvitationStatusFieldUpdateOperationsInput | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sentAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  sentByUser?: Prisma.UserUpdateOneRequiredWithoutSentInvitationsNestedInput
}

export type UserInvitationUncheckedUpdateWithoutAcceptedByUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  token?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumInvitationStatusFieldUpdateOperationsInput | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sentAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  sentBy?: Prisma.StringFieldUpdateOperationsInput | string
}

export type UserInvitationUncheckedUpdateManyWithoutAcceptedByUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
  token?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumInvitationStatusFieldUpdateOperationsInput | $Enums.InvitationStatus
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sentAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  acceptedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  sentBy?: Prisma.StringFieldUpdateOperationsInput | string
}



export type UserInvitationSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  email?: boolean
  role?: boolean
  token?: boolean
  status?: boolean
  expiresAt?: boolean
  sentAt?: boolean
  acceptedAt?: boolean
  sentBy?: boolean
  acceptedBy?: boolean
  sentByUser?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  acceptedByUser?: boolean | Prisma.UserInvitation$acceptedByUserArgs<ExtArgs>
}, ExtArgs["result"]["userInvitation"]>

export type UserInvitationSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  email?: boolean
  role?: boolean
  token?: boolean
  status?: boolean
  expiresAt?: boolean
  sentAt?: boolean
  acceptedAt?: boolean
  sentBy?: boolean
  acceptedBy?: boolean
  sentByUser?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  acceptedByUser?: boolean | Prisma.UserInvitation$acceptedByUserArgs<ExtArgs>
}, ExtArgs["result"]["userInvitation"]>

export type UserInvitationSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  email?: boolean
  role?: boolean
  token?: boolean
  status?: boolean
  expiresAt?: boolean
  sentAt?: boolean
  acceptedAt?: boolean
  sentBy?: boolean
  acceptedBy?: boolean
  sentByUser?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  acceptedByUser?: boolean | Prisma.UserInvitation$acceptedByUserArgs<ExtArgs>
}, ExtArgs["result"]["userInvitation"]>

export type UserInvitationSelectScalar = {
  id?: boolean
  email?: boolean
  role?: boolean
  token?: boolean
  status?: boolean
  expiresAt?: boolean
  sentAt?: boolean
  acceptedAt?: boolean
  sentBy?: boolean
  acceptedBy?: boolean
}

export type UserInvitationOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "email" | "role" | "token" | "status" | "expiresAt" | "sentAt" | "acceptedAt" | "sentBy" | "acceptedBy", ExtArgs["result"]["userInvitation"]>
export type UserInvitationInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sentByUser?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  acceptedByUser?: boolean | Prisma.UserInvitation$acceptedByUserArgs<ExtArgs>
}
export type UserInvitationIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sentByUser?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  acceptedByUser?: boolean | Prisma.UserInvitation$acceptedByUserArgs<ExtArgs>
}
export type UserInvitationIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sentByUser?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  acceptedByUser?: boolean | Prisma.UserInvitation$acceptedByUserArgs<ExtArgs>
}

export type $UserInvitationPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "UserInvitation"
  objects: {
    sentByUser: Prisma.$UserPayload<ExtArgs>
    acceptedByUser: Prisma.$UserPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    email: string
    role: $Enums.UserRole
    token: string
    status: $Enums.InvitationStatus
    expiresAt: Date
    sentAt: Date
    acceptedAt: Date | null
    sentBy: string
    acceptedBy: string | null
  }, ExtArgs["result"]["userInvitation"]>
  composites: {}
}

export type UserInvitationGetPayload<S extends boolean | null | undefined | UserInvitationDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload, S>

export type UserInvitationCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<UserInvitationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: UserInvitationCountAggregateInputType | true
  }

export interface UserInvitationDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['UserInvitation'], meta: { name: 'UserInvitation' } }
  /**
   * Find zero or one UserInvitation that matches the filter.
   * @param {UserInvitationFindUniqueArgs} args - Arguments to find a UserInvitation
   * @example
   * // Get one UserInvitation
   * const userInvitation = await prisma.userInvitation.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends UserInvitationFindUniqueArgs>(args: Prisma.SelectSubset<T, UserInvitationFindUniqueArgs<ExtArgs>>): Prisma.Prisma__UserInvitationClient<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one UserInvitation that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {UserInvitationFindUniqueOrThrowArgs} args - Arguments to find a UserInvitation
   * @example
   * // Get one UserInvitation
   * const userInvitation = await prisma.userInvitation.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends UserInvitationFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, UserInvitationFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__UserInvitationClient<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first UserInvitation that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserInvitationFindFirstArgs} args - Arguments to find a UserInvitation
   * @example
   * // Get one UserInvitation
   * const userInvitation = await prisma.userInvitation.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends UserInvitationFindFirstArgs>(args?: Prisma.SelectSubset<T, UserInvitationFindFirstArgs<ExtArgs>>): Prisma.Prisma__UserInvitationClient<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first UserInvitation that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserInvitationFindFirstOrThrowArgs} args - Arguments to find a UserInvitation
   * @example
   * // Get one UserInvitation
   * const userInvitation = await prisma.userInvitation.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends UserInvitationFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, UserInvitationFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__UserInvitationClient<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more UserInvitations that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserInvitationFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all UserInvitations
   * const userInvitations = await prisma.userInvitation.findMany()
   * 
   * // Get first 10 UserInvitations
   * const userInvitations = await prisma.userInvitation.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const userInvitationWithIdOnly = await prisma.userInvitation.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends UserInvitationFindManyArgs>(args?: Prisma.SelectSubset<T, UserInvitationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a UserInvitation.
   * @param {UserInvitationCreateArgs} args - Arguments to create a UserInvitation.
   * @example
   * // Create one UserInvitation
   * const UserInvitation = await prisma.userInvitation.create({
   *   data: {
   *     // ... data to create a UserInvitation
   *   }
   * })
   * 
   */
  create<T extends UserInvitationCreateArgs>(args: Prisma.SelectSubset<T, UserInvitationCreateArgs<ExtArgs>>): Prisma.Prisma__UserInvitationClient<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many UserInvitations.
   * @param {UserInvitationCreateManyArgs} args - Arguments to create many UserInvitations.
   * @example
   * // Create many UserInvitations
   * const userInvitation = await prisma.userInvitation.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends UserInvitationCreateManyArgs>(args?: Prisma.SelectSubset<T, UserInvitationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many UserInvitations and returns the data saved in the database.
   * @param {UserInvitationCreateManyAndReturnArgs} args - Arguments to create many UserInvitations.
   * @example
   * // Create many UserInvitations
   * const userInvitation = await prisma.userInvitation.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many UserInvitations and only return the `id`
   * const userInvitationWithIdOnly = await prisma.userInvitation.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends UserInvitationCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, UserInvitationCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a UserInvitation.
   * @param {UserInvitationDeleteArgs} args - Arguments to delete one UserInvitation.
   * @example
   * // Delete one UserInvitation
   * const UserInvitation = await prisma.userInvitation.delete({
   *   where: {
   *     // ... filter to delete one UserInvitation
   *   }
   * })
   * 
   */
  delete<T extends UserInvitationDeleteArgs>(args: Prisma.SelectSubset<T, UserInvitationDeleteArgs<ExtArgs>>): Prisma.Prisma__UserInvitationClient<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one UserInvitation.
   * @param {UserInvitationUpdateArgs} args - Arguments to update one UserInvitation.
   * @example
   * // Update one UserInvitation
   * const userInvitation = await prisma.userInvitation.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends UserInvitationUpdateArgs>(args: Prisma.SelectSubset<T, UserInvitationUpdateArgs<ExtArgs>>): Prisma.Prisma__UserInvitationClient<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more UserInvitations.
   * @param {UserInvitationDeleteManyArgs} args - Arguments to filter UserInvitations to delete.
   * @example
   * // Delete a few UserInvitations
   * const { count } = await prisma.userInvitation.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends UserInvitationDeleteManyArgs>(args?: Prisma.SelectSubset<T, UserInvitationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more UserInvitations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserInvitationUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many UserInvitations
   * const userInvitation = await prisma.userInvitation.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends UserInvitationUpdateManyArgs>(args: Prisma.SelectSubset<T, UserInvitationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more UserInvitations and returns the data updated in the database.
   * @param {UserInvitationUpdateManyAndReturnArgs} args - Arguments to update many UserInvitations.
   * @example
   * // Update many UserInvitations
   * const userInvitation = await prisma.userInvitation.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more UserInvitations and only return the `id`
   * const userInvitationWithIdOnly = await prisma.userInvitation.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends UserInvitationUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, UserInvitationUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one UserInvitation.
   * @param {UserInvitationUpsertArgs} args - Arguments to update or create a UserInvitation.
   * @example
   * // Update or create a UserInvitation
   * const userInvitation = await prisma.userInvitation.upsert({
   *   create: {
   *     // ... data to create a UserInvitation
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the UserInvitation we want to update
   *   }
   * })
   */
  upsert<T extends UserInvitationUpsertArgs>(args: Prisma.SelectSubset<T, UserInvitationUpsertArgs<ExtArgs>>): Prisma.Prisma__UserInvitationClient<runtime.Types.Result.GetResult<Prisma.$UserInvitationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of UserInvitations.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserInvitationCountArgs} args - Arguments to filter UserInvitations to count.
   * @example
   * // Count the number of UserInvitations
   * const count = await prisma.userInvitation.count({
   *   where: {
   *     // ... the filter for the UserInvitations we want to count
   *   }
   * })
  **/
  count<T extends UserInvitationCountArgs>(
    args?: Prisma.Subset<T, UserInvitationCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], UserInvitationCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a UserInvitation.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserInvitationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends UserInvitationAggregateArgs>(args: Prisma.Subset<T, UserInvitationAggregateArgs>): Prisma.PrismaPromise<GetUserInvitationAggregateType<T>>

  /**
   * Group by UserInvitation.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserInvitationGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends UserInvitationGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: UserInvitationGroupByArgs['orderBy'] }
      : { orderBy?: UserInvitationGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, UserInvitationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserInvitationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the UserInvitation model
 */
readonly fields: UserInvitationFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for UserInvitation.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__UserInvitationClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  sentByUser<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  acceptedByUser<T extends Prisma.UserInvitation$acceptedByUserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserInvitation$acceptedByUserArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the UserInvitation model
 */
export interface UserInvitationFieldRefs {
  readonly id: Prisma.FieldRef<"UserInvitation", 'String'>
  readonly email: Prisma.FieldRef<"UserInvitation", 'String'>
  readonly role: Prisma.FieldRef<"UserInvitation", 'UserRole'>
  readonly token: Prisma.FieldRef<"UserInvitation", 'String'>
  readonly status: Prisma.FieldRef<"UserInvitation", 'InvitationStatus'>
  readonly expiresAt: Prisma.FieldRef<"UserInvitation", 'DateTime'>
  readonly sentAt: Prisma.FieldRef<"UserInvitation", 'DateTime'>
  readonly acceptedAt: Prisma.FieldRef<"UserInvitation", 'DateTime'>
  readonly sentBy: Prisma.FieldRef<"UserInvitation", 'String'>
  readonly acceptedBy: Prisma.FieldRef<"UserInvitation", 'String'>
}
    

// Custom InputTypes
/**
 * UserInvitation findUnique
 */
export type UserInvitationFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  /**
   * Filter, which UserInvitation to fetch.
   */
  where: Prisma.UserInvitationWhereUniqueInput
}

/**
 * UserInvitation findUniqueOrThrow
 */
export type UserInvitationFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  /**
   * Filter, which UserInvitation to fetch.
   */
  where: Prisma.UserInvitationWhereUniqueInput
}

/**
 * UserInvitation findFirst
 */
export type UserInvitationFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  /**
   * Filter, which UserInvitation to fetch.
   */
  where?: Prisma.UserInvitationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UserInvitations to fetch.
   */
  orderBy?: Prisma.UserInvitationOrderByWithRelationInput | Prisma.UserInvitationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for UserInvitations.
   */
  cursor?: Prisma.UserInvitationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UserInvitations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UserInvitations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of UserInvitations.
   */
  distinct?: Prisma.UserInvitationScalarFieldEnum | Prisma.UserInvitationScalarFieldEnum[]
}

/**
 * UserInvitation findFirstOrThrow
 */
export type UserInvitationFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  /**
   * Filter, which UserInvitation to fetch.
   */
  where?: Prisma.UserInvitationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UserInvitations to fetch.
   */
  orderBy?: Prisma.UserInvitationOrderByWithRelationInput | Prisma.UserInvitationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for UserInvitations.
   */
  cursor?: Prisma.UserInvitationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UserInvitations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UserInvitations.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of UserInvitations.
   */
  distinct?: Prisma.UserInvitationScalarFieldEnum | Prisma.UserInvitationScalarFieldEnum[]
}

/**
 * UserInvitation findMany
 */
export type UserInvitationFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  /**
   * Filter, which UserInvitations to fetch.
   */
  where?: Prisma.UserInvitationWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of UserInvitations to fetch.
   */
  orderBy?: Prisma.UserInvitationOrderByWithRelationInput | Prisma.UserInvitationOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing UserInvitations.
   */
  cursor?: Prisma.UserInvitationWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` UserInvitations from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` UserInvitations.
   */
  skip?: number
  distinct?: Prisma.UserInvitationScalarFieldEnum | Prisma.UserInvitationScalarFieldEnum[]
}

/**
 * UserInvitation create
 */
export type UserInvitationCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  /**
   * The data needed to create a UserInvitation.
   */
  data: Prisma.XOR<Prisma.UserInvitationCreateInput, Prisma.UserInvitationUncheckedCreateInput>
}

/**
 * UserInvitation createMany
 */
export type UserInvitationCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many UserInvitations.
   */
  data: Prisma.UserInvitationCreateManyInput | Prisma.UserInvitationCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * UserInvitation createManyAndReturn
 */
export type UserInvitationCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * The data used to create many UserInvitations.
   */
  data: Prisma.UserInvitationCreateManyInput | Prisma.UserInvitationCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * UserInvitation update
 */
export type UserInvitationUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  /**
   * The data needed to update a UserInvitation.
   */
  data: Prisma.XOR<Prisma.UserInvitationUpdateInput, Prisma.UserInvitationUncheckedUpdateInput>
  /**
   * Choose, which UserInvitation to update.
   */
  where: Prisma.UserInvitationWhereUniqueInput
}

/**
 * UserInvitation updateMany
 */
export type UserInvitationUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update UserInvitations.
   */
  data: Prisma.XOR<Prisma.UserInvitationUpdateManyMutationInput, Prisma.UserInvitationUncheckedUpdateManyInput>
  /**
   * Filter which UserInvitations to update
   */
  where?: Prisma.UserInvitationWhereInput
  /**
   * Limit how many UserInvitations to update.
   */
  limit?: number
}

/**
 * UserInvitation updateManyAndReturn
 */
export type UserInvitationUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * The data used to update UserInvitations.
   */
  data: Prisma.XOR<Prisma.UserInvitationUpdateManyMutationInput, Prisma.UserInvitationUncheckedUpdateManyInput>
  /**
   * Filter which UserInvitations to update
   */
  where?: Prisma.UserInvitationWhereInput
  /**
   * Limit how many UserInvitations to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * UserInvitation upsert
 */
export type UserInvitationUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  /**
   * The filter to search for the UserInvitation to update in case it exists.
   */
  where: Prisma.UserInvitationWhereUniqueInput
  /**
   * In case the UserInvitation found by the `where` argument doesn't exist, create a new UserInvitation with this data.
   */
  create: Prisma.XOR<Prisma.UserInvitationCreateInput, Prisma.UserInvitationUncheckedCreateInput>
  /**
   * In case the UserInvitation was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.UserInvitationUpdateInput, Prisma.UserInvitationUncheckedUpdateInput>
}

/**
 * UserInvitation delete
 */
export type UserInvitationDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
  /**
   * Filter which UserInvitation to delete.
   */
  where: Prisma.UserInvitationWhereUniqueInput
}

/**
 * UserInvitation deleteMany
 */
export type UserInvitationDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which UserInvitations to delete
   */
  where?: Prisma.UserInvitationWhereInput
  /**
   * Limit how many UserInvitations to delete.
   */
  limit?: number
}

/**
 * UserInvitation.acceptedByUser
 */
export type UserInvitation$acceptedByUserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  where?: Prisma.UserWhereInput
}

/**
 * UserInvitation without action
 */
export type UserInvitationDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserInvitation
   */
  select?: Prisma.UserInvitationSelect<ExtArgs> | null
  /**
   * Omit specific fields from the UserInvitation
   */
  omit?: Prisma.UserInvitationOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInvitationInclude<ExtArgs> | null
}
