
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `SupportMessage` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model SupportMessage
 * 
 */
export type SupportMessageModel = runtime.Types.Result.DefaultSelection<Prisma.$SupportMessagePayload>

export type AggregateSupportMessage = {
  _count: SupportMessageCountAggregateOutputType | null
  _min: SupportMessageMinAggregateOutputType | null
  _max: SupportMessageMaxAggregateOutputType | null
}

export type SupportMessageMinAggregateOutputType = {
  id: string | null
  ticketId: string | null
  message: string | null
  isInternal: boolean | null
  authorEmail: string | null
  authorId: string | null
  createdAt: Date | null
}

export type SupportMessageMaxAggregateOutputType = {
  id: string | null
  ticketId: string | null
  message: string | null
  isInternal: boolean | null
  authorEmail: string | null
  authorId: string | null
  createdAt: Date | null
}

export type SupportMessageCountAggregateOutputType = {
  id: number
  ticketId: number
  message: number
  isInternal: number
  authorEmail: number
  authorId: number
  createdAt: number
  _all: number
}


export type SupportMessageMinAggregateInputType = {
  id?: true
  ticketId?: true
  message?: true
  isInternal?: true
  authorEmail?: true
  authorId?: true
  createdAt?: true
}

export type SupportMessageMaxAggregateInputType = {
  id?: true
  ticketId?: true
  message?: true
  isInternal?: true
  authorEmail?: true
  authorId?: true
  createdAt?: true
}

export type SupportMessageCountAggregateInputType = {
  id?: true
  ticketId?: true
  message?: true
  isInternal?: true
  authorEmail?: true
  authorId?: true
  createdAt?: true
  _all?: true
}

export type SupportMessageAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which SupportMessage to aggregate.
   */
  where?: Prisma.SupportMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of SupportMessages to fetch.
   */
  orderBy?: Prisma.SupportMessageOrderByWithRelationInput | Prisma.SupportMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.SupportMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` SupportMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` SupportMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned SupportMessages
  **/
  _count?: true | SupportMessageCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: SupportMessageMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: SupportMessageMaxAggregateInputType
}

export type GetSupportMessageAggregateType<T extends SupportMessageAggregateArgs> = {
      [P in keyof T & keyof AggregateSupportMessage]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSupportMessage[P]>
    : Prisma.GetScalarType<T[P], AggregateSupportMessage[P]>
}




export type SupportMessageGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SupportMessageWhereInput
  orderBy?: Prisma.SupportMessageOrderByWithAggregationInput | Prisma.SupportMessageOrderByWithAggregationInput[]
  by: Prisma.SupportMessageScalarFieldEnum[] | Prisma.SupportMessageScalarFieldEnum
  having?: Prisma.SupportMessageScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SupportMessageCountAggregateInputType | true
  _min?: SupportMessageMinAggregateInputType
  _max?: SupportMessageMaxAggregateInputType
}

export type SupportMessageGroupByOutputType = {
  id: string
  ticketId: string
  message: string
  isInternal: boolean
  authorEmail: string | null
  authorId: string | null
  createdAt: Date
  _count: SupportMessageCountAggregateOutputType | null
  _min: SupportMessageMinAggregateOutputType | null
  _max: SupportMessageMaxAggregateOutputType | null
}

type GetSupportMessageGroupByPayload<T extends SupportMessageGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SupportMessageGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof SupportMessageGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], SupportMessageGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], SupportMessageGroupByOutputType[P]>
      }
    >
  >



export type SupportMessageWhereInput = {
  AND?: Prisma.SupportMessageWhereInput | Prisma.SupportMessageWhereInput[]
  OR?: Prisma.SupportMessageWhereInput[]
  NOT?: Prisma.SupportMessageWhereInput | Prisma.SupportMessageWhereInput[]
  id?: Prisma.StringFilter<"SupportMessage"> | string
  ticketId?: Prisma.StringFilter<"SupportMessage"> | string
  message?: Prisma.StringFilter<"SupportMessage"> | string
  isInternal?: Prisma.BoolFilter<"SupportMessage"> | boolean
  authorEmail?: Prisma.StringNullableFilter<"SupportMessage"> | string | null
  authorId?: Prisma.StringNullableFilter<"SupportMessage"> | string | null
  createdAt?: Prisma.DateTimeFilter<"SupportMessage"> | Date | string
  ticket?: Prisma.XOR<Prisma.SupportTicketScalarRelationFilter, Prisma.SupportTicketWhereInput>
  authorUser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.UserWhereInput> | null
}

export type SupportMessageOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  ticketId?: Prisma.SortOrder
  message?: Prisma.SortOrder
  isInternal?: Prisma.SortOrder
  authorEmail?: Prisma.SortOrderInput | Prisma.SortOrder
  authorId?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  ticket?: Prisma.SupportTicketOrderByWithRelationInput
  authorUser?: Prisma.UserOrderByWithRelationInput
}

export type SupportMessageWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.SupportMessageWhereInput | Prisma.SupportMessageWhereInput[]
  OR?: Prisma.SupportMessageWhereInput[]
  NOT?: Prisma.SupportMessageWhereInput | Prisma.SupportMessageWhereInput[]
  ticketId?: Prisma.StringFilter<"SupportMessage"> | string
  message?: Prisma.StringFilter<"SupportMessage"> | string
  isInternal?: Prisma.BoolFilter<"SupportMessage"> | boolean
  authorEmail?: Prisma.StringNullableFilter<"SupportMessage"> | string | null
  authorId?: Prisma.StringNullableFilter<"SupportMessage"> | string | null
  createdAt?: Prisma.DateTimeFilter<"SupportMessage"> | Date | string
  ticket?: Prisma.XOR<Prisma.SupportTicketScalarRelationFilter, Prisma.SupportTicketWhereInput>
  authorUser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.UserWhereInput> | null
}, "id">

export type SupportMessageOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  ticketId?: Prisma.SortOrder
  message?: Prisma.SortOrder
  isInternal?: Prisma.SortOrder
  authorEmail?: Prisma.SortOrderInput | Prisma.SortOrder
  authorId?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  _count?: Prisma.SupportMessageCountOrderByAggregateInput
  _max?: Prisma.SupportMessageMaxOrderByAggregateInput
  _min?: Prisma.SupportMessageMinOrderByAggregateInput
}

export type SupportMessageScalarWhereWithAggregatesInput = {
  AND?: Prisma.SupportMessageScalarWhereWithAggregatesInput | Prisma.SupportMessageScalarWhereWithAggregatesInput[]
  OR?: Prisma.SupportMessageScalarWhereWithAggregatesInput[]
  NOT?: Prisma.SupportMessageScalarWhereWithAggregatesInput | Prisma.SupportMessageScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"SupportMessage"> | string
  ticketId?: Prisma.StringWithAggregatesFilter<"SupportMessage"> | string
  message?: Prisma.StringWithAggregatesFilter<"SupportMessage"> | string
  isInternal?: Prisma.BoolWithAggregatesFilter<"SupportMessage"> | boolean
  authorEmail?: Prisma.StringNullableWithAggregatesFilter<"SupportMessage"> | string | null
  authorId?: Prisma.StringNullableWithAggregatesFilter<"SupportMessage"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"SupportMessage"> | Date | string
}

export type SupportMessageCreateInput = {
  id?: string
  message: string
  isInternal?: boolean
  authorEmail?: string | null
  createdAt?: Date | string
  ticket: Prisma.SupportTicketCreateNestedOneWithoutMessagesInput
  authorUser?: Prisma.UserCreateNestedOneWithoutSupportMessagesInput
}

export type SupportMessageUncheckedCreateInput = {
  id?: string
  ticketId: string
  message: string
  isInternal?: boolean
  authorEmail?: string | null
  authorId?: string | null
  createdAt?: Date | string
}

export type SupportMessageUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isInternal?: Prisma.BoolFieldUpdateOperationsInput | boolean
  authorEmail?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ticket?: Prisma.SupportTicketUpdateOneRequiredWithoutMessagesNestedInput
  authorUser?: Prisma.UserUpdateOneWithoutSupportMessagesNestedInput
}

export type SupportMessageUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isInternal?: Prisma.BoolFieldUpdateOperationsInput | boolean
  authorEmail?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SupportMessageCreateManyInput = {
  id?: string
  ticketId: string
  message: string
  isInternal?: boolean
  authorEmail?: string | null
  authorId?: string | null
  createdAt?: Date | string
}

export type SupportMessageUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isInternal?: Prisma.BoolFieldUpdateOperationsInput | boolean
  authorEmail?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SupportMessageUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isInternal?: Prisma.BoolFieldUpdateOperationsInput | boolean
  authorEmail?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SupportMessageListRelationFilter = {
  every?: Prisma.SupportMessageWhereInput
  some?: Prisma.SupportMessageWhereInput
  none?: Prisma.SupportMessageWhereInput
}

export type SupportMessageOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type SupportMessageCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  ticketId?: Prisma.SortOrder
  message?: Prisma.SortOrder
  isInternal?: Prisma.SortOrder
  authorEmail?: Prisma.SortOrder
  authorId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type SupportMessageMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  ticketId?: Prisma.SortOrder
  message?: Prisma.SortOrder
  isInternal?: Prisma.SortOrder
  authorEmail?: Prisma.SortOrder
  authorId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type SupportMessageMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  ticketId?: Prisma.SortOrder
  message?: Prisma.SortOrder
  isInternal?: Prisma.SortOrder
  authorEmail?: Prisma.SortOrder
  authorId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
}

export type SupportMessageCreateNestedManyWithoutAuthorUserInput = {
  create?: Prisma.XOR<Prisma.SupportMessageCreateWithoutAuthorUserInput, Prisma.SupportMessageUncheckedCreateWithoutAuthorUserInput> | Prisma.SupportMessageCreateWithoutAuthorUserInput[] | Prisma.SupportMessageUncheckedCreateWithoutAuthorUserInput[]
  connectOrCreate?: Prisma.SupportMessageCreateOrConnectWithoutAuthorUserInput | Prisma.SupportMessageCreateOrConnectWithoutAuthorUserInput[]
  createMany?: Prisma.SupportMessageCreateManyAuthorUserInputEnvelope
  connect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
}

export type SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput = {
  create?: Prisma.XOR<Prisma.SupportMessageCreateWithoutAuthorUserInput, Prisma.SupportMessageUncheckedCreateWithoutAuthorUserInput> | Prisma.SupportMessageCreateWithoutAuthorUserInput[] | Prisma.SupportMessageUncheckedCreateWithoutAuthorUserInput[]
  connectOrCreate?: Prisma.SupportMessageCreateOrConnectWithoutAuthorUserInput | Prisma.SupportMessageCreateOrConnectWithoutAuthorUserInput[]
  createMany?: Prisma.SupportMessageCreateManyAuthorUserInputEnvelope
  connect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
}

export type SupportMessageUpdateManyWithoutAuthorUserNestedInput = {
  create?: Prisma.XOR<Prisma.SupportMessageCreateWithoutAuthorUserInput, Prisma.SupportMessageUncheckedCreateWithoutAuthorUserInput> | Prisma.SupportMessageCreateWithoutAuthorUserInput[] | Prisma.SupportMessageUncheckedCreateWithoutAuthorUserInput[]
  connectOrCreate?: Prisma.SupportMessageCreateOrConnectWithoutAuthorUserInput | Prisma.SupportMessageCreateOrConnectWithoutAuthorUserInput[]
  upsert?: Prisma.SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInput | Prisma.SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInput[]
  createMany?: Prisma.SupportMessageCreateManyAuthorUserInputEnvelope
  set?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  disconnect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  delete?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  connect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  update?: Prisma.SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInput | Prisma.SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInput[]
  updateMany?: Prisma.SupportMessageUpdateManyWithWhereWithoutAuthorUserInput | Prisma.SupportMessageUpdateManyWithWhereWithoutAuthorUserInput[]
  deleteMany?: Prisma.SupportMessageScalarWhereInput | Prisma.SupportMessageScalarWhereInput[]
}

export type SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput = {
  create?: Prisma.XOR<Prisma.SupportMessageCreateWithoutAuthorUserInput, Prisma.SupportMessageUncheckedCreateWithoutAuthorUserInput> | Prisma.SupportMessageCreateWithoutAuthorUserInput[] | Prisma.SupportMessageUncheckedCreateWithoutAuthorUserInput[]
  connectOrCreate?: Prisma.SupportMessageCreateOrConnectWithoutAuthorUserInput | Prisma.SupportMessageCreateOrConnectWithoutAuthorUserInput[]
  upsert?: Prisma.SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInput | Prisma.SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInput[]
  createMany?: Prisma.SupportMessageCreateManyAuthorUserInputEnvelope
  set?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  disconnect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  delete?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  connect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  update?: Prisma.SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInput | Prisma.SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInput[]
  updateMany?: Prisma.SupportMessageUpdateManyWithWhereWithoutAuthorUserInput | Prisma.SupportMessageUpdateManyWithWhereWithoutAuthorUserInput[]
  deleteMany?: Prisma.SupportMessageScalarWhereInput | Prisma.SupportMessageScalarWhereInput[]
}

export type SupportMessageCreateNestedManyWithoutTicketInput = {
  create?: Prisma.XOR<Prisma.SupportMessageCreateWithoutTicketInput, Prisma.SupportMessageUncheckedCreateWithoutTicketInput> | Prisma.SupportMessageCreateWithoutTicketInput[] | Prisma.SupportMessageUncheckedCreateWithoutTicketInput[]
  connectOrCreate?: Prisma.SupportMessageCreateOrConnectWithoutTicketInput | Prisma.SupportMessageCreateOrConnectWithoutTicketInput[]
  createMany?: Prisma.SupportMessageCreateManyTicketInputEnvelope
  connect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
}

export type SupportMessageUncheckedCreateNestedManyWithoutTicketInput = {
  create?: Prisma.XOR<Prisma.SupportMessageCreateWithoutTicketInput, Prisma.SupportMessageUncheckedCreateWithoutTicketInput> | Prisma.SupportMessageCreateWithoutTicketInput[] | Prisma.SupportMessageUncheckedCreateWithoutTicketInput[]
  connectOrCreate?: Prisma.SupportMessageCreateOrConnectWithoutTicketInput | Prisma.SupportMessageCreateOrConnectWithoutTicketInput[]
  createMany?: Prisma.SupportMessageCreateManyTicketInputEnvelope
  connect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
}

export type SupportMessageUpdateManyWithoutTicketNestedInput = {
  create?: Prisma.XOR<Prisma.SupportMessageCreateWithoutTicketInput, Prisma.SupportMessageUncheckedCreateWithoutTicketInput> | Prisma.SupportMessageCreateWithoutTicketInput[] | Prisma.SupportMessageUncheckedCreateWithoutTicketInput[]
  connectOrCreate?: Prisma.SupportMessageCreateOrConnectWithoutTicketInput | Prisma.SupportMessageCreateOrConnectWithoutTicketInput[]
  upsert?: Prisma.SupportMessageUpsertWithWhereUniqueWithoutTicketInput | Prisma.SupportMessageUpsertWithWhereUniqueWithoutTicketInput[]
  createMany?: Prisma.SupportMessageCreateManyTicketInputEnvelope
  set?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  disconnect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  delete?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  connect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  update?: Prisma.SupportMessageUpdateWithWhereUniqueWithoutTicketInput | Prisma.SupportMessageUpdateWithWhereUniqueWithoutTicketInput[]
  updateMany?: Prisma.SupportMessageUpdateManyWithWhereWithoutTicketInput | Prisma.SupportMessageUpdateManyWithWhereWithoutTicketInput[]
  deleteMany?: Prisma.SupportMessageScalarWhereInput | Prisma.SupportMessageScalarWhereInput[]
}

export type SupportMessageUncheckedUpdateManyWithoutTicketNestedInput = {
  create?: Prisma.XOR<Prisma.SupportMessageCreateWithoutTicketInput, Prisma.SupportMessageUncheckedCreateWithoutTicketInput> | Prisma.SupportMessageCreateWithoutTicketInput[] | Prisma.SupportMessageUncheckedCreateWithoutTicketInput[]
  connectOrCreate?: Prisma.SupportMessageCreateOrConnectWithoutTicketInput | Prisma.SupportMessageCreateOrConnectWithoutTicketInput[]
  upsert?: Prisma.SupportMessageUpsertWithWhereUniqueWithoutTicketInput | Prisma.SupportMessageUpsertWithWhereUniqueWithoutTicketInput[]
  createMany?: Prisma.SupportMessageCreateManyTicketInputEnvelope
  set?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  disconnect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  delete?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  connect?: Prisma.SupportMessageWhereUniqueInput | Prisma.SupportMessageWhereUniqueInput[]
  update?: Prisma.SupportMessageUpdateWithWhereUniqueWithoutTicketInput | Prisma.SupportMessageUpdateWithWhereUniqueWithoutTicketInput[]
  updateMany?: Prisma.SupportMessageUpdateManyWithWhereWithoutTicketInput | Prisma.SupportMessageUpdateManyWithWhereWithoutTicketInput[]
  deleteMany?: Prisma.SupportMessageScalarWhereInput | Prisma.SupportMessageScalarWhereInput[]
}

export type SupportMessageCreateWithoutAuthorUserInput = {
  id?: string
  message: string
  isInternal?: boolean
  authorEmail?: string | null
  createdAt?: Date | string
  ticket: Prisma.SupportTicketCreateNestedOneWithoutMessagesInput
}

export type SupportMessageUncheckedCreateWithoutAuthorUserInput = {
  id?: string
  ticketId: string
  message: string
  isInternal?: boolean
  authorEmail?: string | null
  createdAt?: Date | string
}

export type SupportMessageCreateOrConnectWithoutAuthorUserInput = {
  where: Prisma.SupportMessageWhereUniqueInput
  create: Prisma.XOR<Prisma.SupportMessageCreateWithoutAuthorUserInput, Prisma.SupportMessageUncheckedCreateWithoutAuthorUserInput>
}

export type SupportMessageCreateManyAuthorUserInputEnvelope = {
  data: Prisma.SupportMessageCreateManyAuthorUserInput | Prisma.SupportMessageCreateManyAuthorUserInput[]
  skipDuplicates?: boolean
}

export type SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInput = {
  where: Prisma.SupportMessageWhereUniqueInput
  update: Prisma.XOR<Prisma.SupportMessageUpdateWithoutAuthorUserInput, Prisma.SupportMessageUncheckedUpdateWithoutAuthorUserInput>
  create: Prisma.XOR<Prisma.SupportMessageCreateWithoutAuthorUserInput, Prisma.SupportMessageUncheckedCreateWithoutAuthorUserInput>
}

export type SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInput = {
  where: Prisma.SupportMessageWhereUniqueInput
  data: Prisma.XOR<Prisma.SupportMessageUpdateWithoutAuthorUserInput, Prisma.SupportMessageUncheckedUpdateWithoutAuthorUserInput>
}

export type SupportMessageUpdateManyWithWhereWithoutAuthorUserInput = {
  where: Prisma.SupportMessageScalarWhereInput
  data: Prisma.XOR<Prisma.SupportMessageUpdateManyMutationInput, Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserInput>
}

export type SupportMessageScalarWhereInput = {
  AND?: Prisma.SupportMessageScalarWhereInput | Prisma.SupportMessageScalarWhereInput[]
  OR?: Prisma.SupportMessageScalarWhereInput[]
  NOT?: Prisma.SupportMessageScalarWhereInput | Prisma.SupportMessageScalarWhereInput[]
  id?: Prisma.StringFilter<"SupportMessage"> | string
  ticketId?: Prisma.StringFilter<"SupportMessage"> | string
  message?: Prisma.StringFilter<"SupportMessage"> | string
  isInternal?: Prisma.BoolFilter<"SupportMessage"> | boolean
  authorEmail?: Prisma.StringNullableFilter<"SupportMessage"> | string | null
  authorId?: Prisma.StringNullableFilter<"SupportMessage"> | string | null
  createdAt?: Prisma.DateTimeFilter<"SupportMessage"> | Date | string
}

export type SupportMessageCreateWithoutTicketInput = {
  id?: string
  message: string
  isInternal?: boolean
  authorEmail?: string | null
  createdAt?: Date | string
  authorUser?: Prisma.UserCreateNestedOneWithoutSupportMessagesInput
}

export type SupportMessageUncheckedCreateWithoutTicketInput = {
  id?: string
  message: string
  isInternal?: boolean
  authorEmail?: string | null
  authorId?: string | null
  createdAt?: Date | string
}

export type SupportMessageCreateOrConnectWithoutTicketInput = {
  where: Prisma.SupportMessageWhereUniqueInput
  create: Prisma.XOR<Prisma.SupportMessageCreateWithoutTicketInput, Prisma.SupportMessageUncheckedCreateWithoutTicketInput>
}

export type SupportMessageCreateManyTicketInputEnvelope = {
  data: Prisma.SupportMessageCreateManyTicketInput | Prisma.SupportMessageCreateManyTicketInput[]
  skipDuplicates?: boolean
}

export type SupportMessageUpsertWithWhereUniqueWithoutTicketInput = {
  where: Prisma.SupportMessageWhereUniqueInput
  update: Prisma.XOR<Prisma.SupportMessageUpdateWithoutTicketInput, Prisma.SupportMessageUncheckedUpdateWithoutTicketInput>
  create: Prisma.XOR<Prisma.SupportMessageCreateWithoutTicketInput, Prisma.SupportMessageUncheckedCreateWithoutTicketInput>
}

export type SupportMessageUpdateWithWhereUniqueWithoutTicketInput = {
  where: Prisma.SupportMessageWhereUniqueInput
  data: Prisma.XOR<Prisma.SupportMessageUpdateWithoutTicketInput, Prisma.SupportMessageUncheckedUpdateWithoutTicketInput>
}

export type SupportMessageUpdateManyWithWhereWithoutTicketInput = {
  where: Prisma.SupportMessageScalarWhereInput
  data: Prisma.XOR<Prisma.SupportMessageUpdateManyMutationInput, Prisma.SupportMessageUncheckedUpdateManyWithoutTicketInput>
}

export type SupportMessageCreateManyAuthorUserInput = {
  id?: string
  ticketId: string
  message: string
  isInternal?: boolean
  authorEmail?: string | null
  createdAt?: Date | string
}

export type SupportMessageUpdateWithoutAuthorUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isInternal?: Prisma.BoolFieldUpdateOperationsInput | boolean
  authorEmail?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ticket?: Prisma.SupportTicketUpdateOneRequiredWithoutMessagesNestedInput
}

export type SupportMessageUncheckedUpdateWithoutAuthorUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isInternal?: Prisma.BoolFieldUpdateOperationsInput | boolean
  authorEmail?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SupportMessageUncheckedUpdateManyWithoutAuthorUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  ticketId?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isInternal?: Prisma.BoolFieldUpdateOperationsInput | boolean
  authorEmail?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SupportMessageCreateManyTicketInput = {
  id?: string
  message: string
  isInternal?: boolean
  authorEmail?: string | null
  authorId?: string | null
  createdAt?: Date | string
}

export type SupportMessageUpdateWithoutTicketInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isInternal?: Prisma.BoolFieldUpdateOperationsInput | boolean
  authorEmail?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  authorUser?: Prisma.UserUpdateOneWithoutSupportMessagesNestedInput
}

export type SupportMessageUncheckedUpdateWithoutTicketInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isInternal?: Prisma.BoolFieldUpdateOperationsInput | boolean
  authorEmail?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SupportMessageUncheckedUpdateManyWithoutTicketInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  message?: Prisma.StringFieldUpdateOperationsInput | string
  isInternal?: Prisma.BoolFieldUpdateOperationsInput | boolean
  authorEmail?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  authorId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type SupportMessageSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  ticketId?: boolean
  message?: boolean
  isInternal?: boolean
  authorEmail?: boolean
  authorId?: boolean
  createdAt?: boolean
  ticket?: boolean | Prisma.SupportTicketDefaultArgs<ExtArgs>
  authorUser?: boolean | Prisma.SupportMessage$authorUserArgs<ExtArgs>
}, ExtArgs["result"]["supportMessage"]>

export type SupportMessageSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  ticketId?: boolean
  message?: boolean
  isInternal?: boolean
  authorEmail?: boolean
  authorId?: boolean
  createdAt?: boolean
  ticket?: boolean | Prisma.SupportTicketDefaultArgs<ExtArgs>
  authorUser?: boolean | Prisma.SupportMessage$authorUserArgs<ExtArgs>
}, ExtArgs["result"]["supportMessage"]>

export type SupportMessageSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  ticketId?: boolean
  message?: boolean
  isInternal?: boolean
  authorEmail?: boolean
  authorId?: boolean
  createdAt?: boolean
  ticket?: boolean | Prisma.SupportTicketDefaultArgs<ExtArgs>
  authorUser?: boolean | Prisma.SupportMessage$authorUserArgs<ExtArgs>
}, ExtArgs["result"]["supportMessage"]>

export type SupportMessageSelectScalar = {
  id?: boolean
  ticketId?: boolean
  message?: boolean
  isInternal?: boolean
  authorEmail?: boolean
  authorId?: boolean
  createdAt?: boolean
}

export type SupportMessageOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "ticketId" | "message" | "isInternal" | "authorEmail" | "authorId" | "createdAt", ExtArgs["result"]["supportMessage"]>
export type SupportMessageInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  ticket?: boolean | Prisma.SupportTicketDefaultArgs<ExtArgs>
  authorUser?: boolean | Prisma.SupportMessage$authorUserArgs<ExtArgs>
}
export type SupportMessageIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  ticket?: boolean | Prisma.SupportTicketDefaultArgs<ExtArgs>
  authorUser?: boolean | Prisma.SupportMessage$authorUserArgs<ExtArgs>
}
export type SupportMessageIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  ticket?: boolean | Prisma.SupportTicketDefaultArgs<ExtArgs>
  authorUser?: boolean | Prisma.SupportMessage$authorUserArgs<ExtArgs>
}

export type $SupportMessagePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "SupportMessage"
  objects: {
    ticket: Prisma.$SupportTicketPayload<ExtArgs>
    authorUser: Prisma.$UserPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    ticketId: string
    message: string
    isInternal: boolean
    authorEmail: string | null
    authorId: string | null
    createdAt: Date
  }, ExtArgs["result"]["supportMessage"]>
  composites: {}
}

export type SupportMessageGetPayload<S extends boolean | null | undefined | SupportMessageDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload, S>

export type SupportMessageCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<SupportMessageFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: SupportMessageCountAggregateInputType | true
  }

export interface SupportMessageDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['SupportMessage'], meta: { name: 'SupportMessage' } }
  /**
   * Find zero or one SupportMessage that matches the filter.
   * @param {SupportMessageFindUniqueArgs} args - Arguments to find a SupportMessage
   * @example
   * // Get one SupportMessage
   * const supportMessage = await prisma.supportMessage.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SupportMessageFindUniqueArgs>(args: Prisma.SelectSubset<T, SupportMessageFindUniqueArgs<ExtArgs>>): Prisma.Prisma__SupportMessageClient<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one SupportMessage that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SupportMessageFindUniqueOrThrowArgs} args - Arguments to find a SupportMessage
   * @example
   * // Get one SupportMessage
   * const supportMessage = await prisma.supportMessage.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SupportMessageFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, SupportMessageFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__SupportMessageClient<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first SupportMessage that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportMessageFindFirstArgs} args - Arguments to find a SupportMessage
   * @example
   * // Get one SupportMessage
   * const supportMessage = await prisma.supportMessage.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SupportMessageFindFirstArgs>(args?: Prisma.SelectSubset<T, SupportMessageFindFirstArgs<ExtArgs>>): Prisma.Prisma__SupportMessageClient<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first SupportMessage that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportMessageFindFirstOrThrowArgs} args - Arguments to find a SupportMessage
   * @example
   * // Get one SupportMessage
   * const supportMessage = await prisma.supportMessage.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SupportMessageFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, SupportMessageFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__SupportMessageClient<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more SupportMessages that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportMessageFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all SupportMessages
   * const supportMessages = await prisma.supportMessage.findMany()
   * 
   * // Get first 10 SupportMessages
   * const supportMessages = await prisma.supportMessage.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const supportMessageWithIdOnly = await prisma.supportMessage.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends SupportMessageFindManyArgs>(args?: Prisma.SelectSubset<T, SupportMessageFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a SupportMessage.
   * @param {SupportMessageCreateArgs} args - Arguments to create a SupportMessage.
   * @example
   * // Create one SupportMessage
   * const SupportMessage = await prisma.supportMessage.create({
   *   data: {
   *     // ... data to create a SupportMessage
   *   }
   * })
   * 
   */
  create<T extends SupportMessageCreateArgs>(args: Prisma.SelectSubset<T, SupportMessageCreateArgs<ExtArgs>>): Prisma.Prisma__SupportMessageClient<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many SupportMessages.
   * @param {SupportMessageCreateManyArgs} args - Arguments to create many SupportMessages.
   * @example
   * // Create many SupportMessages
   * const supportMessage = await prisma.supportMessage.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends SupportMessageCreateManyArgs>(args?: Prisma.SelectSubset<T, SupportMessageCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many SupportMessages and returns the data saved in the database.
   * @param {SupportMessageCreateManyAndReturnArgs} args - Arguments to create many SupportMessages.
   * @example
   * // Create many SupportMessages
   * const supportMessage = await prisma.supportMessage.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many SupportMessages and only return the `id`
   * const supportMessageWithIdOnly = await prisma.supportMessage.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends SupportMessageCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, SupportMessageCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a SupportMessage.
   * @param {SupportMessageDeleteArgs} args - Arguments to delete one SupportMessage.
   * @example
   * // Delete one SupportMessage
   * const SupportMessage = await prisma.supportMessage.delete({
   *   where: {
   *     // ... filter to delete one SupportMessage
   *   }
   * })
   * 
   */
  delete<T extends SupportMessageDeleteArgs>(args: Prisma.SelectSubset<T, SupportMessageDeleteArgs<ExtArgs>>): Prisma.Prisma__SupportMessageClient<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one SupportMessage.
   * @param {SupportMessageUpdateArgs} args - Arguments to update one SupportMessage.
   * @example
   * // Update one SupportMessage
   * const supportMessage = await prisma.supportMessage.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends SupportMessageUpdateArgs>(args: Prisma.SelectSubset<T, SupportMessageUpdateArgs<ExtArgs>>): Prisma.Prisma__SupportMessageClient<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more SupportMessages.
   * @param {SupportMessageDeleteManyArgs} args - Arguments to filter SupportMessages to delete.
   * @example
   * // Delete a few SupportMessages
   * const { count } = await prisma.supportMessage.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends SupportMessageDeleteManyArgs>(args?: Prisma.SelectSubset<T, SupportMessageDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SupportMessages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportMessageUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many SupportMessages
   * const supportMessage = await prisma.supportMessage.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends SupportMessageUpdateManyArgs>(args: Prisma.SelectSubset<T, SupportMessageUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more SupportMessages and returns the data updated in the database.
   * @param {SupportMessageUpdateManyAndReturnArgs} args - Arguments to update many SupportMessages.
   * @example
   * // Update many SupportMessages
   * const supportMessage = await prisma.supportMessage.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more SupportMessages and only return the `id`
   * const supportMessageWithIdOnly = await prisma.supportMessage.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends SupportMessageUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, SupportMessageUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one SupportMessage.
   * @param {SupportMessageUpsertArgs} args - Arguments to update or create a SupportMessage.
   * @example
   * // Update or create a SupportMessage
   * const supportMessage = await prisma.supportMessage.upsert({
   *   create: {
   *     // ... data to create a SupportMessage
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the SupportMessage we want to update
   *   }
   * })
   */
  upsert<T extends SupportMessageUpsertArgs>(args: Prisma.SelectSubset<T, SupportMessageUpsertArgs<ExtArgs>>): Prisma.Prisma__SupportMessageClient<runtime.Types.Result.GetResult<Prisma.$SupportMessagePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of SupportMessages.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportMessageCountArgs} args - Arguments to filter SupportMessages to count.
   * @example
   * // Count the number of SupportMessages
   * const count = await prisma.supportMessage.count({
   *   where: {
   *     // ... the filter for the SupportMessages we want to count
   *   }
   * })
  **/
  count<T extends SupportMessageCountArgs>(
    args?: Prisma.Subset<T, SupportMessageCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SupportMessageCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a SupportMessage.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportMessageAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends SupportMessageAggregateArgs>(args: Prisma.Subset<T, SupportMessageAggregateArgs>): Prisma.PrismaPromise<GetSupportMessageAggregateType<T>>

  /**
   * Group by SupportMessage.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SupportMessageGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends SupportMessageGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SupportMessageGroupByArgs['orderBy'] }
      : { orderBy?: SupportMessageGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, SupportMessageGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSupportMessageGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the SupportMessage model
 */
readonly fields: SupportMessageFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for SupportMessage.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SupportMessageClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  ticket<T extends Prisma.SupportTicketDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.SupportTicketDefaultArgs<ExtArgs>>): Prisma.Prisma__SupportTicketClient<runtime.Types.Result.GetResult<Prisma.$SupportTicketPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  authorUser<T extends Prisma.SupportMessage$authorUserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.SupportMessage$authorUserArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the SupportMessage model
 */
export interface SupportMessageFieldRefs {
  readonly id: Prisma.FieldRef<"SupportMessage", 'String'>
  readonly ticketId: Prisma.FieldRef<"SupportMessage", 'String'>
  readonly message: Prisma.FieldRef<"SupportMessage", 'String'>
  readonly isInternal: Prisma.FieldRef<"SupportMessage", 'Boolean'>
  readonly authorEmail: Prisma.FieldRef<"SupportMessage", 'String'>
  readonly authorId: Prisma.FieldRef<"SupportMessage", 'String'>
  readonly createdAt: Prisma.FieldRef<"SupportMessage", 'DateTime'>
}
    

// Custom InputTypes
/**
 * SupportMessage findUnique
 */
export type SupportMessageFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  /**
   * Filter, which SupportMessage to fetch.
   */
  where: Prisma.SupportMessageWhereUniqueInput
}

/**
 * SupportMessage findUniqueOrThrow
 */
export type SupportMessageFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  /**
   * Filter, which SupportMessage to fetch.
   */
  where: Prisma.SupportMessageWhereUniqueInput
}

/**
 * SupportMessage findFirst
 */
export type SupportMessageFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  /**
   * Filter, which SupportMessage to fetch.
   */
  where?: Prisma.SupportMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of SupportMessages to fetch.
   */
  orderBy?: Prisma.SupportMessageOrderByWithRelationInput | Prisma.SupportMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for SupportMessages.
   */
  cursor?: Prisma.SupportMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` SupportMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` SupportMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of SupportMessages.
   */
  distinct?: Prisma.SupportMessageScalarFieldEnum | Prisma.SupportMessageScalarFieldEnum[]
}

/**
 * SupportMessage findFirstOrThrow
 */
export type SupportMessageFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  /**
   * Filter, which SupportMessage to fetch.
   */
  where?: Prisma.SupportMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of SupportMessages to fetch.
   */
  orderBy?: Prisma.SupportMessageOrderByWithRelationInput | Prisma.SupportMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for SupportMessages.
   */
  cursor?: Prisma.SupportMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` SupportMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` SupportMessages.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of SupportMessages.
   */
  distinct?: Prisma.SupportMessageScalarFieldEnum | Prisma.SupportMessageScalarFieldEnum[]
}

/**
 * SupportMessage findMany
 */
export type SupportMessageFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  /**
   * Filter, which SupportMessages to fetch.
   */
  where?: Prisma.SupportMessageWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of SupportMessages to fetch.
   */
  orderBy?: Prisma.SupportMessageOrderByWithRelationInput | Prisma.SupportMessageOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing SupportMessages.
   */
  cursor?: Prisma.SupportMessageWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` SupportMessages from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` SupportMessages.
   */
  skip?: number
  distinct?: Prisma.SupportMessageScalarFieldEnum | Prisma.SupportMessageScalarFieldEnum[]
}

/**
 * SupportMessage create
 */
export type SupportMessageCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  /**
   * The data needed to create a SupportMessage.
   */
  data: Prisma.XOR<Prisma.SupportMessageCreateInput, Prisma.SupportMessageUncheckedCreateInput>
}

/**
 * SupportMessage createMany
 */
export type SupportMessageCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many SupportMessages.
   */
  data: Prisma.SupportMessageCreateManyInput | Prisma.SupportMessageCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * SupportMessage createManyAndReturn
 */
export type SupportMessageCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * The data used to create many SupportMessages.
   */
  data: Prisma.SupportMessageCreateManyInput | Prisma.SupportMessageCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * SupportMessage update
 */
export type SupportMessageUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  /**
   * The data needed to update a SupportMessage.
   */
  data: Prisma.XOR<Prisma.SupportMessageUpdateInput, Prisma.SupportMessageUncheckedUpdateInput>
  /**
   * Choose, which SupportMessage to update.
   */
  where: Prisma.SupportMessageWhereUniqueInput
}

/**
 * SupportMessage updateMany
 */
export type SupportMessageUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update SupportMessages.
   */
  data: Prisma.XOR<Prisma.SupportMessageUpdateManyMutationInput, Prisma.SupportMessageUncheckedUpdateManyInput>
  /**
   * Filter which SupportMessages to update
   */
  where?: Prisma.SupportMessageWhereInput
  /**
   * Limit how many SupportMessages to update.
   */
  limit?: number
}

/**
 * SupportMessage updateManyAndReturn
 */
export type SupportMessageUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * The data used to update SupportMessages.
   */
  data: Prisma.XOR<Prisma.SupportMessageUpdateManyMutationInput, Prisma.SupportMessageUncheckedUpdateManyInput>
  /**
   * Filter which SupportMessages to update
   */
  where?: Prisma.SupportMessageWhereInput
  /**
   * Limit how many SupportMessages to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * SupportMessage upsert
 */
export type SupportMessageUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  /**
   * The filter to search for the SupportMessage to update in case it exists.
   */
  where: Prisma.SupportMessageWhereUniqueInput
  /**
   * In case the SupportMessage found by the `where` argument doesn't exist, create a new SupportMessage with this data.
   */
  create: Prisma.XOR<Prisma.SupportMessageCreateInput, Prisma.SupportMessageUncheckedCreateInput>
  /**
   * In case the SupportMessage was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SupportMessageUpdateInput, Prisma.SupportMessageUncheckedUpdateInput>
}

/**
 * SupportMessage delete
 */
export type SupportMessageDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
  /**
   * Filter which SupportMessage to delete.
   */
  where: Prisma.SupportMessageWhereUniqueInput
}

/**
 * SupportMessage deleteMany
 */
export type SupportMessageDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which SupportMessages to delete
   */
  where?: Prisma.SupportMessageWhereInput
  /**
   * Limit how many SupportMessages to delete.
   */
  limit?: number
}

/**
 * SupportMessage.authorUser
 */
export type SupportMessage$authorUserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  where?: Prisma.UserWhereInput
}

/**
 * SupportMessage without action
 */
export type SupportMessageDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the SupportMessage
   */
  select?: Prisma.SupportMessageSelect<ExtArgs> | null
  /**
   * Omit specific fields from the SupportMessage
   */
  omit?: Prisma.SupportMessageOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SupportMessageInclude<ExtArgs> | null
}
