"use client";

import type {
	AnalyticsData,
	DashboardStats,
	RecentActivity,
} from "@snapback/shared";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { getInvalidationKeys, queryKeys } from "@/lib/query-keys";

// Dashboard Stats Query
export function useDashboardStats() {
	return useQuery({
		queryKey: queryKeys.dashboard.stats(),
		queryFn: async (): Promise<DashboardStats> => {
			// For now, return mock data since the endpoint doesn't exist yet
			// In production, this would be: return analyticsApi.getDashboardStats();

			await new Promise((resolve) => setTimeout(resolve, 500));

			return {
				totalLicenses: 1247,
				activeLicenses: 1189,
				totalRevenue: 8945.5,
				monthlyRevenue: 1234.75,
				totalCustomers: 892,
				activeDevices: 2156,
				systemHealth: "healthy",
				pendingRefunds: 3,
			};
		},
		staleTime: 2 * 60 * 1000, // 2 minutes
		gcTime: 5 * 60 * 1000, // 5 minutes
	});
}

// Analytics Data Query
export function useAnalyticsData(timeRange = "30d") {
	return useQuery({
		queryKey: queryKeys.dashboard.analytics(timeRange),
		queryFn: async (): Promise<AnalyticsData> => {
			// For now, return mock data since the endpoint doesn't exist yet
			// In production, this would be: return analyticsApi.getAnalyticsData(timeRange);

			await new Promise((resolve) => setTimeout(resolve, 600));

			const baseData = {
				revenue: {
					total: 8945.5,
					monthly: 1234.75,
					growth: 12.5,
					chartData: [
						{ month: "Jan", amount: 1200, date: "2024-01-01" },
						{ month: "Feb", amount: 1450, date: "2024-02-01" },
						{ month: "Mar", amount: 1800, date: "2024-03-01" },
						{ month: "Apr", amount: 1650, date: "2024-04-01" },
						{ month: "May", amount: 2100, date: "2024-05-01" },
						{ month: "Jun", amount: 2150, date: "2024-06-01" },
					],
				},
				licenses: {
					total: 1247,
					monthly: 89,
					growth: 8.3,
					byType: {
						standard: 897,
						extended: 350,
						trial: 0,
					},
				},
				customers: {
					total: 892,
					monthly: 67,
					growth: 15.2,
					retention: 94.5,
					chartData: [
						{
							month: "Jan",
							newCustomers: 45,
							totalCustomers: 678,
							date: "2024-01-01",
						},
						{
							month: "Feb",
							newCustomers: 52,
							totalCustomers: 730,
							date: "2024-02-01",
						},
						{
							month: "Mar",
							newCustomers: 61,
							totalCustomers: 791,
							date: "2024-03-01",
						},
						{
							month: "Apr",
							newCustomers: 48,
							totalCustomers: 839,
							date: "2024-04-01",
						},
						{
							month: "May",
							newCustomers: 53,
							totalCustomers: 892,
							date: "2024-05-01",
						},
						{
							month: "Jun",
							newCustomers: 67,
							totalCustomers: 959,
							date: "2024-06-01",
						},
					],
				},
				devices: {
					total: 2156,
					active: 2089,
					analytics: [
						{ deviceType: "MacBook Pro", count: 1245, percentage: 57.7 },
						{ deviceType: "iMac", count: 456, percentage: 21.1 },
						{ deviceType: "Mac Studio", count: 234, percentage: 10.9 },
						{ deviceType: "MacBook Air", count: 221, percentage: 10.3 },
					],
				},
				refunds: {
					total: 23,
					pending: 3,
					processed: 20,
					amount: 1247.5,
				},
			};

			// Adjust data based on time range
			if (timeRange === "7d") {
				baseData.revenue.chartData = baseData.revenue.chartData.slice(-1);
				baseData.customers.chartData = baseData.customers.chartData.slice(-1);
			} else if (timeRange === "90d") {
				baseData.revenue.chartData = [
					...baseData.revenue.chartData,
					{ month: "Jul", amount: 2300, date: "2024-07-01" },
					{ month: "Aug", amount: 2450, date: "2024-08-01" },
					{ month: "Sep", amount: 2600, date: "2024-09-01" },
				];
			}

			return baseData;
		},
		staleTime: 5 * 60 * 1000, // 5 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes
	});
}

// Recent Activity Query
export function useRecentActivity(limit = 10) {
	return useQuery({
		queryKey: queryKeys.dashboard.recentActivity(limit),
		queryFn: async (): Promise<RecentActivity[]> => {
			// For now, return mock data since the endpoint doesn't exist yet
			// In production, this would be: return analyticsApi.getRecentActivity(limit);

			await new Promise((resolve) => setTimeout(resolve, 400));

			return (
				[
					{
						id: "1",
						type: "license_created" as const,
						description:
							"New Standard license <NAME_EMAIL>",
						timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
						amount: 499,
						email: "<EMAIL>",
					},
					{
						id: "2",
						type: "payment_received" as const,
						description: "Payment received for Extended license",
						timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
						amount: 999,
					},
					{
						id: "3",
						type: "device_added" as const,
						description:
							"New device registered for license ABCD1234EFGH5678IJKL",
						timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
						licenseKey: "ABCD1234EFGH5678IJKL",
					},
					{
						id: "4",
						type: "license_upgraded" as const,
						description: "License upgraded with 2 additional devices",
						timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(),
						amount: 198,
					},
					{
						id: "5",
						type: "refund_requested" as const,
						description: "Refund requested for license WXYZ9876ABCD5432EFGH",
						timestamp: new Date(Date.now() - 1000 * 60 * 240).toISOString(),
						licenseKey: "WXYZ9876ABCD5432EFGH",
					},
				] as RecentActivity[]
			).slice(0, limit);
		},
		staleTime: 1 * 60 * 1000, // 1 minute
		gcTime: 5 * 60 * 1000, // 5 minutes
	});
}

// Combined Dashboard Data Hook
export function useDashboardData(timeRange = "30d") {
	const statsQuery = useDashboardStats();
	const analyticsQuery = useAnalyticsData(timeRange);
	const activityQuery = useRecentActivity();

	return {
		stats: statsQuery.data,
		analytics: analyticsQuery.data,
		recentActivity: activityQuery.data || [],

		// Combined loading state
		loading:
			statsQuery.isLoading ||
			analyticsQuery.isLoading ||
			activityQuery.isLoading,

		// Combined error state
		error:
			statsQuery.error?.message ||
			analyticsQuery.error?.message ||
			activityQuery.error?.message ||
			null,

		// Individual query states for more granular control
		queries: {
			stats: statsQuery,
			analytics: analyticsQuery,
			activity: activityQuery,
		},

		// Refetch all dashboard data
		refetch: () => {
			statsQuery.refetch();
			analyticsQuery.refetch();
			activityQuery.refetch();
		},
	};
}

// Mutation for refreshing dashboard data
export function useRefreshDashboard() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async () => {
			// This could trigger a manual refresh or call a refresh endpoint
			await new Promise((resolve) => setTimeout(resolve, 1000));
		},
		onSuccess: () => {
			// Invalidate all dashboard-related queries
			getInvalidationKeys.onDashboardRefresh().forEach((queryKey) => {
				queryClient.invalidateQueries({ queryKey });
			});
		},
	});
}
