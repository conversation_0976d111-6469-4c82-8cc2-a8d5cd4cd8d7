"use client";

import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import { useCallback, useMemo } from "react";
import type {
	URLStateConfig,
	UseURLStateReturn,
} from "@/types/dashboard-components";

/**
 * URL State Management Hook
 *
 * Manages filter and pagination state in URL parameters for:
 * - Bookmarkable URLs
 * - Shareable links
 * - Browser back/forward navigation
 * - State persistence across page refreshes
 */
export function useURLState(
	defaultValues: URLStateConfig = {},
): UseURLStateReturn {
	const router = useRouter();
	const searchParams = useSearchParams();

	// Parse current URL parameters into state object
	const state = useMemo(() => {
		const currentState: URLStateConfig = { ...defaultValues };

		// Parse all search parameters
		for (const [key, value] of searchParams.entries()) {
			if (key === "page" || key === "limit") {
				// Parse numeric values
				const numValue = Number.parseInt(value, 10);
				if (!Number.isNaN(numValue)) {
					currentState[key] = numValue;
				}
			} else if (key === "sort") {
				// Parse sort string
				currentState[key] = value;
			} else if (key === "order") {
				// Parse order with validation
				if (value === "asc" || value === "desc") {
					currentState[key] = value;
				}
			} else {
				// Parse other filter values
				// Handle boolean values
				if (value === "true") {
					currentState[key] = true;
				} else if (value === "false") {
					currentState[key] = false;
				} else if (value === "" || value === "undefined") {
				} else {
					currentState[key] = value;
				}
			}
		}

		return currentState;
	}, [searchParams, defaultValues]);

	// Update URL with new state - use useRef to avoid recreating the function
	const updateState = useCallback(
		(updates: Partial<URLStateConfig>) => {
			// Get current search params fresh to avoid stale closure
			const currentParams = new URLSearchParams(window.location.search);

			// Apply updates
			for (const [key, value] of Object.entries(updates)) {
				if (value === undefined || value === null || value === "") {
					// Remove parameter if value is empty
					currentParams.delete(key);
				} else {
					// Set parameter value
					currentParams.set(key, String(value));
				}
			}

			// Update URL without page reload
			const newUrl = `${window.location.pathname}?${currentParams.toString()}`;
			router.push(newUrl, { scroll: false });
		},
		[router], // Remove searchParams dependency to prevent infinite loops
	);

	// Reset state to defaults
	const resetState = useCallback(() => {
		const newParams = new URLSearchParams();

		// Only set non-empty default values
		for (const [key, value] of Object.entries(defaultValues)) {
			if (value !== undefined && value !== null && value !== "") {
				newParams.set(key, String(value));
			}
		}

		const newUrl = newParams.toString()
			? `${window.location.pathname}?${newParams.toString()}`
			: window.location.pathname;

		router.push(newUrl, { scroll: false });
	}, [router, defaultValues]);

	return {
		state,
		updateState,
		resetState,
	};
}

/**
 * Specialized hook for pagination state management
 */
export function usePaginationState(defaultPage = 1, defaultLimit = 20) {
	const { state, updateState, resetState } = useURLState({
		page: defaultPage,
		limit: defaultLimit,
	});

	const updatePage = useCallback(
		(page: number) => {
			updateState({ page });
		},
		[updateState],
	);

	const updateLimit = useCallback(
		(limit: number) => {
			// Reset to page 1 when changing page size
			updateState({ limit, page: 1 });
		},
		[updateState],
	);

	return {
		page: state.page || defaultPage,
		limit: state.limit || defaultLimit,
		updatePage,
		updateLimit,
		resetPagination: resetState,
	};
}

/**
 * Specialized hook for filter state management
 */
export function useFilterState<
	T extends Record<string, string | number | boolean | undefined>,
>(defaultFilters: T) {
	const { state, updateState, resetState } = useURLState(defaultFilters);

	const updateFilter = useCallback(
		(key: keyof T, value: T[keyof T]) => {
			// Reset to page 1 when filters change
			updateState({ [key]: value, page: 1 });
		},
		[updateState],
	);

	const clearFilters = useCallback(() => {
		resetState();
	}, [resetState]);

	// Extract filter values (excluding pagination)
	const filters = useMemo(() => {
		// biome-ignore lint: page and limit are intentionally destructured out
		const { page, limit, ...filterValues } = state;
		return filterValues as T;
	}, [state]);

	return {
		filters,
		updateFilter,
		clearFilters,
		// Also expose pagination if needed
		page: state.page || 1,
		limit: state.limit || 20,
	};
}

/**
 * Specialized hook for sorting state management
 */
export function useSortState(
	defaultSort?: string,
	defaultOrder: "asc" | "desc" = "asc",
) {
	const { state, updateState } = useURLState({
		sort: defaultSort,
		order: defaultOrder,
	});

	const updateSort = useCallback(
		(sort: string, order: "asc" | "desc") => {
			updateState({ sort, order, page: 1 }); // Reset to page 1 when sorting
		},
		[updateState],
	);

	const clearSort = useCallback(() => {
		updateState({ sort: undefined, order: undefined });
	}, [updateState]);

	return {
		sort: state.sort as string | undefined,
		order: (state.order as "asc" | "desc") || defaultOrder,
		updateSort,
		clearSort,
	};
}

/**
 * Combined hook for complete dashboard state management
 */
export function useDashboardState<
	T extends Record<string, string | number | boolean | undefined>,
>(
	defaultFilters: T,
	defaultPage = 1,
	defaultLimit = 20,
	defaultSort?: string,
	defaultOrder: "asc" | "desc" = "asc",
) {
	const { state, updateState, resetState } = useURLState({
		...defaultFilters,
		page: defaultPage,
		limit: defaultLimit,
		sort: defaultSort,
		order: defaultOrder,
	});

	// Extract different parts of state
	const { page, limit, sort, order, ...filterValues } = state;

	const updateFilter = useCallback(
		(key: keyof T, value: T[keyof T]) => {
			updateState({ [key]: value, page: 1 });
		},
		[updateState],
	);

	const updatePage = useCallback(
		(newPage: number) => {
			updateState({ page: newPage });
		},
		[updateState],
	);

	const updateLimit = useCallback(
		(newLimit: number) => {
			updateState({ limit: newLimit, page: 1 });
		},
		[updateState],
	);

	const updateSort = useCallback(
		(newSort: string, newOrder: "asc" | "desc") => {
			updateState({ sort: newSort, order: newOrder, page: 1 });
		},
		[updateState],
	);

	return {
		// Filter state
		filters: filterValues as T,
		updateFilter,

		// Pagination state
		page: (page as number) || defaultPage,
		limit: (limit as number) || defaultLimit,
		updatePage,
		updateLimit,

		// Sort state
		sort: sort as string | undefined,
		order: (order as "asc" | "desc") || defaultOrder,
		updateSort,

		// Combined actions
		resetAll: resetState,
	};
}
