"use client";

import type {
	AnalyticsData,
	DashboardStats,
	RecentActivity,
} from "@snapback/shared/types";
import { useEffect, useState } from "react";
import { ApiError } from "@/lib/api";

interface UseDashboardDataReturn {
	stats: DashboardStats | null;
	analytics: AnalyticsData | null;
	recentActivity: RecentActivity[];
	loading: boolean;
	error: string | null;
	refetch: () => Promise<void>;
}

export function useDashboardData(timeRange = "30d"): UseDashboardDataReturn {
	const [stats, setStats] = useState<DashboardStats | null>(null);
	const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
	const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const fetchData = async () => {
		try {
			setLoading(true);
			setError(null);

			// For now, we'll use mock data since the analytics endpoints don't exist yet
			// In a real implementation, these would be actual API calls
			const [statsData, analyticsData, activityData] = await Promise.allSettled(
				[
					getMockDashboardStats(),
					getMockAnalyticsData(timeRange),
					getMockRecentActivity(),
				],
			);

			if (statsData.status === "fulfilled") {
				setStats(statsData.value);
			}

			if (analyticsData.status === "fulfilled") {
				setAnalytics(analyticsData.value);
			}

			if (activityData.status === "fulfilled") {
				setRecentActivity(activityData.value);
			}

			// Check if any requests failed
			const failures = [statsData, analyticsData, activityData].filter(
				(result) => result.status === "rejected",
			);

			if (failures.length > 0) {
				console.warn("Some dashboard data requests failed:", failures);
			}
		} catch (err) {
			console.error("Dashboard data fetch error:", err);
			setError(
				err instanceof ApiError ? err.message : "Failed to load dashboard data",
			);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	return {
		stats,
		analytics,
		recentActivity,
		loading,
		error,
		refetch: fetchData,
	};
}

// Mock data functions - these would be replaced with real API calls
async function getMockDashboardStats(): Promise<DashboardStats> {
	// Simulate API delay
	await new Promise((resolve) => setTimeout(resolve, 500));

	return {
		totalLicenses: 1247,
		activeLicenses: 1189,
		totalRevenue: 8945.5,
		monthlyRevenue: 1234.75,
		totalCustomers: 892,
		activeDevices: 2156,
		systemHealth: "healthy",
		pendingRefunds: 3,
	};
}

async function getMockAnalyticsData(timeRange: string): Promise<AnalyticsData> {
	// Simulate API delay
	await new Promise((resolve) => setTimeout(resolve, 600));

	const baseData = {
		revenue: {
			total: 8945.5,
			monthly: 1234.75,
			growth: 12.5,
			chartData: [
				{ month: "Jan", amount: 1200, date: "2024-01-01" },
				{ month: "Feb", amount: 1450, date: "2024-02-01" },
				{ month: "Mar", amount: 1800, date: "2024-03-01" },
				{ month: "Apr", amount: 1650, date: "2024-04-01" },
				{ month: "May", amount: 2100, date: "2024-05-01" },
				{ month: "Jun", amount: 2150, date: "2024-06-01" },
			],
		},
		licenses: {
			total: 1247,
			monthly: 89,
			growth: 8.3,
			byType: {
				standard: 897,
				extended: 350,
				trial: 0,
			},
		},
		customers: {
			total: 892,
			monthly: 67,
			growth: 15.2,
			retention: 94.5,
			chartData: [
				{
					month: "Jan",
					newCustomers: 45,
					totalCustomers: 678,
					date: "2024-01-01",
				},
				{
					month: "Feb",
					newCustomers: 52,
					totalCustomers: 730,
					date: "2024-02-01",
				},
				{
					month: "Mar",
					newCustomers: 61,
					totalCustomers: 791,
					date: "2024-03-01",
				},
				{
					month: "Apr",
					newCustomers: 48,
					totalCustomers: 839,
					date: "2024-04-01",
				},
				{
					month: "May",
					newCustomers: 53,
					totalCustomers: 892,
					date: "2024-05-01",
				},
				{
					month: "Jun",
					newCustomers: 67,
					totalCustomers: 959,
					date: "2024-06-01",
				},
			],
		},
		devices: {
			total: 2156,
			active: 2089,
			analytics: [
				{ deviceType: "MacBook Pro", count: 1245, percentage: 57.7 },
				{ deviceType: "iMac", count: 456, percentage: 21.1 },
				{ deviceType: "Mac Studio", count: 234, percentage: 10.9 },
				{ deviceType: "MacBook Air", count: 221, percentage: 10.3 },
			],
		},
		refunds: {
			total: 23,
			pending: 3,
			processed: 20,
			amount: 1247.5,
		},
	};

	// Adjust data based on time range
	if (timeRange === "7d") {
		baseData.revenue.chartData = baseData.revenue.chartData.slice(-1);
		baseData.customers.chartData = baseData.customers.chartData.slice(-1);
	} else if (timeRange === "90d") {
		// Extend data for 90 days (simplified)
		baseData.revenue.chartData = [
			...baseData.revenue.chartData,
			{ month: "Jul", amount: 2300, date: "2024-07-01" },
			{ month: "Aug", amount: 2450, date: "2024-08-01" },
			{ month: "Sep", amount: 2600, date: "2024-09-01" },
		];
	}

	return baseData;
}

async function getMockRecentActivity(): Promise<RecentActivity[]> {
	// Simulate API delay
	await new Promise((resolve) => setTimeout(resolve, 400));

	return [
		{
			id: "1",
			type: "license_created",
			description: "New Standard license <NAME_EMAIL>",
			timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
			amount: 499,
			email: "<EMAIL>",
		},
		{
			id: "2",
			type: "payment_received",
			description: "Payment received for Extended license",
			timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago
			amount: 999,
		},
		{
			id: "3",
			type: "device_added",
			description: "New device registered for license ABCD1234EFGH5678IJKL",
			timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
			licenseKey: "ABCD1234EFGH5678IJKL",
		},
		{
			id: "4",
			type: "license_upgraded",
			description: "License upgraded with 2 additional devices",
			timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(), // 3 hours ago
			amount: 198,
		},
		{
			id: "5",
			type: "refund_requested",
			description: "Refund requested for license WXYZ9876ABCD5432EFGH",
			timestamp: new Date(Date.now() - 1000 * 60 * 240).toISOString(), // 4 hours ago
			licenseKey: "WXYZ9876ABCD5432EFGH",
		},
	];
}

// Hook for fetching specific license data
export function useLicenseData(licenseKey?: string) {
	const [license, setLicense] = useState<any>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const fetchLicense = async (key: string) => {
		try {
			setLoading(true);
			setError(null);
			// const data = await analyticsApi.getLicenseStatus(key);
			// setLicense(data);

			// Mock implementation for now
			await new Promise((resolve) => setTimeout(resolve, 300));
			setLicense({
				licenseKey: key,
				licenseType: "standard",
				email: "<EMAIL>",
				maxDevices: 2,
				devicesUsed: 1,
				isActive: true,
			});
		} catch (err) {
			setError(
				err instanceof ApiError ? err.message : "Failed to load license data",
			);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (licenseKey) {
			fetchLicense(licenseKey);
		}
	}, [licenseKey, fetchLicense]);

	return {
		license,
		loading,
		error,
		refetch: () => licenseKey && fetchLicense(licenseKey),
	};
}
