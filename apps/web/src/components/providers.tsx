"use client";

import { QueryProvider } from "./providers/query-provider";
import { SessionProvider } from "./providers/session-provider";
import { ThemeProvider } from "./theme-provider";
import { Toaster } from "./ui/sonner";

export default function Providers({ children }: { children: React.ReactNode }) {
	return (
		<QueryProvider>
			<SessionProvider
				config={{
					checkInterval: 60 * 1000, // Check every minute
					refreshThreshold: 15 * 60 * 1000, // Refresh 15 minutes before expiry
					maxRetries: 3,
				}}
				autoStart={true}
				redirectOnExpiry="/login"
				showNotifications={true}
			>
				<ThemeProvider
					attribute="class"
					defaultTheme="system"
					enableSystem
					disableTransitionOnChange
				>
					{children}
					<Toaster richColors />
				</ThemeProvider>
			</SessionProvider>
		</QueryProvider>
	);
}
