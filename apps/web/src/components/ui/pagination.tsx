"use client";

import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "./button";

export interface PaginationProps {
	currentPage: number;
	totalPages: number;
	onPageChange: (page: number) => void;
	showFirstLast?: boolean;
	showPrevNext?: boolean;
	maxVisiblePages?: number;
	className?: string;
}

export function Pagination({
	currentPage,
	totalPages,
	onPageChange,
	showFirstLast = true,
	showPrevNext = true,
	maxVisiblePages = 5,
	className,
}: PaginationProps) {
	// Calculate which page numbers to show
	const getVisiblePages = () => {
		if (totalPages <= maxVisiblePages) {
			return Array.from({ length: totalPages }, (_, i) => i + 1);
		}

		const half = Math.floor(maxVisiblePages / 2);
		let start = Math.max(1, currentPage - half);
		let end = Math.min(totalPages, start + maxVisiblePages - 1);

		// Adjust start if we're near the end
		if (end - start + 1 < maxVisiblePages) {
			start = Math.max(1, end - maxVisiblePages + 1);
		}

		return Array.from({ length: end - start + 1 }, (_, i) => start + i);
	};

	const visiblePages = getVisiblePages();
	const showStartEllipsis = visiblePages[0] > 2;
	const showEndEllipsis = visiblePages[visiblePages.length - 1] < totalPages - 1;

	if (totalPages <= 1) {
		return null;
	}

	return (
		<nav
			className={cn("flex items-center justify-center space-x-1", className)}
			aria-label="Pagination"
		>
			{/* First page */}
			{showFirstLast && currentPage > 1 && (
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(1)}
					aria-label="Go to first page"
				>
					First
				</Button>
			)}

			{/* Previous page */}
			{showPrevNext && (
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(Math.max(1, currentPage - 1))}
					disabled={currentPage <= 1}
					aria-label="Go to previous page"
				>
					<ChevronLeft className="h-4 w-4" />
				</Button>
			)}

			{/* First page number if not in visible range */}
			{showStartEllipsis && (
				<>
					<Button
						variant="outline"
						size="sm"
						onClick={() => onPageChange(1)}
						aria-label="Go to page 1"
					>
						1
					</Button>
					<span className="flex items-center justify-center w-8 h-8">
						<MoreHorizontal className="h-4 w-4" />
					</span>
				</>
			)}

			{/* Visible page numbers */}
			{visiblePages.map((page) => (
				<Button
					key={page}
					variant={page === currentPage ? "default" : "outline"}
					size="sm"
					onClick={() => onPageChange(page)}
					aria-label={`Go to page ${page}`}
					aria-current={page === currentPage ? "page" : undefined}
				>
					{page}
				</Button>
			))}

			{/* Last page number if not in visible range */}
			{showEndEllipsis && (
				<>
					<span className="flex items-center justify-center w-8 h-8">
						<MoreHorizontal className="h-4 w-4" />
					</span>
					<Button
						variant="outline"
						size="sm"
						onClick={() => onPageChange(totalPages)}
						aria-label={`Go to page ${totalPages}`}
					>
						{totalPages}
					</Button>
				</>
			)}

			{/* Next page */}
			{showPrevNext && (
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
					disabled={currentPage >= totalPages}
					aria-label="Go to next page"
				>
					<ChevronRight className="h-4 w-4" />
				</Button>
			)}

			{/* Last page */}
			{showFirstLast && currentPage < totalPages && (
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(totalPages)}
					aria-label="Go to last page"
				>
					Last
				</Button>
			)}
		</nav>
	);
}

// Pagination info component
export interface PaginationInfoProps {
	currentPage: number;
	totalPages: number;
	totalCount: number;
	itemsPerPage: number;
	className?: string;
}

export function PaginationInfo({
	currentPage,
	totalPages,
	totalCount,
	itemsPerPage,
	className,
}: PaginationInfoProps) {
	const startItem = (currentPage - 1) * itemsPerPage + 1;
	const endItem = Math.min(currentPage * itemsPerPage, totalCount);

	return (
		<div className={cn("text-sm text-muted-foreground", className)}>
			Showing {startItem} to {endItem} of {totalCount} results
		</div>
	);
}

// Combined pagination component with info
export interface PaginationWithInfoProps extends PaginationProps {
	totalCount: number;
	itemsPerPage: number;
	showInfo?: boolean;
}

export function PaginationWithInfo({
	currentPage,
	totalPages,
	totalCount,
	itemsPerPage,
	onPageChange,
	showInfo = true,
	className,
	...paginationProps
}: PaginationWithInfoProps) {
	return (
		<div className={cn("flex items-center justify-between", className)}>
			{showInfo && (
				<PaginationInfo
					currentPage={currentPage}
					totalPages={totalPages}
					totalCount={totalCount}
					itemsPerPage={itemsPerPage}
				/>
			)}
			<Pagination
				currentPage={currentPage}
				totalPages={totalPages}
				onPageChange={onPageChange}
				{...paginationProps}
			/>
		</div>
	);
}
