/**
 * User Invitation Form Component
 * Allows authorized users to send invitations with role selection and validation
 */

"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import type { UserRole } from "@snapback/shared";
import { sendInvitationSchema } from "@snapback/shared";
import type { z } from "zod";

// Create the form input type from the schema
type InvitationFormInput = z.input<typeof sendInvitationSchema>;

import { Loader2, Mail, Send, UserPlus, X } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

import {
	useSendInvitation,
	useUserPermissions,
} from "@/hooks/use-user-management";

interface InvitationFormProps {
	trigger?: React.ReactNode;
	onSuccess?: () => void;
}

const ROLE_DESCRIPTIONS: Record<UserRole, string> = {
	VIEWER: "Read-only access to designated content",
	USER: "Standard user with access to core app features",
	MANAGER: "Can manage team members and content within their scope",
	ADMIN: "Can manage users, content, and most system features",
	SUPER_ADMIN: "Full system access, can manage all users and system settings",
};

export function InvitationForm({ trigger, onSuccess }: InvitationFormProps) {
	const [isOpen, setIsOpen] = useState(false);
	const { assignableRoles, canInviteUsers } = useUserPermissions();
	const sendInvitation = useSendInvitation();

	const form = useForm<InvitationFormInput>({
		resolver: zodResolver(sendInvitationSchema),
		defaultValues: {
			email: "",
			role: "USER",
		},
	});

	const onSubmit = async (data: InvitationFormInput) => {
		try {
			// Ensure role has a default value
			const invitationData = {
				email: data.email,
				role: data.role || ("USER" as UserRole),
			};
			await sendInvitation.mutateAsync(invitationData);
			form.reset();
			setIsOpen(false);
			onSuccess?.();
		} catch (error) {
			// Error handling is done in the mutation hook
			console.error("Invitation submission error:", error);
		}
	};

	// Don't render if user doesn't have permission
	if (!canInviteUsers) {
		return null;
	}

	const defaultTrigger = (
		<Button>
			<UserPlus className="mr-2 h-4 w-4" />
			Invite User
		</Button>
	);

	return (
		<Dialog open={isOpen} onOpenChange={setIsOpen}>
			<DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Mail className="h-5 w-5" />
						Send User Invitation
					</DialogTitle>
					<DialogDescription>
						Invite a new user to join the SnapBack app. They will receive an
						email with instructions to set up their account.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Email Address</FormLabel>
									<FormControl>
										<Input
											type="email"
											placeholder="<EMAIL>"
											{...field}
											disabled={sendInvitation.isPending}
										/>
									</FormControl>
									<FormDescription>
										The email address where the invitation will be sent.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="role"
							render={({ field }) => (
								<FormItem>
									<FormLabel>User Role</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
										disabled={sendInvitation.isPending}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select a role" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{assignableRoles.map((role: UserRole) => (
												<SelectItem key={role} value={role}>
													<div className="flex flex-col">
														<span className="font-medium">{role}</span>
														<span className="text-muted-foreground text-sm">
															{ROLE_DESCRIPTIONS[role]}
														</span>
													</div>
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormDescription>
										Select the role that determines what permissions the user
										will have.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="flex justify-end gap-3 pt-4">
							<Button
								type="button"
								variant="outline"
								onClick={() => setIsOpen(false)}
								disabled={sendInvitation.isPending}
							>
								<X className="mr-2 h-4 w-4" />
								Cancel
							</Button>
							<Button
								type="submit"
								disabled={sendInvitation.isPending}
								className="min-w-[120px]"
							>
								{sendInvitation.isPending ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										Sending...
									</>
								) : (
									<>
										<Send className="mr-2 h-4 w-4" />
										Send Invitation
									</>
								)}
							</Button>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
