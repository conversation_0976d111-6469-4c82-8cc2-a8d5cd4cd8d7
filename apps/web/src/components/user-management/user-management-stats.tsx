/**
 * User Management Stats Component
 * Displays key metrics and statistics for user management
 */

"use client";

import { Clock, Mail, Shield, UserCheck, Users, UserX } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
	useInvitations,
	useUserPermissions,
	useUsers,
} from "@/hooks/use-user-management";

interface UserManagementStatsProps {
	className?: string;
}

export function UserManagementStats({ className }: UserManagementStatsProps) {
	const { canManageUsers, canInviteUsers } = useUserPermissions();

	const { data: usersData, isLoading: usersLoading } = useUsers({
		page: 1,
		limit: 1000, // Get all users for stats
	});

	const { data: invitationsData, isLoading: invitationsLoading } =
		useInvitations({
			limit: 1000, // Get all invitations for stats
		});

	// Don't render if user doesn't have permissions
	if (!canManageUsers && !canInviteUsers) {
		return null;
	}

	const isLoading = usersLoading || invitationsLoading;

	// Calculate stats
	const totalUsers = usersData?.users?.length || 0;
	const activeUsers =
		usersData?.users?.filter((user) => user.isActive)?.length || 0;
	const inactiveUsers = totalUsers - activeUsers;

	const totalInvitations = invitationsData?.invitations?.length || 0;
	const pendingInvitations =
		invitationsData?.invitations?.filter((inv) => inv.status === "PENDING")
			?.length || 0;

	// Role distribution
	const roleStats =
		usersData?.users?.reduce(
			(acc, user) => {
				acc[user.role] = (acc[user.role] || 0) + 1;
				return acc;
			},
			{} as Record<string, number>,
		) || {};

	const adminCount = (roleStats.SUPER_ADMIN || 0) + (roleStats.ADMIN || 0);

	const stats = [
		{
			title: "Total Users",
			value: totalUsers,
			icon: Users,
			description: "All registered users",
			show: canManageUsers,
		},
		{
			title: "Active Users",
			value: activeUsers,
			icon: UserCheck,
			description: "Currently active users",
			show: canManageUsers,
		},
		{
			title: "Inactive Users",
			value: inactiveUsers,
			icon: UserX,
			description: "Deactivated user accounts",
			show: canManageUsers,
		},
		{
			title: "Administrators",
			value: adminCount,
			icon: Shield,
			description: "Super Admins and Admins",
			show: canManageUsers,
		},
		{
			title: "Pending Invitations",
			value: pendingInvitations,
			icon: Clock,
			description: "Awaiting acceptance",
			show: canInviteUsers,
		},
		{
			title: "Total Invitations",
			value: totalInvitations,
			icon: Mail,
			description: "All sent invitations",
			show: canInviteUsers,
		},
	].filter((stat) => stat.show);

	return (
		<div
			className={`grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 ${className}`}
		>
			{stats.map((stat) => {
				const Icon = stat.icon;

				return (
					<Card key={stat.title}>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="font-medium text-sm">
								{stat.title}
							</CardTitle>
							<Icon className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="font-bold text-2xl">
								{isLoading ? (
									<Skeleton className="h-8 w-12" />
								) : (
									stat.value.toLocaleString()
								)}
							</div>
							<p className="text-muted-foreground text-xs">
								{stat.description}
							</p>
						</CardContent>
					</Card>
				);
			})}
		</div>
	);
}
