/**
 * Debug Permissions Component
 * Temporary component to help debug user permissions and access issues
 */

"use client";

import { Badge } from "@/components/ui/badge";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
	useCurrentUser,
	useUserPermissions,
} from "@/hooks/use-user-management";

export function DebugPermissions() {
	const {
		data: currentUser,
		isLoading: userLoading,
		error: userError,
	} = useCurrentUser();
	const {
		canManageUsers,
		canInviteUsers,
		canViewUsers,
		canDeactivateUsers,
		canManageRoles,
		assignableRoles,
		isLoading: permissionsLoading,
	} = useUserPermissions();

	if (userLoading || permissionsLoading) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Loading User Information...</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<Skeleton className="h-4 w-full" />
					<Skeleton className="h-4 w-3/4" />
					<Skeleton className="h-4 w-1/2" />
				</CardContent>
			</Card>
		);
	}

	if (userError) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="text-destructive">Error Loading User</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-muted-foreground">
						{userError instanceof Error
							? userError.message
							: "Unknown error occurred"}
					</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Current User Information</CardTitle>
					<CardDescription>
						Debug information for user permissions
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					{currentUser?.user ? (
						<div className="space-y-2">
							<div>
								<strong>Name:</strong> {currentUser.user.name}
							</div>
							<div>
								<strong>Email:</strong> {currentUser.user.email}
							</div>
							<div>
								<strong>Role:</strong>{" "}
								<Badge variant="outline">{currentUser.user.role}</Badge>
							</div>
							<div>
								<strong>Active:</strong>{" "}
								<Badge
									variant={
										currentUser.user.isActive ? "secondary" : "destructive"
									}
								>
									{currentUser.user.isActive ? "Yes" : "No"}
								</Badge>
							</div>
						</div>
					) : (
						<p className="text-muted-foreground">No user data available</p>
					)}
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>User Permissions</CardTitle>
					<CardDescription>Current user's permission status</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-2 gap-4">
						<div className="flex items-center justify-between">
							<span>Can Manage Users:</span>
							<Badge variant={canManageUsers ? "secondary" : "outline"}>
								{canManageUsers ? "Yes" : "No"}
							</Badge>
						</div>
						<div className="flex items-center justify-between">
							<span>Can Invite Users:</span>
							<Badge variant={canInviteUsers ? "secondary" : "outline"}>
								{canInviteUsers ? "Yes" : "No"}
							</Badge>
						</div>
						<div className="flex items-center justify-between">
							<span>Can View Users:</span>
							<Badge variant={canViewUsers ? "secondary" : "outline"}>
								{canViewUsers ? "Yes" : "No"}
							</Badge>
						</div>
						<div className="flex items-center justify-between">
							<span>Can Deactivate Users:</span>
							<Badge variant={canDeactivateUsers ? "secondary" : "outline"}>
								{canDeactivateUsers ? "Yes" : "No"}
							</Badge>
						</div>
						<div className="flex items-center justify-between">
							<span>Can Manage Roles:</span>
							<Badge variant={canManageRoles ? "secondary" : "outline"}>
								{canManageRoles ? "Yes" : "No"}
							</Badge>
						</div>
					</div>

					<div>
						<strong>Assignable Roles:</strong>
						<div className="mt-2 flex flex-wrap gap-2">
							{assignableRoles.length > 0 ? (
								assignableRoles.map((role) => (
									<Badge key={role} variant="outline">
										{role}
									</Badge>
								))
							) : (
								<span className="text-muted-foreground">None</span>
							)}
						</div>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Raw Permissions Data</CardTitle>
					<CardDescription>
						Full permissions object for debugging
					</CardDescription>
				</CardHeader>
				<CardContent>
					<pre className="overflow-auto rounded bg-muted p-4 text-sm">
						{JSON.stringify(currentUser?.permissions, null, 2)}
					</pre>
				</CardContent>
			</Card>

			{currentUser?.trialInfo && (
				<Card>
					<CardHeader>
						<CardTitle>Trial Information</CardTitle>
						<CardDescription>
							Trial license information if applicable
						</CardDescription>
					</CardHeader>
					<CardContent>
						<pre className="overflow-auto rounded bg-muted p-4 text-sm">
							{JSON.stringify(currentUser.trialInfo, null, 2)}
						</pre>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
