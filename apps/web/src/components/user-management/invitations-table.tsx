/**
 * Pending Invitations Management Component
 * Displays and manages user invitations with status tracking and actions
 */

"use client";

import type { InvitationStatus, UserInvitation } from "@snapback/shared";
import { format } from "date-fns";
import {
	AlertTriangle,
	Calendar,
	CheckCircle,
	Clock,
	Mail,
	RefreshCw,
	Trash2,
	XCircle,
} from "lucide-react";
import { useState } from "react";
import { DataTable } from "@/components/dashboard";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	useInvitations,
	useResendInvitation,
	useRevokeInvitation,
	useUserPermissions,
} from "@/hooks/use-user-management";
import type { TableColumn, TableRowAction } from "@/types/dashboard-components";

interface InvitationsTableProps {
	className?: string;
}

const STATUS_CONFIG: Record<
	InvitationStatus,
	{
		label: string;
		variant: "default" | "secondary" | "destructive" | "outline";
		icon: React.ComponentType<{ className?: string }>;
	}
> = {
	PENDING: {
		label: "Pending",
		variant: "default",
		icon: Clock,
	},
	ACCEPTED: {
		label: "Accepted",
		variant: "secondary",
		icon: CheckCircle,
	},
	EXPIRED: {
		label: "Expired",
		variant: "destructive",
		icon: AlertTriangle,
	},
	REVOKED: {
		label: "Revoked",
		variant: "outline",
		icon: XCircle,
	},
};

export function InvitationsTable({ className }: InvitationsTableProps) {
	const [revokeInvitationId, setRevokeInvitationId] = useState<string | null>(
		null,
	);
	const { canInviteUsers } = useUserPermissions();
	const {
		data: invitationsData,
		isLoading,
		error,
		refetch,
	} = useInvitations({
		limit: 50, // Show more invitations since they're typically fewer than users
	});
	const resendInvitation = useResendInvitation();
	const revokeInvitation = useRevokeInvitation();

	// Table Columns Configuration
	const columns: TableColumn<UserInvitation>[] = [
		{
			key: "email",
			header: "Email",
			render: (value) => <span className="font-medium">{value as string}</span>,
		},
		{
			key: "role",
			header: "Role",
			render: (value) => <Badge variant="outline">{value as string}</Badge>,
		},
		{
			key: "status",
			header: "Status",
			render: (_, invitation) => {
				const statusConfig = STATUS_CONFIG[invitation.status];
				const StatusIcon = statusConfig.icon;
				return (
					<Badge variant={statusConfig.variant} className="gap-1">
						<StatusIcon className="h-3 w-3" />
						{statusConfig.label}
					</Badge>
				);
			},
		},
		{
			key: "expiresAt",
			header: "Expires",
			render: (value) => (
				<div className="flex items-center gap-1 text-muted-foreground text-sm">
					<Calendar className="h-3 w-3" />
					{format(new Date(value as string), "MMM d, yyyy")}
				</div>
			),
		},
		{
			key: "invitedBy",
			header: "Invited By",
			render: (_, invitation) => invitation.invitedBy?.name || "System",
		},
	];

	// Table Row Actions Configuration
	const rowActions: TableRowAction<UserInvitation>[] = [
		{
			label: "Resend Invitation",
			icon: RefreshCw,
			onClick: (invitation) => {
				resendInvitation.mutate(invitation.id);
			},
			hidden: (invitation) => invitation.status !== "PENDING",
		},
		{
			label: "Revoke Invitation",
			icon: Trash2,
			onClick: (invitation) => {
				setRevokeInvitationId(invitation.id);
			},
			variant: "destructive",
			separator: true,
			hidden: (invitation) => {
				const isExpired = new Date(invitation.expiresAt) < new Date();
				return invitation.status !== "PENDING" || isExpired;
			},
		},
	];

	const handleRevokeInvitation = async (invitationId: string) => {
		try {
			await revokeInvitation.mutateAsync(invitationId);
			setRevokeInvitationId(null);
		} catch (error) {
			// Error handling is done in the mutation hook
			console.error("Failed to revoke invitation:", error);
		}
	};

	// Don't render if user doesn't have permission
	if (!canInviteUsers) {
		return null;
	}

	if (error) {
		return (
			<Card className={className}>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Mail className="h-5 w-5" />
						Pending Invitations
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="py-8 text-center">
						<AlertTriangle className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
						<p className="mb-4 text-muted-foreground">
							Failed to load invitations
						</p>
						<Button onClick={() => refetch()} variant="outline">
							<RefreshCw className="mr-2 h-4 w-4" />
							Try Again
						</Button>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<>
			<Card className={className}>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Mail className="h-5 w-5" />
						Pending Invitations
					</CardTitle>
					<CardDescription>
						Manage user invitations and track their status
					</CardDescription>
				</CardHeader>
				<CardContent>
					<DataTable
						data={invitationsData?.invitations || []}
						columns={columns}
						actions={rowActions}
						loading={isLoading}
						emptyMessage="No pending invitations. Invitations you send will appear here."
						emptyIcon={Mail}
					/>
				</CardContent>
			</Card>

			{/* Revoke Confirmation Dialog */}
			<AlertDialog
				open={!!revokeInvitationId}
				onOpenChange={() => setRevokeInvitationId(null)}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Revoke Invitation</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to revoke this invitation? The recipient
							will no longer be able to use the invitation link to join the
							system.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={() =>
								revokeInvitationId && handleRevokeInvitation(revokeInvitationId)
							}
							disabled={revokeInvitation.isPending}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							{revokeInvitation.isPending ? "Revoking..." : "Revoke Invitation"}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
