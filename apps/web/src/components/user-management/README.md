# User Management & Invitation System

A comprehensive React-based user management system with role-based access control (RBAC) and invitation functionality for the SnapBack app.

## Features

### 🔐 Role-Based Access Control
- **5-tier role hierarchy**: SUPER_ADMIN → ADMIN → MANAGER → USER → VIEWER
- **Hierarchical permissions**: Higher roles inherit lower role permissions
- **Dynamic role assignment**: Users can only assign roles they have permission for
- **Permission-based UI**: Components automatically hide/show based on user permissions

### 📧 User Invitation System
- **Email-based invitations** with role selection
- **Invitation status tracking**: PENDING, ACCEPTED, EXPIRED, REVOKED
- **Expiration management**: 7-day expiration with clear indicators
- **Revocation capabilities**: Cancel pending invitations
- **Personal messages**: Optional custom messages in invitations

### 👥 User Management Dashboard
- **User listing** with search and filtering
- **Status management**: Activate/deactivate user accounts
- **Role management**: Change user roles with proper authorization
- **Activity tracking**: Last login and account creation dates
- **Bulk operations**: Efficient management of multiple users

## Components

### Core Components

#### `UserManagementDashboard`
Main dashboard component that combines all user management features.

```tsx
import { UserManagementDashboard } from "@/components/user-management";

export default function AdminPage() {
  return <UserManagementDashboard />;
}
```

#### `InvitationForm`
Modal form for sending user invitations with role selection.

```tsx
import { InvitationForm } from "@/components/user-management";

// With default trigger
<InvitationForm />

// With custom trigger
<InvitationForm 
  trigger={<Button>Custom Invite Button</Button>}
  onSuccess={() => console.log("Invitation sent!")}
/>
```

#### `UsersTable`
Comprehensive table for managing users with filtering and actions.

```tsx
import { UsersTable } from "@/components/user-management";

<UsersTable className="mt-6" />
```

#### `InvitationsTable`
Table for managing pending invitations with status tracking.

```tsx
import { InvitationsTable } from "@/components/user-management";

<InvitationsTable className="mt-6" />
```

#### `UserManagementStats`
Statistics cards showing user and invitation metrics.

```tsx
import { UserManagementStats } from "@/components/user-management";

<UserManagementStats className="mb-6" />
```

### Hooks

#### `useCurrentUser()`
Get current user information and permissions.

```tsx
const { data: currentUser, isLoading } = useCurrentUser();
const user = currentUser?.user;
const permissions = currentUser?.permissions;
```

#### `useUsers(params?)`
List users with pagination and filtering.

```tsx
const { data: usersData, isLoading } = useUsers({
  search: "john",
  role: "ADMIN",
  isActive: true,
  page: 1,
  limit: 20
});
```

#### `useInvitations(params?)`
List invitations with filtering.

```tsx
const { data: invitationsData, isLoading } = useInvitations({
  status: "PENDING",
  page: 1,
  limit: 20
});
```

#### `useSendInvitation()`
Mutation hook for sending invitations.

```tsx
const sendInvitation = useSendInvitation();

const handleSend = async (data) => {
  await sendInvitation.mutateAsync({
    email: "<EMAIL>",
    role: "USER"
  });
};
```

#### `useUserPermissions()`
Get current user's permissions and capabilities.

```tsx
const {
  canManageUsers,
  canInviteUsers,
  canViewUsers,
  assignableRoles,
  isLoading
} = useUserPermissions();
```

## API Integration

### Endpoints Used

- `GET /api/users/me` - Current user info and permissions
- `GET /api/users` - List users with filtering
- `POST /api/users` - Create new user
- `PATCH /api/users/:id` - Update user
- `PATCH /api/users/:id/status` - Toggle user status
- `POST /api/users/invite` - Send invitation
- `DELETE /api/users/invite/:id` - Revoke invitation
- `GET /api/users/invitations` - List invitations

### TanStack Query Integration

All API calls use TanStack Query for:
- **Caching**: Intelligent caching with stale-time configuration
- **Background refetching**: Automatic data updates
- **Optimistic updates**: Immediate UI feedback
- **Error handling**: Consistent error states and retry logic
- **Loading states**: Proper loading indicators

## Permission System

### Role Hierarchy
```
SUPER_ADMIN (Level 4)
├── Full system access
├── Can manage all users and system settings
└── Can assign any role

ADMIN (Level 3)
├── Can manage users and content
├── Cannot access critical system settings
└── Can assign roles up to ADMIN

MANAGER (Level 2)
├── Can manage team members within scope
├── Can invite users and manage content
└── Can assign roles up to USER

USER (Level 1)
├── Standard user access
├── Core app features
└── Cannot manage other users

VIEWER (Level 0)
├── Read-only access
├── Designated content only
└── No management capabilities
```

### Permission Checks

Components automatically check permissions:

```tsx
// Component only renders if user has permission
const { canInviteUsers } = useUserPermissions();

if (!canInviteUsers) {
  return null; // Component doesn't render
}
```

### Role Assignment Rules

- Users can only assign roles **lower than or equal** to their own
- **Exception**: SUPER_ADMIN can assign any role
- **Exception**: ADMIN can assign any role except SUPER_ADMIN
- Role changes require proper authorization and are logged

## Styling & Theming

### Design System
- Uses **shadcn/ui** components for consistency
- **Tailwind CSS** for styling
- **Lucide React** icons throughout
- **Responsive design** for mobile and desktop

### Color Coding
- **SUPER_ADMIN**: Red badge (destructive variant)
- **ADMIN**: Blue badge (default variant)
- **MANAGER**: Gray badge (secondary variant)
- **USER/VIEWER**: Outline badges

### Status Indicators
- **Active users**: Green checkmark
- **Inactive users**: Gray X
- **Pending invitations**: Clock icon
- **Expired invitations**: Warning triangle

## Error Handling

### User Feedback
- **Success messages**: Toast notifications for successful actions
- **Error messages**: Clear error descriptions with actionable advice
- **Loading states**: Skeleton loaders and spinners
- **Empty states**: Helpful messages when no data is available

### Validation
- **Client-side validation**: Zod schemas for form validation
- **Server-side validation**: API endpoint validation
- **Permission validation**: Real-time permission checking

## Security Features

### Access Control
- **Route-level protection**: Pages check permissions before rendering
- **Component-level protection**: Components hide based on permissions
- **API-level protection**: Server validates all requests

### Audit Logging
- **User actions**: All management actions are logged
- **Permission changes**: Role changes are tracked
- **Security events**: Failed authorization attempts logged

## Usage Examples

### Basic Setup
```tsx
// In your admin layout or page
import { UserManagementDashboard } from "@/components/user-management";

export default function AdminUsersPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">User Management</h1>
      <UserManagementDashboard />
    </div>
  );
}
```

### Custom Integration
```tsx
// Custom user management with specific components
import { 
  UsersTable, 
  InvitationForm, 
  UserManagementStats 
} from "@/components/user-management";

export default function CustomUserManagement() {
  return (
    <div className="space-y-6">
      <UserManagementStats />
      
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Team Members</h2>
        <InvitationForm />
      </div>
      
      <UsersTable />
    </div>
  );
}
```

## Best Practices

### Performance
- Use pagination for large user lists
- Implement proper loading states
- Cache user data appropriately
- Debounce search inputs

### Security
- Always validate permissions on both client and server
- Use proper error messages that don't leak information
- Implement rate limiting for sensitive operations
- Log all administrative actions

### User Experience
- Provide clear feedback for all actions
- Use confirmation dialogs for destructive actions
- Show helpful empty states
- Implement proper keyboard navigation

## Troubleshooting

### Common Issues

**Components not rendering**
- Check user permissions with `useUserPermissions()`
- Verify authentication state
- Check console for permission errors

**API calls failing**
- Verify user has required role (MANAGER+ for most operations)
- Check network requests in browser dev tools
- Verify API endpoints are properly configured

**Permissions not updating**
- Check if TanStack Query cache needs invalidation
- Verify user session is current
- Check if role changes require re-authentication
