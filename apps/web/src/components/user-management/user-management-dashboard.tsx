/**
 * User Management Dashboard Component
 * Main dashboard that combines user management and invitation features
 */

"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useUserPermissions } from "@/hooks/use-user-management";
import { InvitationsTable, UserManagementStats, UsersTable } from "./";

interface UserManagementDashboardProps {
	className?: string;
}

export function UserManagementDashboard({
	className,
}: UserManagementDashboardProps) {
	const { canManageUsers, canInviteUsers, isLoading } = useUserPermissions();

	// Show loading state while checking permissions
	if (isLoading) {
		return (
			<div className={`space-y-6 ${className}`}>
				<div className="h-32 animate-pulse rounded-lg bg-muted" />
				<div className="h-96 animate-pulse rounded-lg bg-muted" />
			</div>
		);
	}

	// Don't render if user doesn't have any management permissions
	if (!canManageUsers && !canInviteUsers) {
		return (
			<div className={`py-12 text-center ${className}`}>
				<div className="mx-auto max-w-md">
					<h3 className="mb-2 font-semibold text-lg">Access Restricted</h3>
					<p className="text-muted-foreground">
						You don't have permission to access user management features.
						Contact your administrator if you need access.
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className={`space-y-6 ${className}`}>
			{/* Stats Overview */}
			{/* <UserManagementStats /> */}

			{/* Main Content */}
			<Tabs defaultValue="users" className="space-y-6">
				<TabsList>
					{canManageUsers && <TabsTrigger value="users">Users</TabsTrigger>}
					{canInviteUsers && (
						<TabsTrigger value="invitations">Invitations</TabsTrigger>
					)}
				</TabsList>

				{canManageUsers && (
					<TabsContent value="users" className="space-y-6">
						<UsersTable />
					</TabsContent>
				)}

				{canInviteUsers && (
					<TabsContent value="invitations" className="space-y-6">
						<InvitationsTable />
					</TabsContent>
				)}
			</Tabs>
		</div>
	);
}
