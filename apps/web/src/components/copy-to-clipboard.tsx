"use client";

import { motion } from "framer-motion";
import { Check, Copy } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";

interface CopyToClipboardProps {
	text: string;
	className?: string;
	children?: React.ReactNode;
	variant?: "button" | "inline";
	size?: "sm" | "default" | "lg";
}

export function CopyToClipboard({
	text,
	className = "",
	children,
	variant = "button",
	size = "default",
}: CopyToClipboardProps) {
	const [copied, setCopied] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleCopy = async () => {
		try {
			// Check if clipboard API is available
			if (!navigator.clipboard) {
				throw new Error("Clipboard API not available");
			}

			await navigator.clipboard.writeText(text);
			setCopied(true);
			setError(null);

			// Reset copied state after 2 seconds
			setTimeout(() => setCopied(false), 2000);
		} catch (err) {
			console.error("Failed to copy text:", err);
			setError("Failed to copy");

			// Fallback: try to use the older execCommand method
			try {
				const textArea = document.createElement("textarea");
				textArea.value = text;
				textArea.style.position = "fixed";
				textArea.style.left = "-999999px";
				textArea.style.top = "-999999px";
				document.body.appendChild(textArea);
				textArea.focus();
				textArea.select();
				document.execCommand("copy");
				document.body.removeChild(textArea);

				setCopied(true);
				setError(null);
				setTimeout(() => setCopied(false), 2000);
			} catch (fallbackErr) {
				console.error("Fallback copy method also failed:", fallbackErr);
				setError("Copy not supported");
				setTimeout(() => setError(null), 3000);
			}
		}
	};

	if (variant === "inline") {
		return (
			<div className={`flex items-center gap-2 ${className}`}>
				{children}
				<button
					type="button"
					onClick={handleCopy}
					className="group relative flex items-center justify-center rounded-md p-1.5 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
					aria-label={copied ? "Copied!" : "Copy to clipboard"}
					title={copied ? "Copied!" : "Copy to clipboard"}
				>
					<motion.div
						key={copied ? "check" : "copy"}
						initial={{ scale: 0.8, opacity: 0 }}
						animate={{ scale: 1, opacity: 1 }}
						exit={{ scale: 0.8, opacity: 0 }}
						transition={{ duration: 0.15 }}
					>
						{copied ? (
							<Check className="h-4 w-4 text-green-600" />
						) : (
							<Copy className="h-4 w-4" />
						)}
					</motion.div>

					{/* Tooltip */}
					{(copied || error) && (
						<motion.div
							initial={{ opacity: 0, y: 5 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: 5 }}
							className="-top-8 -translate-x-1/2 absolute left-1/2 whitespace-nowrap rounded bg-gray-900 px-2 py-1 text-white text-xs"
						>
							{error || "Copied!"}
							<div className="-translate-x-1/2 absolute top-full left-1/2 border-2 border-transparent border-t-gray-900" />
						</motion.div>
					)}
				</button>
			</div>
		);
	}

	const sizeClasses = {
		sm: "px-2 py-1 text-sm",
		default: "px-3 py-2",
		lg: "px-4 py-3 text-lg",
	};

	return (
		<Button
			onClick={handleCopy}
			variant="outline"
			size={size}
			className={`relative ${sizeClasses[size]} ${className}`}
			aria-label={copied ? "Copied!" : "Copy to clipboard"}
		>
			<motion.div
				key={copied ? "check" : "copy"}
				initial={{ scale: 0.8, opacity: 0 }}
				animate={{ scale: 1, opacity: 1 }}
				exit={{ scale: 0.8, opacity: 0 }}
				transition={{ duration: 0.15 }}
				className="flex items-center gap-2"
			>
				{copied ? (
					<>
						<Check className="h-4 w-4 text-green-600" />
						<span className="text-green-600">Copied!</span>
					</>
				) : (
					<>
						<Copy className="h-4 w-4" />
						<span>{children || "Copy"}</span>
					</>
				)}
			</motion.div>

			{error && (
				<motion.div
					initial={{ opacity: 0, y: 5 }}
					animate={{ opacity: 1, y: 0 }}
					exit={{ opacity: 0, y: 5 }}
					className="-top-8 -translate-x-1/2 absolute left-1/2 whitespace-nowrap rounded bg-red-600 px-2 py-1 text-white text-xs"
				>
					{error}
					<div className="-translate-x-1/2 absolute top-full left-1/2 border-2 border-transparent border-t-red-600" />
				</motion.div>
			)}
		</Button>
	);
}
