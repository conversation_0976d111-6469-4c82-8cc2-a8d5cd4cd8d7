import { Clock, Code, GraduationCap, Palette, Users } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const benefits = [
	{ icon: Code, text: "Developers juggling multiple projects" },
	{ icon: Palette, text: "Designers switching between clients" },
	{ icon: GraduationCap, text: "Students organizing study sessions" },
	{ icon: Users, text: "Anyone who values their time" },
];

export function WhySnapback() {
	return (
		<section className="px-6 py-24 lg:px-8">
			<div className="mx-auto max-w-6xl">
				<div className="mb-16 text-center">
					<h2 className="mb-6 font-bold text-4xl text-gray-900">
						Why Snapback?
					</h2>
					<p className="mb-4 font-semibold text-2xl text-gray-700">
						Stop losing momentum to window management.
					</p>
				</div>

				<Card className="mb-12 border-0 bg-gray-50 shadow-xl">
					<CardContent className="p-8 lg:p-12">
						<div className="mb-8 text-center">
							<Clock className="mx-auto mb-4 h-16 w-16 text-gray-500" />
							<p className="text-gray-700 text-xl leading-relaxed">
								Every time you switch projects, you waste{" "}
								<span className="font-bold text-gray-900">10+ minutes</span>{" "}
								recreating your workspace. Snapback eliminates that friction —
								so you can focus on what matters.
							</p>
						</div>
					</CardContent>
				</Card>

				<div className="mb-8 text-center">
					<h3 className="mb-8 font-bold text-2xl text-gray-900">
						Perfect for:
					</h3>
				</div>

				<div className="grid gap-6 md:grid-cols-2">
					{benefits.map((benefit) => (
						<Card key={benefit.text} className="border-0 bg-white shadow-lg">
							<CardContent className="p-6">
								<div className="flex items-center gap-4">
									<div className="rounded-lg bg-gray-100 p-3">
										<benefit.icon className="h-6 w-6 text-gray-600" />
									</div>
									<span className="font-medium text-gray-700 text-lg">
										{benefit.text}
									</span>
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		</section>
	);
}
