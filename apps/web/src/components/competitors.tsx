import { Grid3X3, <PERSON>, Zap } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const competitors = [
	{
		icon: Monitor,
		name: "macOS Spaces/Mission Control",
		description: "Create virtual desktops but don't save window arrangements",
	},
	{
		icon: Grid3X3,
		name: "Rectangle/Magnet",
		description: "Snap individual windows but can't save complete workspaces",
	},
	{
		icon: Zap,
		name: "Snap<PERSON>",
		description:
			"Saves and restores your entire work environment with one keystroke",
		highlight: true,
	},
];

export function Competitors() {
	return (
		<section className="bg-gray-50 px-6 py-24 lg:px-8">
			<div className="mx-auto max-w-6xl">
				<div className="mb-16 text-center">
					<h2 className="mb-8 font-bold text-4xl text-gray-900">
						Snapback vs. Everything Else
					</h2>
				</div>

				<div className="mb-12 grid gap-6 md:grid-cols-3">
					{competitors.map((comp) => (
						<Card
							key={comp.name}
							className={`border-0 shadow-lg ${comp.highlight ? "bg-gray-100 ring-2 ring-gray-300" : "bg-white"}`}
						>
							<CardContent className="p-6 text-center">
								<div className="mb-4 flex justify-center">
									<div
										className={`rounded-lg p-3 ${comp.highlight ? "bg-gray-200" : "bg-gray-100"}`}
									>
										<comp.icon
											className={`h-8 w-8 ${comp.highlight ? "text-gray-700" : "text-gray-600"}`}
										/>
									</div>
								</div>
								<h3
									className={`mb-3 font-bold ${comp.highlight ? "text-gray-900" : "text-gray-900"}`}
								>
									{comp.name}
								</h3>
								<p
									className={`text-sm ${comp.highlight ? "text-gray-700" : "text-gray-600"}`}
								>
									{comp.description}
								</p>
							</CardContent>
						</Card>
					))}
				</div>

				<Card className="border-0 bg-gray-900 text-white shadow-xl">
					<CardContent className="p-8 text-center">
						<p className="font-semibold text-xl">
							The only app that remembers your complete workspace context.
						</p>
					</CardContent>
				</Card>
			</div>
		</section>
	);
}
