import {
	IconCreditCard,
	IconDashboard,
	IconGraph,
	IconLicense,
	IconReceipt,
	IconSettings,
	IconUser,
	IconUsers,
} from "@tabler/icons-react";
import {
	Activity,
	BarChart3,
	CreditCard,
	Key,
	Receipt,
	Settings,
	Shield,
	Users,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import type * as React from "react";
import { NavUser } from "@/components/nav-user";
import { Badge } from "@/components/ui/badge";
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
} from "@/components/ui/sidebar";
import { useUserPermissions } from "@/hooks/use-user-management";

// SnapBack navigation configuration
const navigation = [
	{
		name: "Overview",
		href: "/dashboard",
		icon: IconDashboard,
	},
	{
		name: "Licenses",
		href: "/dashboard/licenses",
		icon: IconLicense,
	},
	{
		name: "Customers",
		href: "/dashboard/customers",
		icon: IconUsers,
	},
	{
		name: "Payment & Billing",
		href: "/dashboard/payments",
		icon: IconCreditCard,
	},
	{
		name: "Taxes",
		href: "/dashboard/tax",
		icon: IconReceipt,
		badge: "MX",
	},
	{
		name: "Analytics",
		href: "/dashboard/analytics",
		icon: IconGraph,
	},
	{
		name: "Users",
		href: "/dashboard/users",
		icon: IconUser,
	},
	{
		name: "Settings",
		href: "/dashboard/admin",
		icon: IconSettings,
	},
];

const data = {
	user: {
		name: "shadcn",
		email: "<EMAIL>",
		avatar: "/avatars/shadcn.jpg",
	},
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
	const pathname = usePathname();
	const { canManageUsers, canInviteUsers } = useUserPermissions();

	// Filter navigation items based on permissions
	const filteredNavigation = navigation.filter((item) => {
		// Show User Management only if user has management permissions
		if (item.href === "/dashboard/users") {
			return canManageUsers || canInviteUsers;
		}
		return true;
	});

	return (
		<Sidebar collapsible="offcanvas" {...props}>
			<SidebarHeader>
				<SidebarMenu>
					<SidebarMenuItem>
						<SidebarMenuButton
							asChild
							className="data-[slot=sidebar-menu-button]:!p-1.5"
						>
							<Link href="/dashboard">
								<div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
									<Image
										src="/Snapback.png"
										alt="Snapback Logo"
										width={32}
										height={32}
									/>
								</div>
								<div className="grid flex-1 text-left text-sm leading-tight">
									<span className="truncate font-semibold">SnapBack</span>
									<span className="truncate text-xs">Dashboard</span>
								</div>
							</Link>
						</SidebarMenuButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarHeader>
			<SidebarContent>
				<SidebarGroup>
					<SidebarGroupLabel>Navigation</SidebarGroupLabel>
					<SidebarGroupContent>
						<SidebarMenu>
							{filteredNavigation.map((item) => {
								const Icon = item.icon;
								const isActive =
									pathname === item.href ||
									(item.href !== "/dashboard" &&
										pathname.startsWith(item.href));
								return (
									<SidebarMenuItem key={item.name}>
										<SidebarMenuButton asChild isActive={isActive}>
											<Link
												href={item.href}
												className="flex items-center gap-2"
											>
												<Icon />
												<span>{item.name}</span>
												{item.badge && (
													<Badge variant="secondary" className="ml-auto">
														{item.badge}
													</Badge>
												)}
											</Link>
										</SidebarMenuButton>
									</SidebarMenuItem>
								);
							})}
						</SidebarMenu>
					</SidebarGroupContent>
				</SidebarGroup>
			</SidebarContent>
			<SidebarFooter>
				<NavUser user={data.user} />
			</SidebarFooter>
		</Sidebar>
	);
}
