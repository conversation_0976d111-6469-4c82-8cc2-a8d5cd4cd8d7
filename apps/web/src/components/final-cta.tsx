"use client";

import { motion } from "framer-motion";
import { <PERSON>R<PERSON>, CheckCircle, Clock, Shield, Zap } from "lucide-react";

import { useState } from "react";
import { PurchaseDialog } from "@/components/purchase-dialog";
import { Button } from "@/components/ui/button";

const benefits = [
	{
		icon: Clock,
		text: "Save 10+ minutes daily",
	},
	{
		icon: Zap,
		text: "Instant workspace switching",
	},
	{
		icon: Shield,
		text: "100% private & secure",
	},
];

const stats = [
	{ number: "50K+", label: "Happy Users" },
	{ number: "4.9★", label: "Rating" },
	{ number: "2M+", label: "Workspaces Saved" },
];

export function FinalCTA() {
	const [purchaseDialog, setPurchaseDialog] = useState<{
		isOpen: boolean;
		licenseType: "standard" | "extended";
		price: number;
	}>({
		isOpen: false,
		licenseType: "standard",
		price: 4.99,
	});

	const handlePurchaseClick = (licenseType: "standard" | "extended") => {
		const price = licenseType === "standard" ? 4.99 : 9.99;
		setPurchaseDialog({
			isOpen: true,
			licenseType,
			price,
		});
	};

	const handleClosePurchaseDialog = () => {
		setPurchaseDialog((prev) => ({ ...prev, isOpen: false }));
	};

	return (
		<>
			<PurchaseDialog
				isOpen={purchaseDialog.isOpen}
				onClose={handleClosePurchaseDialog}
				licenseType={purchaseDialog.licenseType}
				price={purchaseDialog.price}
			/>
			<section className="relative overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 px-6 py-24 lg:px-8">
				{/* Background Elements */}
				<div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-20 [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
				<div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 opacity-30">
					<div className="h-96 w-96 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 blur-3xl" />
				</div>
				<div className="absolute top-0 right-0 opacity-20">
					<div className="h-64 w-64 rounded-full bg-gradient-to-r from-cyan-400 to-blue-400 blur-2xl" />
				</div>

				<div className="relative mx-auto max-w-6xl text-center text-white">
					{/* Stats Bar */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6 }}
						viewport={{ once: true }}
						className="mb-12 flex justify-center gap-8 md:gap-16"
					>
						{stats.map((stat, index) => (
							<motion.div
								key={stat.label}
								initial={{ opacity: 0, scale: 0.8 }}
								whileInView={{ opacity: 1, scale: 1 }}
								transition={{ duration: 0.5, delay: index * 0.1 }}
								viewport={{ once: true }}
								className="text-center"
							>
								<div className="font-bold text-2xl text-white md:text-3xl">
									{stat.number}
								</div>
								<div className="text-blue-200 text-sm">{stat.label}</div>
							</motion.div>
						))}
					</motion.div>

					{/* Main Headline */}
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8, delay: 0.2 }}
						viewport={{ once: true }}
						className="mb-8"
					>
						<h2 className="mb-6 font-bold text-4xl leading-tight md:text-6xl">
							Ready to transform your
							<br />
							<span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
								workspace workflow?
							</span>
						</h2>
						<p className="mx-auto max-w-2xl text-blue-100 text-xl leading-8">
							Join thousands of professionals who've already revolutionized
							their productivity with Snapback.
						</p>
					</motion.div>

					{/* Benefits */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6, delay: 0.4 }}
						viewport={{ once: true }}
						className="mb-12 flex flex-wrap justify-center gap-6"
					>
						{benefits.map((benefit, index) => {
							const Icon = benefit.icon;
							return (
								<motion.div
									key={benefit.text}
									initial={{ opacity: 0, x: -20 }}
									whileInView={{ opacity: 1, x: 0 }}
									transition={{ duration: 0.5, delay: index * 0.1 }}
									viewport={{ once: true }}
									className="flex items-center gap-3 rounded-full bg-white/10 px-4 py-2 backdrop-blur-sm"
								>
									<Icon className="h-5 w-5 text-green-400" />
									<span className="font-medium text-sm text-white">
										{benefit.text}
									</span>
								</motion.div>
							);
						})}
					</motion.div>

					{/* CTA Buttons */}
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8, delay: 0.6 }}
						viewport={{ once: true }}
						className="mb-8 flex flex-col gap-4 sm:flex-row sm:justify-center"
					>
						<Button
							size="lg"
							onClick={() => handlePurchaseClick("standard")}
							className="group h-14 bg-gradient-to-r from-blue-500 to-cyan-500 px-8 font-semibold text-lg text-white shadow-2xl transition-all duration-300 hover:scale-105 hover:from-blue-600 hover:to-cyan-600 hover:shadow-blue-500/25"
						>
							Get Snapback - $4.99
							<ArrowRight className="ml-3 h-5 w-5 transition-transform group-hover:translate-x-1" />
						</Button>
					</motion.div>

					{/* Trust Indicators */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6, delay: 0.8 }}
						viewport={{ once: true }}
						className="flex flex-wrap justify-center gap-6 text-blue-200 text-sm"
					>
						<div className="flex items-center gap-2">
							<CheckCircle className="h-4 w-4 text-green-400" />
							<span>Free 14-day trial</span>
						</div>
						<div className="flex items-center gap-2">
							<CheckCircle className="h-4 w-4 text-green-400" />
							<span>No credit card required</span>
						</div>
						<div className="flex items-center gap-2">
							<CheckCircle className="h-4 w-4 text-green-400" />
							<span>30-day money-back guarantee</span>
						</div>
					</motion.div>

					{/* Final Message */}
					<motion.p
						initial={{ opacity: 0 }}
						whileInView={{ opacity: 1 }}
						transition={{ duration: 0.6, delay: 1 }}
						viewport={{ once: true }}
						className="mt-8 text-blue-200/80 text-sm"
					>
						macOS 14.0+ required • Signed & notarized by Apple • 100% secure
					</motion.p>
				</div>
			</section>
		</>
	);
}
