"use client";

import { motion } from "framer-motion";
import { <PERSON>uo<PERSON>, <PERSON>, Users } from "lucide-react";

const testimonials = [
	{
		name: "<PERSON>",
		role: "Senior Developer",
		company: "TechCorp",
		avatar: "/avatars/sarah.jpg",
		content:
			"<PERSON>nap<PERSON> has completely transformed my workflow. I can switch between different project setups instantly. It's like having multiple computers in one.",
		rating: 5,
		verified: true,
	},
	{
		name: "<PERSON>",
		role: "Product Designer",
		company: "DesignStudio",
		avatar: "/avatars/marcus.jpg",
		content:
			"As a designer working with multiple monitors and dozens of apps, <PERSON>nap<PERSON> is a lifesaver. My productivity has increased by at least 30%.",
		rating: 5,
		verified: true,
	},
	{
		name: "<PERSON>",
		role: "Data Scientist",
		company: "DataLabs",
		avatar: "/avatars/emily.jpg",
		content:
			"I love how <PERSON><PERSON><PERSON> remembers not just window positions but also the state of my applications. It's like having a perfect memory for my workspace.",
		rating: 5,
		verified: true,
	},
	{
		name: "<PERSON>",
		role: "Engineering Manager",
		company: "StartupXYZ",
		avatar: "/avatars/david.jpg",
		content:
			"Our entire team uses Snapback now. The ability to share workspace configurations has standardized our development environment.",
		rating: 5,
		verified: true,
	},
	{
		name: "<PERSON> <PERSON>",
		role: "UX Researcher",
		company: "ResearchCo",
		avatar: "/avatars/lisa.jpg",
		content:
			"Snapback eliminated the 10 minutes I used to spend every morning setting up my workspace. Now I'm productive from the moment I sit down.",
		rating: 5,
		verified: true,
	},
	{
		name: "Alex Johnson",
		role: "Full Stack Developer",
		company: "WebAgency",
		avatar: "/avatars/alex.jpg",
		content:
			"The multi-monitor support is flawless. I work with 3 different monitor setups and Snapback handles all of them perfectly.",
		rating: 5,
		verified: true,
	},
];

const companies = [
	{ name: "Apple", logo: "/logos/apple.svg" },
	{ name: "Google", logo: "/logos/google.svg" },
	{ name: "Microsoft", logo: "/logos/microsoft.svg" },
	{ name: "Meta", logo: "/logos/meta.svg" },
	{ name: "Netflix", logo: "/logos/netflix.svg" },
	{ name: "Spotify", logo: "/logos/spotify.svg" },
];

export function Testimonials() {
	return (
		<section className="relative overflow-hidden bg-gray-50 px-6 py-24 lg:px-8">
			{/* Background Pattern */}
			<div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-40 [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />

			<div className="relative mx-auto max-w-7xl">
				{/* Header */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6 }}
					viewport={{ once: true }}
					className="mb-16 text-center"
				>
					<div className="mb-4 inline-flex items-center rounded-full bg-green-100 px-4 py-2 font-medium text-green-700 text-sm">
						<Users className="mr-2 h-4 w-4" />
						Loved by Professionals
					</div>
					<h2 className="mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text font-bold text-4xl text-transparent sm:text-5xl">
						Join thousands of happy users
					</h2>
					<p className="mx-auto max-w-3xl text-gray-600 text-xl leading-8">
						Developers, designers, and professionals worldwide trust Snapback to
						boost their productivity.
					</p>
				</motion.div>

				{/* Testimonials Grid */}
				<div className="mb-16 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
					{testimonials.map((testimonial, index) => (
						<motion.div
							key={testimonial.name}
							initial={{ opacity: 0, y: 20 }}
							whileInView={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.6, delay: index * 0.1 }}
							viewport={{ once: true }}
							className="group relative rounded-2xl bg-white p-6 shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl"
						>
							{/* Quote Icon */}
							<Quote className="mb-4 h-8 w-8 text-blue-500 opacity-50" />

							{/* Content */}
							<p className="mb-6 text-gray-700 leading-relaxed">
								"{testimonial.content}"
							</p>

							{/* Rating */}
							<div className="mb-4 flex items-center gap-1">
								{[...Array(testimonial.rating)].map((_, i) => (
									<Star
										// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
										key={i}
										className="h-4 w-4 fill-yellow-400 text-yellow-400"
									/>
								))}
							</div>

							{/* Author */}
							<div className="flex items-center gap-3">
								<div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500">
									<span className="font-semibold text-sm text-white">
										{testimonial.name
											.split(" ")
											.map((n) => n[0])
											.join("")}
									</span>
								</div>
								<div>
									<div className="flex items-center gap-2">
										<span className="font-semibold text-gray-900">
											{testimonial.name}
										</span>
										{testimonial.verified && (
											<div className="flex h-4 w-4 items-center justify-center rounded-full bg-blue-500">
												<svg
													className="h-2.5 w-2.5 text-white"
													fill="currentColor"
													viewBox="0 0 20 20"
												>
													<title>Verified</title>
													<path
														fillRule="evenodd"
														d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
														clipRule="evenodd"
													/>
												</svg>
											</div>
										)}
									</div>
									<div className="text-gray-500 text-sm">
										{testimonial.role} at {testimonial.company}
									</div>
								</div>
							</div>
						</motion.div>
					))}
				</div>

				{/* Company Logos */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6, delay: 0.4 }}
					viewport={{ once: true }}
					className="text-center"
				>
					<p className="mb-8 font-medium text-gray-500 text-sm">
						Trusted by teams at leading companies
					</p>
					<div className="flex flex-wrap items-center justify-center gap-8 opacity-60">
						{companies.map((company) => (
							<div
								key={company.name}
								className="flex items-center justify-center"
							>
								<span className="font-semibold text-gray-400 text-lg">
									{company.name}
								</span>
							</div>
						))}
					</div>
				</motion.div>
			</div>
		</section>
	);
}
