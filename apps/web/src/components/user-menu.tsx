// import Link from "next/link";
// import { useRouter } from "next/navigation";
// import {
// 	DropdownMenu,
// 	DropdownMenuContent,
// 	DropdownMenuItem,
// 	DropdownMenuLabel,
// 	DropdownMenuSeparator,
// 	DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu";
// import { authClient } from "@/lib/auth-client";
// import { SessionStatusIndicator } from "./session-status";
// import { Button } from "./ui/button";
// import { Skeleton } from "./ui/skeleton";

// export default function UserMenu() {
// 	const router = useRouter();
// 	const { data: session, isPending } = authClient.useSession();

// 	if (isPending) {
// 		return <Skeleton className="h-9 w-24" />;
// 	}

// 	if (!session) {
// 		return (
// 			<Button variant="outline" asChild>
// 				<Link href="/login">Sign In</Link>
// 			</Button>
// 		);
// 	}

// 	return (
// 		<div className="flex items-center gap-3">
// 			<SessionStatusIndicator />
// 			<DropdownMenu>
// 				<DropdownMenuTrigger asChild>
// 					<Button variant="outline">{session.user.name}</Button>
// 				</DropdownMenuTrigger>
// 				<DropdownMenuContent className="bg-card">
// 					<DropdownMenuLabel>My Account</DropdownMenuLabel>
// 					<DropdownMenuSeparator />
// 					<DropdownMenuItem>{session.user.email}</DropdownMenuItem>
// 					<DropdownMenuSeparator />
// 					<DropdownMenuItem asChild>
// 						<Button
// 							variant="destructive"
// 							className="w-full"
// 							onClick={() => {
// 								authClient.signOut({
// 									fetchOptions: {
// 										onSuccess: () => {
// 											router.push("/");
// 										},
// 									},
// 								});
// 							}}
// 						>
// 							Sign Out
// 						</Button>
// 					</DropdownMenuItem>
// 				</DropdownMenuContent>
// 			</DropdownMenu>
// 		</div>
// 	);
// }

"use client";

import { Bell, CreditCard, LogOut, MoreVertical, User } from "lucide-react";
import { useRouter } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	useSidebar,
} from "@/components/ui/sidebar";
import { authClient } from "@/lib/auth-client";
import { SessionStatusIndicator } from "./session-status";
import { Button } from "./ui/button";
import { Skeleton } from "./ui/skeleton";

export default function UserMenu() {
	const router = useRouter();
	const { data: session, isPending } = authClient.useSession();
	const { isMobile } = useSidebar();
	if (isPending) {
		return <Skeleton className="h-9 w-24" />;
	}

	return (
		<SidebarMenu>
			<SidebarMenuItem>
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<SidebarMenuButton
							size="lg"
							className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
						>
							<Avatar className="h-8 w-8 rounded-lg grayscale">
								{/* <AvatarImage src={user.avatar} alt={user.name} /> */}
								<AvatarFallback className="rounded-lg">CN</AvatarFallback>
							</Avatar>
							<div className="grid flex-1 text-left text-sm leading-tight">
								<span className="truncate font-medium">
									{session?.user.name}
								</span>
								<span className="truncate text-muted-foreground text-xs">
									{session?.user.email}
								</span>
							</div>
							<MoreVertical className="ml-auto size-4" />
						</SidebarMenuButton>
					</DropdownMenuTrigger>
					<DropdownMenuContent
						className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
						side={isMobile ? "bottom" : "right"}
						align="end"
						sideOffset={4}
					>
						<DropdownMenuLabel className="p-0 font-normal">
							<div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
								<Avatar className="h-8 w-8 rounded-lg">
									{/* <AvatarImage src={user.avatar} alt={user.name} /> */}
									<AvatarFallback className="rounded-lg">CN</AvatarFallback>
								</Avatar>
								<div className="grid flex-1 text-left text-sm leading-tight">
									<span className="truncate font-medium">
										{session?.user.name}
									</span>
									<span className="truncate text-muted-foreground text-xs">
										{session?.user.email}
									</span>
								</div>
							</div>
						</DropdownMenuLabel>
						<DropdownMenuSeparator />
						<DropdownMenuGroup>
							<DropdownMenuItem>
								<User />
								Account
							</DropdownMenuItem>
							<DropdownMenuItem>
								<CreditCard />
								Billing
							</DropdownMenuItem>
							<DropdownMenuItem>
								<Bell />
								Notifications
							</DropdownMenuItem>
						</DropdownMenuGroup>
						<DropdownMenuSeparator />
						<DropdownMenuItem asChild>
							<Button
								variant="ghost"
								className="w-full"
								onClick={() => {
									authClient.signOut({
										fetchOptions: {
											onSuccess: () => {
												router.push("/");
											},
										},
									});
								}}
							>
								Sign Out
							</Button>
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			</SidebarMenuItem>
		</SidebarMenu>
	);
}
