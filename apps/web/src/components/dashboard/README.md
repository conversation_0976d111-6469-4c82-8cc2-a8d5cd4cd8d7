# Dashboard Components Documentation

## Overview

This directory contains comprehensive reusable dashboard components for the SnapBack app site. These components provide consistent functionality, styling, and user experience across all dashboard views.

## Components

### 1. DashboardHeader
- **File**: `dashboard-header.tsx`
- **Purpose**: Consistent header with title, subtitle, and action buttons
- **Features**: Responsive layout, skeleton loading, action button configurations

### 2. DataTable
- **File**: `data-table.tsx`
- **Purpose**: Responsive data table with sorting and actions
- **Features**: Desktop table + mobile cards, sortable columns, row actions dropdown, custom rendering

### 3. Filters
- **File**: `filters.tsx`
- **Purpose**: Standalone filter controls with URL integration
- **Features**: Search, select, boolean, date range filters, debounced search, clear functionality

### 4. EnhancedPagination
- **File**: `enhanced-pagination.tsx`
- **Purpose**: Comprehensive pagination with TanStack Query integration
- **Features**: Page size selector, navigation controls, pagination info

### 5. Skeletons
- **File**: `skeletons.tsx`
- **Purpose**: Loading states for all dashboard components
- **Features**: Header, table, filter, pagination, and combined skeletons

## URL State Management

### Hook: `useDashboardState`
- **File**: `../hooks/use-url-state.ts`
- **Purpose**: Manages filter and pagination state in URL parameters
- **Benefits**: Bookmarkable URLs, browser navigation support, state persistence

## Consistency Standards

### Pagination Defaults
- **Default page size**: 20 items
- **Maximum page size**: 100 items
- **Page size options**: [10, 20, 50, 100]
- **URL parameters**: `page`, `limit`

### Filter Standards
- **Search debounce**: 300ms
- **URL parameter integration**: All filters stored in URL
- **Clear functionality**: Reset all filters and pagination
- **Filter types**: search, select, boolean, date range

### Column Configuration
- **Sortable columns**: Indicated with visual cues
- **Mobile rendering**: Custom mobile layouts for complex data
- **Consistent badges**: Status, type, and other categorical data
- **Date formatting**: Consistent date display across all tables

### RBAC Integration
- **Permission checks**: Integrated with existing `useUserPermissions()` hook
- **Conditional actions**: Row actions hidden based on permissions
- **Fallback handling**: Graceful degradation for insufficient permissions

## Refactored Dashboard Views

### 1. Customers (`/dashboard/customers`)
- ✅ **Status**: Refactored
- **Features**: Customer management with tax compliance tracking
- **Filters**: Search, country, tax status
- **Actions**: View profile, edit, send email, generate invoice

### 2. Users (`/dashboard/users`)
- ✅ **Status**: Refactored
- **Features**: User management with role-based access control
- **Filters**: Search, role, status
- **Actions**: Edit user, toggle status (permission-based)

### 3. Payments (`/dashboard/payments`)
- ✅ **Status**: Refactored
- **Features**: Payment tracking and billing management
- **Filters**: Search, status
- **Actions**: View details, export, sync with Stripe

### 4. Licenses (`/dashboard/licenses`)
- ✅ **Status**: Refactored
- **Features**: License management and tracking
- **Filters**: Search, email, license type, status, expiry
- **Actions**: View details, export, refresh

### 5. Tax Compliance (`/dashboard/tax`)
- ✅ **Status**: Refactored
- **Features**: SAT-compliant tax reporting for Mexico
- **Filters**: Search by month, status, year
- **Actions**: View details, generate report, export SAT XML, download XML

### 6. Analytics (`/dashboard/analytics`)
- ⚠️ **Status**: Not refactored (chart-focused, no table data)
- **Reason**: Primarily visualization-based, doesn't benefit from table components

## Usage Examples

### Basic Dashboard Page
```typescript
import {
  DashboardHeader,
  DataTable,
  Filters,
  EnhancedPagination,
} from "@/components/dashboard";
import { useDashboardState } from "@/hooks/use-url-state";

export default function MyDashboardPage() {
  const {
    filters,
    updateFilter,
    page,
    limit,
    updatePage,
    updateLimit,
    sort,
    order,
    updateSort,
    resetAll,
  } = useDashboardState(
    { search: "", status: "" },
    1, // default page
    20 // default limit
  );

  // Configure components...
  
  return (
    <div className="space-y-6">
      <DashboardHeader {...headerConfig} />
      <Filters {...filterConfig} />
      <DataTable {...tableConfig} />
      <EnhancedPagination {...paginationConfig} />
    </div>
  );
}
```

### Filter Configuration
```typescript
const filterConfigs: FilterConfig[] = [
  {
    type: "search",
    key: "search",
    placeholder: "Search...",
    icon: Search,
  },
  {
    type: "select",
    key: "status",
    label: "Status",
    placeholder: "All Status",
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
    ],
  },
];
```

### Table Column Configuration
```typescript
const columns: TableColumn<DataType>[] = [
  {
    key: "name",
    header: "Name",
    sortable: true,
    render: (_, item) => (
      <div>
        <div className="font-medium">{item.name}</div>
        <div className="text-muted-foreground text-sm">{item.email}</div>
      </div>
    ),
  },
  {
    key: "status",
    header: "Status",
    render: (value) => (
      <Badge variant={value === "active" ? "default" : "secondary"}>
        {value}
      </Badge>
    ),
  },
];
```

## Benefits

1. **Consistency**: Uniform look and behavior across all dashboard views
2. **Maintainability**: Centralized component logic reduces code duplication
3. **Performance**: Optimized rendering and state management
4. **User Experience**: Consistent interactions, URL state persistence, responsive design
5. **Developer Experience**: Type-safe components with comprehensive documentation

## Migration Guide

When refactoring existing dashboard pages:

1. Replace old header with `DashboardHeader`
2. Replace table implementation with `DataTable`
3. Replace filter sections with `Filters`
4. Replace pagination with `EnhancedPagination`
5. Add URL state management using `useDashboardState`
6. Configure components according to established patterns
7. Test functionality and mobile responsiveness
