/**
 * Dashboard Components Export Index
 *
 * Centralized exports for all reusable dashboard components
 */

// Re-export types for convenience
export type {
	BooleanFilterConfig,
	DashboardHeaderAction,
	DashboardHeaderProps,
	DashboardListViewProps,
	DataTableProps,
	DateRangeFilterConfig,
	EnhancedPaginationProps,
	FilterComponentProps,
	FilterConfig,
	FilterState,
	PaginationConfig,
	PaginationMeta,
	PermissionGatedProps,
	SearchFilterConfig,
	SelectFilterConfig,
	SkeletonFiltersProps,
	SkeletonHeaderProps,
	SkeletonTableProps,
	TableColumn,
	TableRowAction,
	TableSortConfig,
	URLStateConfig,
	UseURLStateReturn,
} from "@/types/dashboard-components";
// Header Components
export {
	createDashboardHeaderConfig,
	DashboardHeader,
	DashboardHeaderSkeleton,
} from "./dashboard-header";
// Table Components
export {
	DataTable,
	DataTableSkeleton,
} from "./data-table";

// Pagination Components
export {
	EnhancedPagination,
	PaginationSkeleton,
} from "./enhanced-pagination";
// Filter Components
export {
	Filters,
	FiltersSkeleton,
} from "./filters";
// Skeleton Components
export {
	ChartSkeleton,
	DashboardHeaderSkeleton as HeaderSkeleton,
	DashboardListSkeleton,
	DataTableSkeleton as TableSkeleton,
	DetailViewSkeleton,
	FiltersSkeleton as FiltersSkeletonComponent,
	FormSkeleton,
	PaginationSkeleton as PaginationSkeletonComponent,
	StatsCardsSkeleton,
} from "./skeletons";
