"use client";

import { ChevronDown, ChevronUp, MoreHorizontal, Package } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import type {
	DataTableProps,
	TableSortConfig,
} from "@/types/dashboard-components";

/**
 * Reusable Data Table Component
 *
 * Features:
 * - Responsive design (desktop table + mobile cards)
 * - Sortable columns with visual indicators
 * - Row actions with dropdown menu
 * - Custom column rendering
 * - Loading states
 * - Empty state handling
 * - NOT wrapped in card containers (standalone)
 */
export function DataTable<T extends Record<string, any>>({
	data,
	columns,
	actions = [],
	loading = false,
	emptyMessage = "No data available",
	emptyIcon: EmptyIcon = Package,
	sortConfig,
	onSort,
	className,
	mobileBreakpoint = "md",
}: DataTableProps<T>) {
	const handleSort = (key: string) => {
		if (!onSort) return;

		const newDirection =
			sortConfig?.key === key && sortConfig.direction === "asc"
				? "desc"
				: "asc";

		onSort({ key, direction: newDirection });
	};

	const getSortIcon = (columnKey: string) => {
		if (sortConfig?.key !== columnKey) return null;
		return sortConfig.direction === "asc" ? (
			<ChevronUp className="ml-1 h-4 w-4" />
		) : (
			<ChevronDown className="ml-1 h-4 w-4" />
		);
	};

	if (loading) {
		return (
			<DataTableSkeleton
				columns={columns.length}
				rows={5}
				showActions={actions.length > 0}
			/>
		);
	}

	if (data.length === 0) {
		return (
			<div className="py-12 text-center">
				<EmptyIcon className="mx-auto h-12 w-12 text-muted-foreground" />
				<h3 className="mt-4 font-semibold text-lg">No Data</h3>
				<p className="mt-2 text-muted-foreground">{emptyMessage}</p>
			</div>
		);
	}

	return (
		<div className={className}>
			{/* Desktop Table View */}
			<div className={`hidden ${mobileBreakpoint}:block`}>
				<div className="overflow-hidden rounded-lg border">
					<Table>
						<TableHeader className="sticky top-0 z-10 bg-muted">
							<TableRow>
								{columns.map((column) => (
									<TableHead
										key={String(column.key)}
										className={cn(
											column.className,
											column.sortable &&
												onSort &&
												"cursor-pointer hover:bg-muted/50",
											column.width && `w-[${column.width}]`,
										)}
										onClick={() =>
											column.sortable && handleSort(String(column.key))
										}
									>
										<div className="flex items-center">
											{column.header}
											{column.sortable && getSortIcon(String(column.key))}
										</div>
									</TableHead>
								))}
								{actions.length > 0 && (
									<TableHead className="w-[50px]">Actions</TableHead>
								)}
							</TableRow>
						</TableHeader>
						<TableBody>
							{data.map((row, index) => (
								<TableRow key={row.id || index}>
									{columns.map((column) => (
										<TableCell
											key={String(column.key)}
											className={column.className}
										>
											{column.render
												? column.render(row[column.key], row, index)
												: row[column.key]}
										</TableCell>
									))}
									{actions.length > 0 && (
										<TableCell>
											<RowActionsDropdown row={row} actions={actions} />
										</TableCell>
									)}
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
			</div>

			{/* Mobile Card View */}
			<div className={`space-y-4 ${mobileBreakpoint}:hidden`}>
				{data.map((row, index) => (
					<Card key={row.id || index} className="p-4">
						<div className="flex items-start justify-between">
							<div className="flex-1 space-y-3">
								{columns.map((column) => {
									if (column.mobileRender) {
										return (
											<div key={String(column.key)}>
												{column.mobileRender(row, index)}
											</div>
										);
									}

									const value = column.render
										? column.render(row[column.key], row, index)
										: row[column.key];

									return (
										<div
											key={String(column.key)}
											className="flex justify-between"
										>
											<span className="font-medium text-muted-foreground text-sm">
												{column.header}:
											</span>
											<span className="text-sm">{value}</span>
										</div>
									);
								})}
							</div>
							{actions.length > 0 && (
								<div className="ml-4">
									<RowActionsDropdown row={row} actions={actions} />
								</div>
							)}
						</div>
					</Card>
				))}
			</div>
		</div>
	);
}

/**
 * Row Actions Dropdown Component
 */
function RowActionsDropdown<T>({
	row,
	actions,
}: {
	row: T;
	actions: DataTableProps<T>["actions"];
}) {
	if (!actions || actions.length === 0) return null;

	const visibleActions = actions.filter(
		(action) => !action.hidden || !action.hidden(row),
	);

	if (visibleActions.length === 0) return null;

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					variant="ghost"
					className="flex size-8 text-muted-foreground data-[state=open]:bg-muted"
					size="icon"
				>
					<MoreHorizontal className="h-4 w-4" />
					<span className="sr-only">Open menu</span>
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuLabel>Actions</DropdownMenuLabel>
				{visibleActions.map((action, index) => {
					const Icon = action.icon;
					const isDisabled = action.disabled ? action.disabled(row) : false;

					return (
						<div key={`${action.label}-${index}`}>
							{action.separator && index > 0 && <DropdownMenuSeparator />}
							<DropdownMenuItem
								onClick={() => !isDisabled && action.onClick(row)}
								disabled={isDisabled}
								className={cn(
									action.variant === "destructive" &&
										"text-destructive focus:text-destructive",
								)}
							>
								{Icon && <Icon className="mr-2 h-4 w-4" />}
								{action.label}
							</DropdownMenuItem>
						</div>
					);
				})}
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

/**
 * Data Table Skeleton Loading Component
 */
export function DataTableSkeleton({
	columns,
	rows,
	showActions = false,
	className,
}: {
	columns: number;
	rows: number;
	showActions?: boolean;
	className?: string;
}) {
	return (
		<div className={className}>
			{/* Desktop Skeleton */}
			<div className="hidden md:block">
				<div className="overflow-hidden rounded-lg border">
					<Table>
						<TableHeader className="sticky top-0 z-10 bg-muted">
							<TableRow>
								{Array.from({ length: columns }).map((_, index) => (
									// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
									<TableHead key={index}>
										<Skeleton className="h-4 w-20" />
									</TableHead>
								))}
								{showActions && (
									<TableHead className="w-[50px]">
										<Skeleton className="h-4 w-16" />
									</TableHead>
								)}
							</TableRow>
						</TableHeader>
						<TableBody>
							{Array.from({ length: rows }).map((_, rowIndex) => (
								// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
								<TableRow key={rowIndex}>
									{Array.from({ length: columns }).map((_, colIndex) => (
										// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
										<TableCell key={colIndex}>
											<Skeleton className="h-4 w-full" />
										</TableCell>
									))}
									{showActions && (
										<TableCell>
											<Skeleton className="h-8 w-8 rounded" />
										</TableCell>
									)}
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
			</div>

			{/* Mobile Skeleton */}
			<div className="space-y-4 md:hidden">
				{Array.from({ length: rows }).map((_, index) => (
					// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
					<Card key={index} className="p-4">
						<div className="space-y-3">
							<Skeleton className="h-4 w-full" />
							<Skeleton className="h-4 w-3/4" />
							<Skeleton className="h-4 w-1/2" />
						</div>
					</Card>
				))}
			</div>
		</div>
	);
}
