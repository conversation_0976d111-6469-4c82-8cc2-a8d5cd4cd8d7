"use client";

import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import type { EnhancedPaginationProps } from "@/types/dashboard-components";

/**
 * Enhanced Pagination Component
 *
 * Features:
 * - Integrates with TanStack Query
 * - Page size selector
 * - Follows 20 items per page default with max 100
 * - Responsive design
 * - Loading states
 * - Pagination info display
 * - Extends existing pagination component
 */
export function EnhancedPagination({
	meta,
	page,
	limit,
	showInfo = true,
	showFirstLast = true,
	showPrevNext = true,
	maxVisiblePages = 5,
	pageSizeOptions = [10, 20, 50, 100],
	onPageChange,
	onPageSizeChange,
	loading = false,
	className,
}: EnhancedPaginationProps) {
	if (loading) {
		return <PaginationSkeleton className={className} />;
	}

	// Don't render if there's only one page and no items
	if (meta.totalCount === 0) {
		return null;
	}

	// Don't render pagination if there's only one page
	if (meta.totalPages <= 1 && !onPageSizeChange) {
		return showInfo ? (
			<div className={cn("flex items-center justify-between", className)}>
				<PaginationInfo meta={meta} />
				<div /> {/* Spacer */}
			</div>
		) : null;
	}

	return (
		<div
			className={cn(
				"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",
				className,
			)}
		>
			{/* Pagination Info */}
			{showInfo && <PaginationInfo meta={meta} />}

			{/* Pagination Controls */}
			<div className="flex items-center gap-4">
				{/* Page Size Selector */}
				{onPageSizeChange && (
					<div className="flex items-center gap-2">
						<span className="text-muted-foreground text-sm">Show:</span>
						<Select
							value={limit.toString()}
							onValueChange={(value) => onPageSizeChange(Number(value))}
						>
							<SelectTrigger className="w-20">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								{pageSizeOptions.map((size) => (
									<SelectItem key={size} value={size.toString()}>
										{size}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				)}

				{/* Page Navigation */}
				{meta.totalPages > 1 && (
					<PaginationNavigation
						currentPage={page}
						totalPages={meta.totalPages}
						onPageChange={onPageChange}
						showFirstLast={showFirstLast}
						showPrevNext={showPrevNext}
						maxVisiblePages={maxVisiblePages}
					/>
				)}
			</div>
		</div>
	);
}

/**
 * Pagination Info Component
 */
function PaginationInfo({ meta }: { meta: EnhancedPaginationProps["meta"] }) {
	const startItem = (meta.page - 1) * meta.limit + 1;
	const endItem = Math.min(meta.page * meta.limit, meta.totalCount);

	return (
		<div className="text-muted-foreground text-sm">
			Showing {startItem} to {endItem} of {meta.totalCount} results
		</div>
	);
}

/**
 * Pagination Navigation Component
 */
function PaginationNavigation({
	currentPage,
	totalPages,
	onPageChange,
	showFirstLast,
	showPrevNext,
	maxVisiblePages,
}: {
	currentPage: number;
	totalPages: number;
	onPageChange: (page: number) => void;
	showFirstLast: boolean;
	showPrevNext: boolean;
	maxVisiblePages: number;
}) {
	// Calculate which page numbers to show
	const getVisiblePages = () => {
		if (totalPages <= maxVisiblePages) {
			return Array.from({ length: totalPages }, (_, i) => i + 1);
		}

		const half = Math.floor(maxVisiblePages / 2);
		let start = Math.max(1, currentPage - half);
		const end = Math.min(totalPages, start + maxVisiblePages - 1);

		// Adjust start if we're near the end
		if (end - start + 1 < maxVisiblePages) {
			start = Math.max(1, end - maxVisiblePages + 1);
		}

		return Array.from({ length: end - start + 1 }, (_, i) => start + i);
	};

	const visiblePages = getVisiblePages();
	const showStartEllipsis = visiblePages[0] > 2;
	const showEndEllipsis =
		visiblePages[visiblePages.length - 1] < totalPages - 1;

	return (
		<nav className="flex items-center space-x-1" aria-label="Pagination">
			{/* First page */}
			{showFirstLast && currentPage > 1 && (
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(1)}
					aria-label="Go to first page"
				>
					First
				</Button>
			)}

			{/* Previous page */}
			{showPrevNext && (
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(Math.max(1, currentPage - 1))}
					disabled={currentPage <= 1}
					aria-label="Go to previous page"
				>
					<ChevronLeft className="h-4 w-4" />
				</Button>
			)}

			{/* First page number if not in visible range */}
			{showStartEllipsis && (
				<>
					<Button
						variant="outline"
						size="sm"
						onClick={() => onPageChange(1)}
						aria-label="Go to page 1"
					>
						1
					</Button>
					<span className="flex h-8 w-8 items-center justify-center">
						<MoreHorizontal className="h-4 w-4" />
					</span>
				</>
			)}

			{/* Visible page numbers */}
			{visiblePages.map((pageNum) => (
				<Button
					key={pageNum}
					variant={pageNum === currentPage ? "default" : "outline"}
					size="sm"
					onClick={() => onPageChange(pageNum)}
					aria-label={`Go to page ${pageNum}`}
					aria-current={pageNum === currentPage ? "page" : undefined}
				>
					{pageNum}
				</Button>
			))}

			{/* Last page number if not in visible range */}
			{showEndEllipsis && (
				<>
					<span className="flex h-8 w-8 items-center justify-center">
						<MoreHorizontal className="h-4 w-4" />
					</span>
					<Button
						variant="outline"
						size="sm"
						onClick={() => onPageChange(totalPages)}
						aria-label={`Go to page ${totalPages}`}
					>
						{totalPages}
					</Button>
				</>
			)}

			{/* Next page */}
			{showPrevNext && (
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
					disabled={currentPage >= totalPages}
					aria-label="Go to next page"
				>
					<ChevronRight className="h-4 w-4" />
				</Button>
			)}

			{/* Last page */}
			{showFirstLast && currentPage < totalPages && (
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(totalPages)}
					aria-label="Go to last page"
				>
					Last
				</Button>
			)}
		</nav>
	);
}

/**
 * Pagination Skeleton Loading Component
 */
export function PaginationSkeleton({ className }: { className?: string }) {
	return (
		<div
			className={cn(
				"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",
				className,
			)}
		>
			{/* Info Skeleton */}
			<Skeleton className="h-4 w-48" />

			{/* Controls Skeleton */}
			<div className="flex items-center gap-4">
				{/* Page Size Selector Skeleton */}
				<div className="flex items-center gap-2">
					<Skeleton className="h-4 w-12" />
					<Skeleton className="h-9 w-20" />
				</div>

				{/* Navigation Skeleton */}
				<div className="flex items-center space-x-1">
					<Skeleton className="h-9 w-16" />
					<Skeleton className="h-9 w-9" />
					<Skeleton className="h-9 w-9" />
					<Skeleton className="h-9 w-9" />
					<Skeleton className="h-9 w-9" />
					<Skeleton className="h-9 w-16" />
				</div>
			</div>
		</div>
	);
}
