"use client";

import { X } from "lucide-react";
import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import type {
	BooleanFilterConfig,
	FilterComponentProps,
	FilterConfig,
	FilterState,
	SearchFilterConfig,
	SelectFilterConfig,
} from "@/types/dashboard-components";

/**
 * Reusable Filter Component
 *
 * Features:
 * - Multiple filter types (search, select, boolean, date range)
 * - Debounced search input
 * - URL parameter integration ready
 * - Responsive grid layout
 * - Clear all filters functionality
 * - Loading states
 * - NOT wrapped in card containers (standalone)
 */
export function Filters({
	filters,
	values,
	onChange,
	onClear,
	loading = false,
	className,
}: FilterComponentProps) {
	if (loading) {
		return (
			<FiltersSkeleton filtersCount={filters.length} className={className} />
		);
	}

	const hasActiveFilters = Object.values(values).some(
		(value) => value !== undefined && value !== "" && value !== null,
	);

	return (
		<div className={cn("space-y-4", className)}>
			{/* Filter Controls Grid */}
			<div className="flex gap-4">
				{filters.map((filter) => (
					<FilterControl
						key={filter.key}
						filter={filter}
						value={values[filter.key]}
						onChange={(value) => onChange(filter.key, value)}
					/>
				))}
			</div>

			{/* Clear Filters Button */}
			{hasActiveFilters && onClear && (
				<div className="flex justify-end">
					<Button
						variant="outline"
						size="sm"
						onClick={onClear}
						className="text-muted-foreground"
					>
						<X className="mr-2 h-4 w-4" />
						Clear Filters
					</Button>
				</div>
			)}
		</div>
	);
}

/**
 * Individual Filter Control Component
 */
function FilterControl({
	filter,
	value,
	onChange,
}: {
	filter: FilterConfig;
	value: string | string[] | boolean | undefined;
	onChange: (value: string | string[] | boolean | undefined) => void;
}) {
	switch (filter.type) {
		case "search":
			return (
				<SearchFilter
					filter={filter}
					value={value as string}
					onChange={onChange as (value: string) => void}
				/>
			);
		case "select":
			return (
				<SelectFilter
					filter={filter}
					value={value as string | string[]}
					onChange={onChange as (value: string | string[]) => void}
				/>
			);
		case "boolean":
			return (
				<BooleanFilter
					filter={filter}
					value={value as boolean | undefined}
					onChange={onChange as (value: boolean | undefined) => void}
				/>
			);
		case "dateRange":
			return (
				<DateRangeFilter filter={filter} value={value} onChange={onChange} />
			);
		default:
			return null;
	}
}

/**
 * Search Filter Component with Debouncing
 */
function SearchFilter({
	filter,
	value,
	onChange,
}: {
	filter: SearchFilterConfig;
	value: string;
	onChange: (value: string) => void;
}) {
	const [localValue, setLocalValue] = useState(value || "");

	// Create a stable debounced function using useRef to avoid infinite loops
	const debouncedOnChangeRef = useRef<ReturnType<typeof debounce> | null>(null);
	const onChangeRef = useRef(onChange);

	// Update the onChange ref when it changes
	onChangeRef.current = onChange;

	// Initialize the debounced function only once
	useEffect(() => {
		debouncedOnChangeRef.current = debounce((newValue: string) => {
			onChangeRef.current(newValue);
		}, filter.debounceMs || 300);

		return () => {
			// Cleanup on unmount
			if (debouncedOnChangeRef.current) {
				debouncedOnChangeRef.current.cancel();
			}
		};
	}, [filter.debounceMs]); // Only depend on debounce time

	// Call debounced function when local value changes
	useEffect(() => {
		if (debouncedOnChangeRef.current) {
			debouncedOnChangeRef.current(localValue);
		}
	}, [localValue]);

	// Update local value when external value changes (but avoid loops)
	useEffect(() => {
		if (value !== localValue) {
			setLocalValue(value || "");
		}
	}, [value]); // Remove localValue from deps to prevent loops

	const Icon = filter.icon;

	return (
		<div className="space-y-2">
			<Label htmlFor={filter.key} className="font-medium text-sm">
				Search
			</Label>
			<div className="relative">
				{Icon && (
					<Icon className="absolute top-2.5 left-2 h-4 w-4 text-muted-foreground" />
				)}
				<Input
					id={filter.key}
					placeholder={filter.placeholder}
					value={localValue}
					onChange={(e) => setLocalValue(e.target.value)}
					className={Icon ? "pl-8" : ""}
				/>
			</div>
		</div>
	);
}

/**
 * Select Filter Component
 */
function SelectFilter({
	filter,
	value,
	onChange,
}: {
	filter: SelectFilterConfig;
	value: string | string[];
	onChange: (value: string | string[]) => void;
}) {
	const Icon = filter.icon;

	return (
		<div className="space-y-2">
			<Label htmlFor={filter.key} className="font-medium text-sm">
				{filter.label}
			</Label>
			<Select
				value={value as string}
				onValueChange={(newValue) =>
					onChange(newValue === "all" ? "" : newValue)
				}
			>
				<SelectTrigger>
					{Icon && <Icon className="mr-2 h-4 w-4" />}
					<SelectValue placeholder={filter.placeholder} />
				</SelectTrigger>
				<SelectContent>
					<SelectItem value="all">All {filter.label}</SelectItem>
					{filter.options.map((option) => {
						const OptionIcon = option.icon;
						return (
							<SelectItem key={option.value} value={option.value}>
								{OptionIcon && <OptionIcon className="mr-2 h-4 w-4" />}
								{option.label}
							</SelectItem>
						);
					})}
				</SelectContent>
			</Select>
		</div>
	);
}

/**
 * Boolean Filter Component
 */
function BooleanFilter({
	filter,
	value,
	onChange,
}: {
	filter: BooleanFilterConfig;
	value: boolean | undefined;
	onChange: (value: boolean | undefined) => void;
}) {
	const Icon = filter.icon;

	return (
		<div className="space-y-2">
			<Label htmlFor={filter.key} className="font-medium text-sm">
				{filter.label}
			</Label>
			<Select
				value={value === undefined ? "all" : value.toString()}
				onValueChange={(newValue) => {
					if (newValue === "all") {
						onChange(undefined);
					} else {
						onChange(newValue === "true");
					}
				}}
			>
				<SelectTrigger>
					{Icon && <Icon className="mr-2 h-4 w-4" />}
					<SelectValue />
				</SelectTrigger>
				<SelectContent>
					<SelectItem value="all">All</SelectItem>
					<SelectItem value="true">{filter.trueLabel}</SelectItem>
					<SelectItem value="false">{filter.falseLabel}</SelectItem>
				</SelectContent>
			</Select>
		</div>
	);
}

/**
 * Date Range Filter Component (Placeholder - would need date picker)
 */
function DateRangeFilter({
	// biome-ignore lint: filter parameter is required for interface consistency
	filter,
	value,
	onChange,
}: {
	filter: FilterConfig;
	value: string | string[] | boolean | undefined;
	onChange: (value: string | string[] | boolean | undefined) => void;
}) {
	// This is a placeholder - in a real implementation, you'd use a date picker
	// like react-day-picker or similar
	const stringValue = typeof value === "string" ? value : "";
	return (
		<div className="space-y-2">
			<Label className="font-medium text-sm">Date Range</Label>
			<Input
				placeholder="Select date range..."
				value={stringValue}
				onChange={(e) => onChange(e.target.value)}
			/>
		</div>
	);
}

/**
 * Filters Skeleton Loading Component
 */
export function FiltersSkeleton({
	filtersCount,
	className,
}: {
	filtersCount: number;
	className?: string;
}) {
	return (
		<div className={cn("space-y-4", className)}>
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
				{Array.from({ length: filtersCount }).map((_, index) => (
					// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
					<div key={index} className="space-y-2">
						<Skeleton className="h-4 w-16" />
						<Skeleton className="h-9 w-full" />
					</div>
				))}
			</div>
		</div>
	);
}

/**
 * Utility function for debouncing with cancel method
 */
// biome-ignore lint: Function signature requires flexible parameter types
function debounce<T extends (...args: any[]) => any>(
	func: T,
	wait: number,
): ((...args: Parameters<T>) => void) & { cancel: () => void } {
	let timeout: NodeJS.Timeout | null = null;

	const debounced = (...args: Parameters<T>) => {
		if (timeout) clearTimeout(timeout);
		timeout = setTimeout(() => func(...args), wait);
	};

	debounced.cancel = () => {
		if (timeout) {
			clearTimeout(timeout);
			timeout = null;
		}
	};

	return debounced;
}
