"use client";

import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import type { DashboardHeaderProps } from "@/types/dashboard-components";

/**
 * Reusable Dashboard Header Component
 *
 * Features:
 * - Responsive layout (stacked on mobile, side-by-side on desktop)
 * - Action buttons with loading states
 * - Skeleton loading state
 * - Consistent spacing and typography
 *
 * Based on existing customer view header pattern but improved for reusability
 */
export function DashboardHeader({
	title,
	subtitle,
	actions = [],
	loading = false,
	className,
}: DashboardHeaderProps) {
	if (loading) {
		return <DashboardHeaderSkeleton className={className} />;
	}

	return (
		<div
			className={cn(
				"flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center",
				className,
			)}
		>
			{/* Title and Subtitle */}
			<div className="min-w-0 flex-1">
				<h1 className="truncate font-bold text-3xl tracking-tight">{title}</h1>
				{subtitle && (
					<p className="mt-1 text-muted-foreground text-sm sm:text-base">
						{subtitle}
					</p>
				)}
			</div>

			{/* Action Buttons */}
			{actions.length > 0 && (
				<div className="flex flex-wrap items-center gap-2 sm:flex-nowrap">
					{actions.map((action, index) => {
						const Icon = action.icon;
						return (
							<Button
								key={`${action.label}-${index}`}
								variant={action.variant || "default"}
								onClick={action.onClick}
								disabled={action.disabled || action.loading}
								className="whitespace-nowrap"
							>
								{action.loading ? (
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								) : (
									Icon && <Icon className="mr-2 h-4 w-4" />
								)}
								{action.label}
							</Button>
						);
					})}
				</div>
			)}
		</div>
	);
}

/**
 * Skeleton loading state for Dashboard Header
 */
export function DashboardHeaderSkeleton({
	showActions = true,
	actionsCount = 1,
	className,
}: {
	showActions?: boolean;
	actionsCount?: number;
	className?: string;
}) {
	return (
		<div
			className={cn(
				"flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center",
				className,
			)}
		>
			{/* Title and Subtitle Skeleton */}
			<div className="min-w-0 flex-1 space-y-2">
				<Skeleton className="h-8 w-64 max-w-full" />
				<Skeleton className="h-4 w-96 max-w-full" />
			</div>

			{/* Action Buttons Skeleton */}
			{showActions && (
				<div className="flex items-center gap-2">
					{Array.from({ length: actionsCount }).map((_, index) => (
						// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
						<Skeleton key={index} className="h-9 w-24" />
					))}
				</div>
			)}
		</div>
	);
}

/**
 * Utility function to create common dashboard header configurations
 */
export const createDashboardHeaderConfig = {
	/**
	 * Standard list view header with add button
	 */
	listView: (
		title: string,
		subtitle: string,
		onAdd: () => void,
		addLabel = "Add New",
	): DashboardHeaderProps => ({
		title,
		subtitle,
		actions: [
			{
				label: addLabel,
				onClick: onAdd,
				variant: "default",
			},
		],
	}),

	/**
	 * Detail view header with back and edit buttons
	 */
	detailView: (
		title: string,
		subtitle: string,
		onBack: () => void,
		onEdit?: () => void,
	): DashboardHeaderProps => ({
		title,
		subtitle,
		actions: [
			{
				label: "Back",
				onClick: onBack,
				variant: "outline",
			},
			...(onEdit
				? [
						{
							label: "Edit",
							onClick: onEdit,
							variant: "default" as const,
						},
					]
				: []),
		],
	}),

	/**
	 * Settings view header with save and cancel buttons
	 */
	settingsView: (
		title: string,
		subtitle: string,
		onSave: () => void,
		onCancel: () => void,
		saveLoading = false,
	): DashboardHeaderProps => ({
		title,
		subtitle,
		actions: [
			{
				label: "Cancel",
				onClick: onCancel,
				variant: "outline",
			},
			{
				label: "Save Changes",
				onClick: onSave,
				variant: "default",
				loading: saveLoading,
			},
		],
	}),
};
