"use client";

import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";

/**
 * Comprehensive Skeleton Loading States
 *
 * Provides consistent loading states for all dashboard components
 * that integrate with TanStack Query loading patterns
 */

/**
 * Complete Dashboard List View Skeleton
 * Combines header, filters, table, and pagination skeletons
 */
export function DashboardListSkeleton({
	showFilters = true,
	filtersCount = 3,
	tableColumns = 5,
	tableRows = 5,
	showActions = true,
	className,
}: {
	showFilters?: boolean;
	filtersCount?: number;
	tableColumns?: number;
	tableRows?: number;
	showActions?: boolean;
	className?: string;
}) {
	return (
		<div className={cn("space-y-6", className)}>
			{/* Header Skeleton */}
			<DashboardHeaderSkeleton showActions={showActions} />

			{/* Filters Skeleton */}
			{showFilters && <FiltersSkeleton filtersCount={filtersCount} />}

			{/* Table Skeleton */}
			<DataTableSkeleton
				columns={tableColumns}
				rows={tableRows}
				showActions={showActions}
			/>

			{/* Pagination Skeleton */}
			<PaginationSkeleton />
		</div>
	);
}

/**
 * Dashboard Header Skeleton
 */
export function DashboardHeaderSkeleton({
	showActions = true,
	actionsCount = 1,
	className,
}: {
	showActions?: boolean;
	actionsCount?: number;
	className?: string;
}) {
	return (
		<div
			className={cn(
				"flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center",
				className,
			)}
		>
			{/* Title and Subtitle Skeleton */}
			<div className="min-w-0 flex-1 space-y-2">
				<Skeleton className="h-8 w-64 max-w-full" />
				<Skeleton className="h-4 w-96 max-w-full" />
			</div>

			{/* Action Buttons Skeleton */}
			{showActions && (
				<div className="flex items-center gap-2">
					{Array.from({ length: actionsCount }).map((_, index) => (
						// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
						<Skeleton key={index} className="h-9 w-24" />
					))}
				</div>
			)}
		</div>
	);
}

/**
 * Filters Skeleton
 */
export function FiltersSkeleton({
	filtersCount,
	className,
}: {
	filtersCount: number;
	className?: string;
}) {
	return (
		<div className={cn("space-y-4", className)}>
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
				{Array.from({ length: filtersCount }).map((_, index) => (
					// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
					<div key={index} className="space-y-2">
						<Skeleton className="h-4 w-16" />
						<Skeleton className="h-9 w-full" />
					</div>
				))}
			</div>
		</div>
	);
}

/**
 * Data Table Skeleton
 */
export function DataTableSkeleton({
	columns,
	rows,
	showActions = false,
	className,
}: {
	columns: number;
	rows: number;
	showActions?: boolean;
	className?: string;
}) {
	return (
		<div className={className}>
			{/* Desktop Table Skeleton */}
			<div className="hidden md:block">
				<Table>
					<TableHeader>
						<TableRow>
							{Array.from({ length: columns }).map((_, index) => (
								// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
								<TableHead key={index}>
									<Skeleton className="h-4 w-20" />
								</TableHead>
							))}
							{showActions && (
								<TableHead className="w-[50px]">
									<Skeleton className="h-4 w-16" />
								</TableHead>
							)}
						</TableRow>
					</TableHeader>
					<TableBody>
						{Array.from({ length: rows }).map((_, rowIndex) => (
							// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
							<TableRow key={rowIndex}>
								{Array.from({ length: columns }).map((_, colIndex) => (
									// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
									<TableCell key={colIndex}>
										<Skeleton className="h-4 w-full" />
									</TableCell>
								))}
								{showActions && (
									<TableCell>
										<Skeleton className="h-8 w-8 rounded" />
									</TableCell>
								)}
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>

			{/* Mobile Cards Skeleton */}
			<div className="space-y-4 md:hidden">
				{Array.from({ length: rows }).map((_, index) => (
					// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
					<Card key={index} className="p-4">
						<div className="flex items-start justify-between">
							<div className="flex-1 space-y-3">
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-4 w-3/4" />
								<Skeleton className="h-4 w-1/2" />
							</div>
							{showActions && (
								<div className="ml-4">
									<Skeleton className="h-8 w-8 rounded" />
								</div>
							)}
						</div>
					</Card>
				))}
			</div>
		</div>
	);
}

/**
 * Pagination Skeleton
 */
export function PaginationSkeleton({ className }: { className?: string }) {
	return (
		<div
			className={cn(
				"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",
				className,
			)}
		>
			{/* Info Skeleton */}
			<Skeleton className="h-4 w-48" />

			{/* Controls Skeleton */}
			<div className="flex items-center gap-4">
				{/* Page Size Selector Skeleton */}
				<div className="flex items-center gap-2">
					<Skeleton className="h-4 w-12" />
					<Skeleton className="h-9 w-20" />
				</div>

				{/* Navigation Skeleton */}
				<div className="flex items-center space-x-1">
					<Skeleton className="h-9 w-16" />
					<Skeleton className="h-9 w-9" />
					<Skeleton className="h-9 w-9" />
					<Skeleton className="h-9 w-9" />
					<Skeleton className="h-9 w-9" />
					<Skeleton className="h-9 w-16" />
				</div>
			</div>
		</div>
	);
}

/**
 * Stats Cards Skeleton (for overview pages)
 */
export function StatsCardsSkeleton({
	count = 4,
	className,
}: {
	count?: number;
	className?: string;
}) {
	return (
		<div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", className)}>
			{Array.from({ length: count }).map((_, index) => (
				// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
				<Card key={index} className="p-6">
					<div className="flex items-center justify-between">
						<div className="space-y-2">
							<Skeleton className="h-4 w-24" />
							<Skeleton className="h-8 w-16" />
						</div>
						<Skeleton className="h-8 w-8 rounded" />
					</div>
				</Card>
			))}
		</div>
	);
}

/**
 * Chart Skeleton (for analytics)
 */
export function ChartSkeleton({
	height = "h-64",
	className,
}: {
	height?: string;
	className?: string;
}) {
	return (
		<Card className={cn("p-6", className)}>
			<div className="space-y-4">
				<div className="flex items-center justify-between">
					<Skeleton className="h-6 w-32" />
					<Skeleton className="h-8 w-24" />
				</div>
				<Skeleton className={cn("w-full", height)} />
			</div>
		</Card>
	);
}

/**
 * Form Skeleton (for modals and forms)
 */
export function FormSkeleton({
	fields = 4,
	showActions = true,
	className,
}: {
	fields?: number;
	showActions?: boolean;
	className?: string;
}) {
	return (
		<div className={cn("space-y-4", className)}>
			{Array.from({ length: fields }).map((_, index) => (
				// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
				<div key={index} className="space-y-2">
					<Skeleton className="h-4 w-24" />
					<Skeleton className="h-9 w-full" />
				</div>
			))}

			{showActions && (
				<div className="flex justify-end gap-2 pt-4">
					<Skeleton className="h-9 w-20" />
					<Skeleton className="h-9 w-24" />
				</div>
			)}
		</div>
	);
}

/**
 * Detail View Skeleton (for individual item pages)
 */
export function DetailViewSkeleton({
	sections = 3,
	className,
}: {
	sections?: number;
	className?: string;
}) {
	return (
		<div className={cn("space-y-6", className)}>
			{/* Header */}
			<DashboardHeaderSkeleton showActions={true} actionsCount={2} />

			{/* Content Sections */}
			{Array.from({ length: sections }).map((_, index) => (
				// biome-ignore lint/suspicious/noArrayIndexKey: There will never be a reordering of the array
				<Card key={index} className="p-6">
					<div className="space-y-4">
						<Skeleton className="h-6 w-32" />
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<div className="space-y-2">
								<Skeleton className="h-4 w-20" />
								<Skeleton className="h-4 w-full" />
							</div>
							<div className="space-y-2">
								<Skeleton className="h-4 w-20" />
								<Skeleton className="h-4 w-full" />
							</div>
						</div>
					</div>
				</Card>
			))}
		</div>
	);
}
