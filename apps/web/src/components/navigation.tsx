"use client";

import { AnimatePresence, motion } from "framer-motion";
import { Download, Menu, X } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
// import UserMenu from "./user-menu";

export function Navigation() {
	const [isScrolled, setIsScrolled] = useState(false);
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

	useEffect(() => {
		const handleScroll = () => {
			setIsScrolled(window.scrollY > 10);
		};

		window.addEventListener("scroll", handleScroll);
		return () => window.removeEventListener("scroll", handleScroll);
	}, []);

	const navItems = [
		{ name: "Features", href: "#features" },
		{ name: "How it Works", href: "#how-it-works" },
		{ name: "Reviews", href: "#testimonials" },
		{ name: "Pricing", href: "#pricing" },
		{ name: "FAQ", href: "#faq" },
	];

	const scrollToSection = (href: string) => {
		const element = document.querySelector(href);
		if (element) {
			element.scrollIntoView({ behavior: "smooth" });
		}
		setIsMobileMenuOpen(false);
	};

	return (
		<>
			<motion.nav
				initial={{ y: -100 }}
				animate={{ y: 0 }}
				transition={{ duration: 0.5 }}
				className={`fixed top-0 z-50 w-full transition-all duration-300 ${
					isScrolled
						? "border-gray-200/50 border-b bg-white/80 shadow-lg backdrop-blur-md"
						: "bg-transparent"
				}`}
			>
				<div className="mx-auto max-w-7xl px-6 lg:px-8">
					<div className="flex h-16 items-center justify-between">
						{/* Logo */}
						<motion.div
							whileHover={{ scale: 1.05 }}
							className="flex items-center space-x-2"
						>
							<Image
								src="/Snapback.png"
								alt="Snapback Logo"
								width={32}
								height={32}
							/>
							<span className="font-bold text-gray-900 text-xl">Snapback</span>
						</motion.div>

						{/* Desktop Navigation */}
						<div className="hidden items-center space-x-8 md:flex">
							{navItems.map((item) => (
								<button
									type="button"
									key={item.name}
									onClick={() => scrollToSection(item.href)}
									className="group relative font-medium text-gray-600 transition-colors duration-200 hover:text-gray-900"
								>
									{item.name}
									<span className="-bottom-1 absolute left-0 h-0.5 w-0 bg-blue-600 transition-all duration-200 group-hover:w-full" />
								</button>
							))}
						</div>

						{/* Desktop CTA */}
						<div className="hidden items-center space-x-4 md:flex">
							<Button
								variant="ghost"
								className="text-gray-600 hover:text-gray-900"
							>
								Sign In
							</Button>
							<Button className="bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl">
								<Download className="mr-2 h-4 w-4" />
								Download
							</Button>
						</div>

						{/* Mobile Menu Button */}
						<button
							type="button"
							onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
							className="rounded-lg p-2 transition-colors hover:bg-gray-100 md:hidden"
						>
							{isMobileMenuOpen ? (
								<X className="h-6 w-6 text-gray-600" />
							) : (
								<Menu className="h-6 w-6 text-gray-600" />
							)}
						</button>
					</div>
				</div>
			</motion.nav>

			{/* Mobile Menu */}
			<AnimatePresence>
				{isMobileMenuOpen && (
					<motion.div
						initial={{ opacity: 0, y: -20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						transition={{ duration: 0.2 }}
						className="fixed top-16 right-0 left-0 z-40 border-gray-200/50 border-b bg-white/95 backdrop-blur-md md:hidden"
					>
						<div className="space-y-4 px-6 py-4">
							{navItems.map((item) => (
								<button
									type="button"
									key={item.name}
									onClick={() => scrollToSection(item.href)}
									className="block w-full py-2 text-left font-medium text-gray-600 transition-colors hover:text-gray-900"
								>
									{item.name}
								</button>
							))}
							<div className="space-y-3 border-gray-200 border-t pt-4">
								<Button
									variant="ghost"
									className="w-full justify-start text-gray-600"
								>
									Sign In
								</Button>
								<Button className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800">
									<Download className="mr-2 h-4 w-4" />
									Download for Mac
								</Button>
								{/* <UserMenu /> */}
							</div>
						</div>
					</motion.div>
				)}
			</AnimatePresence>

			{/* Spacer for fixed navigation */}
			{/* <div className="h-16 bg-transparent" /> */}
		</>
	);
}
