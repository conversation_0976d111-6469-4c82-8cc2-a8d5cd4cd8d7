"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON>, Check, Star, Zap } from "lucide-react";
import { useState } from "react";
import { PurchaseDialog } from "@/components/purchase-dialog";
import { But<PERSON> } from "@/components/ui/button";

const plans = [
	{
		name: "Free",
		price: "$0",
		period: "forever",
		description: "Perfect for single-device workspace management",
		features: [
			"1 device only",
			"Up to 3 saved workspaces",
			"Basic window positioning",
			"Manual workspace switching",
			"Community support",
			"No cloud sync",
			"No import/export",
		],
		cta: "Download Free",
		popular: false,
		icon: Zap,
		color: "from-gray-500 to-gray-600",
	},
	{
		name: "Pro",
		price: "$4.99",
		period: "lifetime",
		description: "Perfect for individual users with up to 2 devices",
		features: [
			"2 devices included",
			"Unlimited saved workspaces",
			"Cloud sync across devices",
			"Import/export workspaces",
			"Advanced window management",
			"Multi-monitor support",
			"Custom keyboard shortcuts",
			"App state preservation",
			"Priority email support",
		],
		cta: "Get Pro",
		popular: true,
		icon: Zap,
		color: "from-blue-500 to-blue-600",
	},
	{
		name: "Add 2 More Devices",
		price: "$4.99",
		period: "one-time",
		description: "Expand your existing license with 2 additional devices",
		features: [
			"Add 2 more devices to existing license",
			"Keep all current premium features",
			"No separate license management",
			"Instant device capacity increase",
			"One-time payment",
		],
		cta: "Expand License",
		popular: false,
		icon: Zap,
		color: "from-green-500 to-green-600",
		isDeviceExpansion: true,
	},
];

export function Pricing() {
	const [purchaseDialog, setPurchaseDialog] = useState<{
		isOpen: boolean;
		licenseType: "pro";
		price: number;
		isDeviceExpansion?: boolean;
		additionalDevices?: number;
	}>({
		isOpen: false,
		licenseType: "pro",
		price: 4.99,
		isDeviceExpansion: false,
		additionalDevices: 2,
	});

	const handlePurchaseClick = (
		licenseType: "pro",
		isDeviceExpansion = false,
		additionalDevices = 2,
	) => {
		const price = 4.99; // Fixed price for pro license or device expansion
		setPurchaseDialog({
			isOpen: true,
			licenseType,
			price,
			isDeviceExpansion,
			additionalDevices,
		});
	};

	const handleClosePurchaseDialog = () => {
		setPurchaseDialog((prev) => ({ ...prev, isOpen: false }));
	};

	return (
		<>
			<PurchaseDialog
				isOpen={purchaseDialog.isOpen}
				onClose={handleClosePurchaseDialog}
				licenseType={purchaseDialog.licenseType}
				price={purchaseDialog.price}
				isDeviceExpansion={purchaseDialog.isDeviceExpansion}
				additionalDevices={purchaseDialog.additionalDevices}
			/>
			<section
				id="pricing"
				className="relative overflow-hidden bg-white px-6 py-24 lg:px-8"
			>
				{/* Background Elements */}
				<div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-purple-50/50" />
				<div className="-translate-x-1/2 absolute top-0 left-1/2 opacity-20">
					<div className="h-96 w-96 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 blur-3xl" />
				</div>

				<div className="relative mx-auto max-w-7xl">
					{/* Header */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6 }}
						viewport={{ once: true }}
						className="mb-16 text-center"
					>
						<div className="mb-4 inline-flex items-center rounded-full bg-blue-100 px-4 py-2 font-medium text-blue-700 text-sm">
							<Star className="mr-2 h-4 w-4" />
							Simple Pricing
						</div>
						<h2 className="mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text font-bold text-4xl text-transparent sm:text-5xl">
							Choose your perfect plan
						</h2>
						<p className="mx-auto max-w-3xl text-gray-600 text-xl leading-8">
							Start free or go pro for the full experience. Simple, transparent
							pricing with no hidden fees.
						</p>
					</motion.div>

					{/* Pricing Cards */}
					<div className="mb-16 grid gap-8 lg:grid-cols-3 lg:items-stretch">
						{plans.map((plan, index) => {
							const Icon = plan.icon;
							return (
								<motion.div
									key={plan.name}
									initial={{ opacity: 0, y: 30 }}
									whileInView={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.6, delay: index * 0.1 }}
									viewport={{ once: true }}
									className={`relative flex flex-col rounded-3xl bg-white p-8 shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
										plan.popular ? "scale-105 ring-2 ring-blue-500" : ""
									}`}
								>
									{/* Popular Badge */}
									{plan.popular && (
										<div className="-top-4 -translate-x-1/2 absolute left-1/2">
											<div className="rounded-full bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-1 font-medium text-sm text-white shadow-lg">
												Most Popular
											</div>
										</div>
									)}

									{/* Icon */}
									<div
										className={`mb-6 inline-flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-r ${plan.color} shadow-lg`}
									>
										<Icon className="h-6 w-6 text-white" />
									</div>

									{/* Plan Info */}
									<div className="mb-6">
										<h3 className="mb-2 font-bold text-2xl text-gray-900">
											{plan.name}
										</h3>
										<div className="mb-2 flex items-baseline gap-1">
											<span className="font-bold text-4xl text-gray-900">
												{plan.price}
											</span>
											<span className="text-gray-500">/{plan.period}</span>
										</div>
										<p className="text-gray-600">{plan.description}</p>
									</div>

									{/* Features */}
									<ul className="mb-8 flex-grow space-y-3">
										{plan.features.map((feature) => (
											<li key={feature} className="flex items-start gap-3">
												<Check className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-500" />
												<span className="text-gray-700">{feature}</span>
											</li>
										))}
									</ul>

									{/* CTA Button - Fixed at bottom */}
									<div className="mt-auto">
										<Button
											onClick={() => {
												if (plan.name === "Pro") {
													handlePurchaseClick("pro", false, 2);
												} else if (plan.isDeviceExpansion) {
													handlePurchaseClick("pro", true, 2);
												} else {
													// Free plan - redirect to download
													window.open("#", "_blank"); // Replace with actual download link
												}
											}}
											className={`h-12 w-full font-semibold text-lg transition-all duration-200 ${
												plan.popular
													? "bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg hover:from-blue-700 hover:to-blue-800 hover:shadow-xl"
													: "bg-gray-100 text-gray-900 hover:bg-gray-200"
											}`}
										>
											{plan.cta}
											<ArrowRight className="ml-2 h-5 w-5" />
										</Button>
									</div>
								</motion.div>
							);
						})}
					</div>

					{/* Money Back Guarantee */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6, delay: 0.4 }}
						viewport={{ once: true }}
						className="mb-16 text-center"
					>
						<div className="mx-auto max-w-2xl rounded-2xl bg-green-50 p-8">
							<div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
								<Check className="h-6 w-6 text-green-600" />
							</div>
							<h3 className="mb-2 font-bold text-gray-900 text-xl">
								30-Day Money-Back Guarantee
							</h3>
							<p className="text-gray-600">
								Not satisfied? Get a full refund within 30 days, no questions
								asked.
							</p>
						</div>
					</motion.div>
				</div>
			</section>
		</>
	);
}
