"use client";

import { motion } from "framer-motion";
import {
	ArrowR<PERSON>,
	CheckCircle,
	Pause,
	Play,
	RotateCcw,
	Save,
	Settings,
} from "lucide-react";
import { useState } from "react";

const steps = [
	{
		number: "01",
		title: "Arrange Your Perfect Workspace",
		description:
			"Set up your windows, applications, and tools exactly how you want them. Position everything for maximum productivity.",
		icon: Settings,
		details: [
			"Open your favorite apps",
			"Position windows precisely",
			"Adjust sizes and layouts",
			"Create your ideal setup",
		],
		color: "from-blue-500 to-cyan-500",
	},
	{
		number: "02",
		title: "Capture with One Shortcut",
		description:
			"Press your custom keyboard shortcut to instantly save the entire workspace state, including window positions and app states.",
		icon: Save,
		details: [
			"Press your custom shortcut",
			"Snapback captures everything",
			"Window positions saved",
			"App states preserved",
		],
		color: "from-purple-500 to-pink-500",
	},
	{
		number: "03",
		title: "Restore in Milliseconds",
		description:
			"Whenever you need your workspace back, just use your shortcut. Everything returns to exactly where it was.",
		icon: RotateCcw,
		details: [
			"Use shortcut anytime",
			"Instant restoration",
			"Pixel-perfect positioning",
			"Ready to work immediately",
		],
		color: "from-green-500 to-emerald-500",
	},
];

export function HowItWorks() {
	const [activeStep, setActiveStep] = useState(0);
	const [isPlaying, setIsPlaying] = useState(false);

	return (
		<section
			id="how-it-works"
			className="relative overflow-hidden bg-white px-6 py-24 lg:px-8"
		>
			{/* Background Elements */}
			<div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-blue-50/30" />
			<div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 opacity-10">
				<div className="h-96 w-96 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 blur-3xl" />
			</div>

			<div className="relative mx-auto max-w-7xl">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6 }}
					viewport={{ once: true }}
					className="mb-16 text-center"
				>
					<div className="mb-4 inline-flex items-center rounded-full bg-green-100 px-4 py-2 font-medium text-green-700 text-sm">
						<Play className="mr-2 h-4 w-4" />
						How It Works
					</div>
					<h2 className="mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text font-bold text-4xl text-transparent sm:text-5xl">
						Three steps to workspace perfection
					</h2>
					<p className="mx-auto max-w-3xl text-gray-600 text-xl leading-8">
						Snapback makes workspace management effortless. Set up once, restore
						instantly, work productively.
					</p>
				</motion.div>

				{/* Interactive Steps */}
				<div className="grid gap-8 lg:grid-cols-3">
					{steps.map((step, index) => {
						const Icon = step.icon;
						const isActive = activeStep === index;

						return (
							<motion.div
								key={step.number}
								initial={{ opacity: 0, y: 30 }}
								whileInView={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.6, delay: index * 0.2 }}
								viewport={{ once: true }}
								onHoverStart={() => setActiveStep(index)}
								className={`group relative cursor-pointer rounded-3xl p-8 transition-all duration-300 ${
									isActive
										? "scale-105 border-2 border-blue-200 bg-white shadow-2xl"
										: "bg-white/80 shadow-lg hover:scale-102 hover:shadow-xl"
								}`}
							>
								{/* Step Number */}
								<div
									className={`mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r ${step.color} shadow-lg`}
								>
									<span className="font-bold text-2xl text-white">
										{step.number}
									</span>
								</div>

								{/* Icon */}
								<div className="mb-4">
									<Icon
										className={`h-8 w-8 transition-colors ${isActive ? "text-blue-600" : "text-gray-400"}`}
									/>
								</div>

								{/* Content */}
								<h3 className="mb-4 font-bold text-gray-900 text-xl">
									{step.title}
								</h3>
								<p className="mb-6 text-gray-600 leading-relaxed">
									{step.description}
								</p>

								{/* Details List */}
								<ul className="space-y-3">
									{step.details.map((detail, i) => (
										<motion.li
											key={detail}
											initial={{ opacity: 0, x: -10 }}
											animate={{
												opacity: isActive ? 1 : 0.7,
												x: isActive ? 0 : -10,
											}}
											transition={{ duration: 0.3, delay: i * 0.1 }}
											className="flex items-center text-gray-500 text-sm"
										>
											<CheckCircle
												className={`mr-3 h-4 w-4 ${isActive ? "text-green-500" : "text-gray-300"}`}
											/>
											{detail}
										</motion.li>
									))}
								</ul>

								{/* Arrow for non-last items */}
								{index < steps.length - 1 && (
									<div className="-right-4 -translate-y-1/2 absolute top-1/2 hidden lg:block">
										<ArrowRight className="h-8 w-8 text-gray-300" />
									</div>
								)}

								{/* Active Indicator */}
								{isActive && (
									<motion.div
										layoutId="activeIndicator"
										className="absolute inset-0 rounded-3xl border-2 border-blue-400 bg-blue-50/20"
										transition={{ type: "spring", duration: 0.6 }}
									/>
								)}
							</motion.div>
						);
					})}
				</div>

				{/* Demo Video Placeholder */}
				<motion.div
					initial={{ opacity: 0, y: 40 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.4 }}
					viewport={{ once: true }}
					className="mt-20"
				>
					<div className="relative mx-auto max-w-4xl">
						<div className="-inset-4 absolute rounded-3xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 blur-2xl" />
						<div className="relative overflow-hidden rounded-2xl bg-gray-900 shadow-2xl">
							<div className="flex items-center gap-2 bg-gray-800 px-4 py-3">
								<div className="h-3 w-3 rounded-full bg-red-500" />
								<div className="h-3 w-3 rounded-full bg-yellow-500" />
								<div className="h-3 w-3 rounded-full bg-green-500" />
								<span className="ml-4 text-gray-400 text-sm">
									Snapback Demo
								</span>
							</div>
							<div className="relative aspect-video bg-gradient-to-br from-gray-800 to-gray-900 p-8">
								<div className="flex h-full items-center justify-center">
									<motion.button
										whileHover={{ scale: 1.1 }}
										whileTap={{ scale: 0.9 }}
										onClick={() => setIsPlaying(!isPlaying)}
										className="flex h-20 w-20 items-center justify-center rounded-full bg-white/20 backdrop-blur-sm transition-all hover:bg-white/30"
									>
										{isPlaying ? (
											<Pause className="h-8 w-8 text-white" />
										) : (
											<Play className="ml-1 h-8 w-8 text-white" />
										)}
									</motion.button>
								</div>
							</div>
						</div>
					</div>
				</motion.div>
			</div>
		</section>
	);
}
