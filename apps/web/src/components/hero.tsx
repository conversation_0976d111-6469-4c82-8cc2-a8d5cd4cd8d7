"use client";

import { motion } from "framer-motion";
import {
	Command,
	Download,
	Layers,
	Play,
	Star,
	Users,
	Zap,
} from "lucide-react";
import Image from "next/image";
import { Button } from "@/components/ui/button";

export function Hero() {
	return (
		<section className="relative overflow-hidden bg-gradient-to-br from-gray-50 via-white to-blue-50 px-6 py-24 sm:py-32 lg:px-8">
			{/* Background Elements */}
			<div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]" />
			<div
				className="-z-10 -translate-x-1/2 xl:-top-6 absolute top-0 left-1/2 blur-3xl"
				aria-hidden="true"
			>
				<div
					className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30"
					style={{
						clipPath:
							"polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
					}}
				/>
			</div>

			<div className="mx-auto max-w-7xl">
				<div className="mx-auto max-w-4xl text-center">
					{/* Social Proof Badge */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5 }}
						className="mb-8 inline-flex items-center rounded-full bg-white/80 px-4 py-2 font-medium text-gray-600 text-sm shadow-lg ring-1 ring-gray-900/5 backdrop-blur-sm"
					>
						<Star className="mr-2 h-4 w-4 fill-yellow-400 text-yellow-400" />
						Trusted by 10,000+ developers worldwide
						<Users className="ml-2 h-4 w-4" />
					</motion.div>

					<p className="mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text font-extrabold text-5xl text-transparent tracking-tight sm:text-6xl lg:text-8xl">
						Your Perfect Workspace, One Click Away
					</p>

					<p className="mx-auto mb-8 max-w-3xl text-gray-600 text-xl leading-8">
						Stop wasting time recreating your workspace. Snapback captures your
						entire setup windows, positions, apps and restores everything
						instantly with a single shortcut.
					</p>

					{/* Key Benefits */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6, delay: 0.3 }}
						className="mb-12 flex flex-wrap justify-center gap-6 text-gray-600 text-sm"
					>
						<div className="flex items-center gap-2">
							<Zap className="h-4 w-4 text-blue-500" />
							<span>Instant restoration</span>
						</div>
						<div className="flex items-center gap-2">
							<Layers className="h-4 w-4 text-green-500" />
							<span>Multi-monitor support</span>
						</div>
						<div className="flex items-center gap-2">
							<Command className="h-4 w-4 text-purple-500" />
							<span>Custom shortcuts</span>
						</div>
					</motion.div>

					{/* CTA Buttons */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.7, delay: 0.4 }}
						className="flex flex-col justify-center gap-4 sm:flex-row"
					>
						<Button
							size="lg"
							className="group h-14 space-x-3 rounded-2xl bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-4 font-semibold text-lg text-white shadow-xl transition-all duration-200 hover:scale-105 hover:from-blue-700 hover:to-blue-800 hover:shadow-2xl"
						>
							<Download className="h-5 w-5 transition-transform group-hover:scale-110" />
							<span>Download for Mac</span>
							<div className="rounded-full bg-white/20 px-2 py-1 text-xs">
								Free
							</div>
						</Button>

						<Button
							variant="outline"
							size="lg"
							className="h-14 space-x-3 rounded-2xl border-2 border-gray-200 bg-white/80 px-8 py-4 font-semibold text-gray-700 text-lg backdrop-blur-sm transition-all duration-200 hover:bg-white hover:shadow-lg"
						>
							<Play className="h-5 w-5" />
							<span>Watch Demo</span>
						</Button>
					</motion.div>

					{/* Trust Indicators */}
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ duration: 0.8, delay: 0.6 }}
						className="mt-16 text-center"
					>
						<p className="mb-6 font-medium text-gray-500 text-sm">
							Compatible with macOS 14.0 and later
						</p>
						<div className="flex items-center justify-center gap-8 opacity-60">
							<Image
								src="/apple-logo.svg"
								alt="macOS Compatible"
								width={24}
								height={24}
								className="grayscale"
							/>
						</div>
					</motion.div>
				</div>

				{/* Product Preview */}
				<motion.div
					initial={{ opacity: 0, y: 40 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.5 }}
					className="mt-20"
				>
					<div className="relative mx-auto max-w-5xl">
						<div className="-inset-4 absolute rounded-3xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 blur-2xl" />
						<div className="relative overflow-hidden rounded-2xl bg-gray-900 shadow-2xl">
							<div className="flex items-center gap-2 bg-gray-800 px-4 py-3">
								<div className="h-3 w-3 rounded-full bg-red-500" />
								<div className="h-3 w-3 rounded-full bg-yellow-500" />
								<div className="h-3 w-3 rounded-full bg-green-500" />
								<span className="ml-4 text-gray-400 text-sm">Snapback</span>
							</div>
							<div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 p-8">
								<div className="grid h-full grid-cols-3 gap-4">
									{/* Simulated windows */}
									{[1, 2, 3, 4, 5, 6].map((i) => (
										<motion.div
											key={i}
											initial={{ opacity: 0, scale: 0.8 }}
											animate={{ opacity: 1, scale: 1 }}
											transition={{ duration: 0.5, delay: 0.7 + i * 0.1 }}
											className="rounded-lg bg-white/10 backdrop-blur-sm"
										/>
									))}
								</div>
							</div>
						</div>
					</div>
				</motion.div>
			</div>
		</section>
	);
}
