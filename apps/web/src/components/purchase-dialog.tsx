"use client";

import { AnimatePresence, motion } from "framer-motion";
import { CreditCard, Loader2, Mail, Shield, X } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { formatPrice, isValidEmail, purchaseLicense } from "@/lib/purchase";

interface PurchaseDialogProps {
	isOpen: boolean;
	onClose: () => void;
	licenseType: "pro";
	price: number;
	isDeviceExpansion?: boolean;
	additionalDevices?: number;
}

export function PurchaseDialog({
	isOpen,
	onClose,
	licenseType,
	price,
	isDeviceExpansion = false,
	additionalDevices = 2,
}: PurchaseDialogProps) {
	const [email, setEmail] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);

		// Validate email
		if (!email.trim()) {
			setError("Please enter your email address");
			return;
		}

		if (!isValidEmail(email)) {
			setError("Please enter a valid email address");
			return;
		}

		setIsLoading(true);

		try {
			// Pass device expansion parameters to purchase function
			await purchaseLicense(
				licenseType,
				email.trim(),
				isDeviceExpansion,
				additionalDevices,
			);
			// purchaseLicense handles the redirect, so we won't reach here normally
		} catch (err) {
			console.error("Purchase failed:", err);
			setError("Failed to start checkout process. Please try again.");
			setIsLoading(false);
		}
	};

	const handleClose = () => {
		if (!isLoading) {
			setEmail("");
			setError(null);
			onClose();
		}
	};

	return (
		<AnimatePresence>
			{isOpen && (
				<>
					{/* Backdrop */}
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
						onClick={handleClose}
					/>

					{/* Dialog */}
					<motion.div
						initial={{ opacity: 0, scale: 0.95, y: 20 }}
						animate={{ opacity: 1, scale: 1, y: 0 }}
						exit={{ opacity: 0, scale: 0.95, y: 20 }}
						className="fixed inset-0 z-50 flex items-center justify-center p-4"
					>
						<div className="w-full max-w-md rounded-2xl bg-white p-8 shadow-2xl">
							{/* Header */}
							<div className="mb-6 flex items-center justify-between">
								<h2 className="font-bold text-2xl text-gray-900">
									{isDeviceExpansion
										? "Expand License"
										: "Purchase Pro License"}
								</h2>
								<button
									type="button"
									onClick={handleClose}
									disabled={isLoading}
									className="rounded-lg p-2 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600 disabled:opacity-50"
								>
									<X className="h-5 w-5" />
								</button>
							</div>

							{/* Price Display */}
							<div className="mb-6 rounded-lg bg-blue-50 p-4">
								<div className="flex items-center justify-between">
									<span className="font-medium text-gray-900">
										{isDeviceExpansion ? "Device Expansion" : "Pro License"}
									</span>
									<span className="font-bold text-2xl text-blue-600">
										{formatPrice(price)}
									</span>
								</div>
								<p className="mt-1 text-gray-600 text-sm">
									{isDeviceExpansion
										? `Add ${additionalDevices} more devices to your existing license`
										: "2 devices • Lifetime license"}
								</p>
							</div>

							{/* Form */}
							<form onSubmit={handleSubmit} className="space-y-4">
								<div>
									<label
										htmlFor="email"
										className="mb-2 block font-medium text-gray-700 text-sm"
									>
										Email Address
									</label>
									<div className="relative">
										<Mail className="-translate-y-1/2 absolute top-1/2 left-3 h-5 w-5 text-gray-400" />
										<input
											id="email"
											type="email"
											value={email}
											onChange={(e) => setEmail(e.target.value)}
											placeholder="<EMAIL>"
											disabled={isLoading}
											className="w-full rounded-lg border border-gray-300 py-3 pr-4 pl-10 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 disabled:bg-gray-50 disabled:opacity-50"
										/>
									</div>
									<p className="mt-1 text-gray-500 text-xs">
										Your license key will be sent to this email address
									</p>
								</div>

								{error && (
									<motion.div
										initial={{ opacity: 0, y: -10 }}
										animate={{ opacity: 1, y: 0 }}
										className="rounded-lg bg-red-50 p-3 text-red-700 text-sm"
									>
										{error}
									</motion.div>
								)}

								<Button
									type="submit"
									disabled={isLoading}
									className="w-full bg-blue-600 py-3 text-white hover:bg-blue-700 disabled:opacity-50"
								>
									{isLoading ? (
										<>
											<Loader2 className="mr-2 h-5 w-5 animate-spin" />
											Processing...
										</>
									) : (
										<>
											<CreditCard className="mr-2 h-5 w-5" />
											Continue to Payment
										</>
									)}
								</Button>
							</form>

							{/* Security Notice */}
							<div className="mt-6 flex items-center gap-2 rounded-lg bg-gray-50 p-3">
								<Shield className="h-4 w-4 text-green-600" />
								<p className="text-gray-600 text-xs">
									Secure payment processing by Stripe. Your payment information
									is never stored on our servers.
								</p>
							</div>

							{/* Terms */}
							<p className="mt-4 text-center text-gray-500 text-xs">
								By continuing, you agree to our{" "}
								<a href="#terms" className="text-blue-600 hover:text-blue-700">
									Terms of Service
								</a>{" "}
								and{" "}
								<a
									href="#privacy"
									className="text-blue-600 hover:text-blue-700"
								>
									Privacy Policy
								</a>
							</p>
						</div>
					</motion.div>
				</>
			)}
		</AnimatePresence>
	);
}
