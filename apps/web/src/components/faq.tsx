"use client";

import { motion } from "framer-motion";
import { HelpCircle, MessageCircle } from "lucide-react";

import { Button } from "@/components/ui/button";

const faqs = [
	{
		question: "Why is Snapback not in the App Store?",
		answer:
			"Snapback is a productivity tool that requires Accessibility permissions to function. The App Store review process does not allow for this type of access.",
	},
	{
		question: "Is Snapback safe to use?",
		answer:
			"Snapback is 100% safe. It's a local app that does not transmit any data. It only requires Accessibility permissions to control window positions. It's signed and notarized by Apple.",
	},
	{
		question: "Do you offer subscriptions?",
		answer:
			"No, we don't believe in subscriptions. We want to make Snapback accessible to everyone.",
	},

	{
		question:
			"What is the difference between Snapback and Other Window Managers?",
		answer:
			"Think of Snapback as a workspace manager that also includes window snapping. Other window managers are primarily focused on window snapping and lack the workspace management features.",
	},
	{
		question: 'What\'s a "workspace" in Snapback?',
		answer:
			"A saved snapshot of your desktop which apps are open, where windows are positioned, their sizes. Like a bookmark for your entire work setup.",
	},

	{
		question: "Can I save workspaces within macOS Spaces?",
		answer:
			"No, Snapback does not support saving workspaces within Spaces. API limitations prevent us from accessing window information within Spaces.",
	},
	{
		question: "Does Snapback replace macOS Spaces?",
		answer:
			"No, Snapback complements Spaces. Spaces are great for virtual desktops, but Snapback saves the exact window layout within each Space.",
	},
	{
		question: "Do I need the window snapping features?",
		answer:
			"Nope! Workspace save/restore is the main feature. Window snapping is optional and can be disabled completely.",
	},
	{
		question: "What macOS versions work?",
		answer:
			"macOS 14.6+ (Sonoma) is required. Snapback does not work on earlier versions.",
	},
	{
		question: "What permissions does it need?",
		answer:
			"Just Accessibility permissions to control window positions. Standard macOS permission, nothing sensitive.",
	},
	{
		question: "Will it conflict with Other Window Managers?",
		answer:
			"You may run into shortcut conflicts with other other apps but it's as easy as changing the shortcut in Snapback settings.",
	},
	{
		question: "Does it work with all apps?",
		answer:
			"Works with most apps. Some apps may have restrictions on window movement or resizing, but most work fine.",
	},
	{
		question: "Is my data private?",
		answer:
			"100% local storage. No cloud, no analytics, no data transmission. Your workspace configs stay on your Mac.",
	},
	{
		question: "What if I replace monitors, will my workspaces still work?",
		answer:
			"Yes, Snapback is designed to handle monitor changes, including monitor resolution changes and monitor reordering. It will automatically adjust window positions to fit your new setup, but be aware that changing the main display can cause issues.",
	},
	{
		question: "What if I wanna share workspaces between machines?",
		answer:
			"Currently, Snapback workspaces are stored locally on your machine. However, you can manually export and import workspace configurations if needed, in the future we plan to add support for cloud syncing.",
	},
	{
		question: "Do I need to buy a license for each machine I use it on?",
		answer: "You can use Snapback on up to 2 machines with a single license.",
	},
];

export function FAQ() {
	return (
		<section
			id="faq"
			className="relative overflow-hidden bg-gray-50 px-6 py-24 lg:px-8"
		>
			{/* Background Pattern */}
			<div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-40 [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />

			<div className="relative mx-auto max-w-4xl">
				{/* Header */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6 }}
					viewport={{ once: true }}
					className="mb-16 text-center"
				>
					<div className="mb-4 inline-flex items-center rounded-full bg-blue-100 px-4 py-2 font-medium text-blue-700 text-sm">
						<HelpCircle className="mr-2 h-4 w-4" />
						Frequently Asked Questions
					</div>
					<h2 className="mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text font-bold text-4xl text-transparent sm:text-5xl">
						Got questions? We've got answers
					</h2>
					<p className="mx-auto max-w-3xl text-gray-600 text-xl leading-8">
						Everything you need to know about Snapback. Can't find what you're
						looking for? Contact our support team.
					</p>
				</motion.div>

				{/* FAQ Simple Design - Matching Previous Pricing Style */}
				<div className="mx-auto max-w-3xl space-y-6">
					{faqs.map((faq, index) => (
						<motion.div
							key={faq.question}
							initial={{ opacity: 0, y: 10 }}
							whileInView={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.4, delay: index * 0.1 }}
							viewport={{ once: true }}
							className="rounded-xl bg-gray-50 p-6 text-left"
						>
							<h4 className="mb-2 font-semibold text-gray-900">
								{faq.question}
							</h4>
							<p className="text-gray-600">{faq.answer}</p>
						</motion.div>
					))}
				</div>

				{/* Contact Support CTA */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6, delay: 0.4 }}
					viewport={{ once: true }}
					className="mt-16 text-center"
				>
					<div className="rounded-2xl bg-white p-8 shadow-lg">
						<MessageCircle className="mx-auto mb-4 h-12 w-12 text-blue-500" />
						<h3 className="mb-2 font-bold text-gray-900 text-xl">
							Still have questions?
						</h3>
						<p className="mb-6 text-gray-600">
							Our support team is here to help you get the most out of Snapback.
						</p>
						<Button className="bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl">
							<MessageCircle className="mr-2 h-4 w-4" />
							Contact Support
						</Button>
					</div>
				</motion.div>
			</div>
		</section>
	);
}
