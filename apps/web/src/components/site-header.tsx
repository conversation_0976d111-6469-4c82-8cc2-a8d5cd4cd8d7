"use client";

import { usePathname } from "next/navigation";

import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";

// Navigation items for page title mapping
const navigation = [
	{
		name: "Overview",
		href: "/dashboard",
	},
	{
		name: "Licenses",
		href: "/dashboard/licenses",
	},
	{
		name: "Customers",
		href: "/dashboard/customers",
	},
	{
		name: "Payment & Billing",
		href: "/dashboard/payments",
	},
	{
		name: "Taxes",
		href: "/dashboard/tax",
	},
	{
		name: "Analytics",
		href: "/dashboard/analytics",
	},
	{
		name: "Users",
		href: "/dashboard/users",
	},
	{
		name: "Settings",
		href: "/dashboard/admin",
	},
];

export function SiteHeader() {
	const pathname = usePathname();

	// Get current page title based on pathname
	const currentPageTitle =
		navigation.find((item) => item.href === pathname)?.name || "Dashboard";

	return (
		<header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
			<div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
				<SidebarTrigger className="-ml-1" />
				<Separator
					orientation="vertical"
					className="mx-2 data-[orientation=vertical]:h-4"
				/>
				<h1 className="font-medium text-base">{currentPageTitle}</h1>
				<div className="ml-auto flex items-center gap-2">
					{/* Future: Add user menu or other header actions here */}
				</div>
			</div>
		</header>
	);
}
