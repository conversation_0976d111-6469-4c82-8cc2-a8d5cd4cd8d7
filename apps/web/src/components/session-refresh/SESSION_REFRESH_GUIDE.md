# Session Refresh Implementation Guide

## Overview

This document outlines the comprehensive session refresh implementation for the SnapBack app, providing automatic session renewal to prevent user interruption due to session expiration.

## Architecture

### Core Components

1. **Better Auth Configuration** (`apps/server/src/lib/auth.ts`)
   - Enhanced session management with automatic refresh
   - Optimized cookie caching for performance
   - Configurable session expiration and update intervals

2. **Session Refresh Service** (`apps/web/src/lib/session-refresh.ts`)
   - Core session monitoring and refresh logic
   - Configurable refresh thresholds and retry mechanisms
   - Event-driven callbacks for session state changes

3. **Session Provider** (`apps/web/src/components/providers/session-provider.tsx`)
   - React context for global session management
   - Automatic session monitoring for authenticated users
   - Integration with toast notifications and routing

4. **Session Hooks** (`apps/web/src/hooks/use-session-refresh.ts`)
   - Convenient React hooks for session management
   - Backward compatibility with existing code
   - Session-aware API call wrapper

5. **Session Status Components** (`apps/web/src/components/session-status.tsx`)
   - Visual session status indicators
   - Manual refresh controls
   - Detailed session information panels

## Configuration

### Server-Side Configuration

```typescript
// apps/server/src/lib/auth.ts
export const auth = betterAuth({
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // Update every 1 day when active
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5, // 5 minutes cache duration
    },
    disableSessionRefresh: false, // Enable automatic refresh
  },
  // ... other config
});
```

### Client-Side Configuration

```typescript
// apps/web/src/components/providers.tsx
<SessionProvider
  config={{
    checkInterval: 60 * 1000, // Check every minute
    refreshThreshold: 15 * 60 * 1000, // Refresh 15 minutes before expiry
    maxRetries: 3,
  }}
  autoStart={true}
  redirectOnExpiry="/login"
  showNotifications={true}
>
  {/* App content */}
</SessionProvider>
```

## Features

### 1. Automatic Session Monitoring

- **Periodic Checks**: Monitors session status every minute
- **Proactive Refresh**: Refreshes sessions 15 minutes before expiry
- **Background Operation**: Runs transparently without user intervention

### 2. Session Refresh Mechanisms

- **Better Auth Integration**: Leverages Better Auth's built-in refresh capabilities
- **Retry Logic**: Attempts refresh up to 3 times with exponential backoff
- **Graceful Degradation**: Handles refresh failures with user notifications

### 3. User Experience Enhancements

- **Toast Notifications**: Informs users of session events
- **Visual Indicators**: Shows session status in the UI
- **Manual Refresh**: Allows users to manually refresh sessions
- **Seamless Operation**: Maintains user workflow without interruption

### 4. API Integration

- **Automatic Retry**: API calls automatically retry after session refresh
- **401 Error Handling**: Intercepts authentication errors and attempts refresh
- **Transparent Operation**: Works with existing API infrastructure

## Usage Examples

### Basic Session Monitoring

```typescript
// Automatic monitoring (handled by SessionProvider)
function DashboardLayout({ children }) {
  // Session monitoring starts automatically when user is authenticated
  return <div>{children}</div>;
}
```

### Manual Session Management

```typescript
import { useSessionRefresh } from '@/hooks/use-session-refresh';

function SessionControls() {
  const { refreshSession, isRefreshing, sessionInfo } = useSessionRefresh();

  const handleRefresh = async () => {
    const success = await refreshSession();
    if (success) {
      console.log('Session refreshed successfully');
    }
  };

  return (
    <button onClick={handleRefresh} disabled={isRefreshing}>
      {isRefreshing ? 'Refreshing...' : 'Refresh Session'}
    </button>
  );
}
```

### Session-Aware API Calls

```typescript
import { useSessionAwareApi } from '@/hooks/use-session-refresh';

function DataComponent() {
  const { apiCall } = useSessionAwareApi();

  const fetchData = async () => {
    try {
      // Automatically handles session refresh on 401 errors
      const data = await apiCall(() => api.getData());
      return data;
    } catch (error) {
      console.error('API call failed:', error);
    }
  };

  return <div>{/* Component content */}</div>;
}
```

### Session Status Display

```typescript
import { SessionStatus, SessionStatusIndicator } from '@/components/session-status';

function Header() {
  return (
    <div className="header">
      <SessionStatusIndicator /> {/* Compact indicator */}
    </div>
  );
}

function SettingsPage() {
  return (
    <div>
      <SessionStatus 
        showDetails={true}
        showRefreshButton={true}
      />
    </div>
  );
}
```

## Configuration Options

### Session Refresh Service

```typescript
interface SessionRefreshConfig {
  checkInterval: number;        // How often to check (ms)
  refreshThreshold: number;     // When to refresh before expiry (ms)
  maxRetries: number;          // Maximum refresh attempts
  onRefreshFailed?: () => void; // Callback on refresh failure
  onRefreshSuccess?: () => void; // Callback on refresh success
  onSessionExpired?: () => void; // Callback on session expiry
}
```

### Session Provider

```typescript
interface SessionProviderProps {
  config?: Partial<SessionRefreshConfig>;
  autoStart?: boolean;          // Auto-start monitoring
  redirectOnExpiry?: string;    // Redirect path on expiry
  showNotifications?: boolean;  // Show toast notifications
}
```

## Error Handling

### Session Expiration

1. **Detection**: Service detects expired sessions during periodic checks
2. **Notification**: User receives toast notification about expiration
3. **Redirect**: User is redirected to login page
4. **Cleanup**: Session monitoring stops and state is cleared

### Refresh Failures

1. **Retry Logic**: Attempts refresh up to configured maximum retries
2. **Exponential Backoff**: Delays between retry attempts
3. **Fallback**: Treats as session expiration after max retries
4. **User Notification**: Informs user of refresh failure

### Network Issues

1. **Error Handling**: Gracefully handles network connectivity issues
2. **Retry Strategy**: Continues monitoring when connectivity is restored
3. **User Feedback**: Provides appropriate error messages

## Security Considerations

### Session Security

- **HttpOnly Cookies**: Session tokens stored in secure, HttpOnly cookies
- **CSRF Protection**: Better Auth provides built-in CSRF protection
- **Secure Transport**: HTTPS required in production environments

### Refresh Security

- **Server Validation**: All refresh operations validated server-side
- **Token Rotation**: Better Auth handles secure token rotation
- **Audit Logging**: Session events logged for security monitoring

## Performance Optimization

### Efficient Monitoring

- **Minimal Overhead**: Lightweight periodic checks
- **Cookie Caching**: Reduces database queries with cookie cache
- **Smart Scheduling**: Adjusts check frequency based on session age

### Resource Management

- **Cleanup**: Proper cleanup of timers and event listeners
- **Memory Management**: Prevents memory leaks in long-running sessions
- **Batching**: Efficient handling of multiple concurrent operations

## Testing

### Unit Tests

```typescript
// Example test for session refresh service
describe('SessionRefreshService', () => {
  it('should refresh session before expiry', async () => {
    const service = new SessionRefreshService({
      refreshThreshold: 5 * 60 * 1000, // 5 minutes
    });
    
    // Mock session near expiry
    // Test refresh trigger
    // Verify refresh called
  });
});
```

### Integration Tests

```typescript
// Example test for session provider
describe('SessionProvider', () => {
  it('should redirect on session expiry', async () => {
    render(
      <SessionProvider redirectOnExpiry="/login">
        <TestComponent />
      </SessionProvider>
    );
    
    // Mock session expiry
    // Verify redirect behavior
  });
});
```

## Troubleshooting

### Common Issues

1. **Session Not Refreshing**
   - Check Better Auth configuration
   - Verify session refresh is enabled
   - Check network connectivity

2. **Frequent Redirects**
   - Verify session expiration settings
   - Check refresh threshold configuration
   - Review server-side session validation

3. **Performance Issues**
   - Adjust check interval
   - Optimize cookie cache settings
   - Review monitoring overhead

### Debug Mode

Enable debug logging for session refresh operations:

```typescript
// Add to session refresh service
console.log('Session refresh attempt:', { 
  sessionInfo, 
  timeUntilExpiry, 
  needsRefresh 
});
```

## Migration Guide

### From Manual Session Handling

1. **Remove Manual Checks**: Remove existing session expiry checks
2. **Add Provider**: Wrap app with SessionProvider
3. **Update Components**: Use new session hooks
4. **Test Integration**: Verify automatic refresh works

### Configuration Migration

1. **Server Config**: Update Better Auth session settings
2. **Client Config**: Configure SessionProvider
3. **API Updates**: Update API error handling
4. **UI Updates**: Add session status components

## Best Practices

1. **Configuration**: Use appropriate refresh thresholds for your use case
2. **Error Handling**: Implement comprehensive error handling
3. **User Experience**: Provide clear feedback on session status
4. **Testing**: Test session refresh scenarios thoroughly
5. **Monitoring**: Monitor session refresh success rates
6. **Security**: Follow security best practices for session management

## Conclusion

The session refresh implementation provides a robust, user-friendly solution for maintaining authenticated sessions in the SnapBack app. It combines Better Auth's powerful session management with intelligent client-side monitoring to create a seamless user experience while maintaining security and performance.
