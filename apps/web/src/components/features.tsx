"use client";

import { motion } from "framer-motion";
import {
	ArrowRight,
	Keyboard,
	Layers,
	Monitor,
	MousePointer2,
	Share2,
	<PERSON><PERSON><PERSON>,
	Zap,
} from "lucide-react";

const features = [
	{
		title: "Save & Restore Window Layouts",
		description:
			"Snapback captures the exact position, size, and state of every window in your workspace. One click brings everything back exactly as you left it.",
		icon: Monitor,
		gradient: "from-blue-500 to-cyan-500",
		benefits: [
			"Pixel-perfect restoration",
			"App state preservation",
			"Multi-workspace support",
		],
	},
	{
		title: "Custom Shortcuts",
		description:
			"Create personalized keyboard combinations for instant workspace switching and window management.",
		icon: Keyboard,
		gradient: "from-purple-500 to-pink-500",
		benefits: ["Any key combination", "Global hotkeys", "Context-aware"],
	},
	{
		title: "Drag-to-Snap",
		description:
			"Effortlessly organize windows by dragging them to screen edges, corners, or custom zones.",
		icon: MousePointer2,
		gradient: "from-green-500 to-emerald-500",
		benefits: ["Smart zones", "Visual feedback", "Magnetic snapping"],
	},
	{
		title: "Workspace Sharing",
		description:
			"Export your perfect setups and share them across devices or with your team.",
		icon: Share2,
		gradient: "from-orange-500 to-red-500",
		benefits: ["Cross-device sync", "Team collaboration", "Backup & restore"],
	},
	{
		title: "Multi-Monitor Magic",
		description:
			"Seamlessly manage complex setups with multiple displays, any resolution, any arrangement.",
		icon: Layers,
		gradient: "from-indigo-500 to-purple-500",
		benefits: ["Unlimited monitors", "Mixed resolutions", "Dynamic layouts"],
	},
	{
		title: "Lightning Fast",
		description:
			"Native performance with minimal system impact. Your workspace restored in milliseconds.",
		icon: Zap,
		gradient: "from-yellow-500 to-orange-500",
		benefits: ["<100ms restore", "Low memory usage", "Battery efficient"],
	},
];

export function Features() {
	return (
		<section
			id="features"
			className="relative overflow-hidden bg-gray-50 px-6 py-24 lg:px-8"
		>
			{/* Background Pattern */}
			<div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-40 [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />

			<div className="relative mx-auto max-w-7xl">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6 }}
					viewport={{ once: true }}
					className="mb-16 text-center"
				>
					<div className="mb-4 inline-flex items-center rounded-full bg-blue-100 px-4 py-2 font-medium text-blue-700 text-sm">
						<Sparkles className="mr-2 h-4 w-4" />
						Powerful Features
					</div>
					<h2 className="mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text font-bold text-4xl text-transparent sm:text-5xl">
						Everything you need for perfect workspace management
					</h2>
					<p className="mx-auto max-w-3xl text-gray-600 text-xl leading-8">
						From instant restoration to advanced customization, Snapback gives
						you complete control over your digital workspace.
					</p>
				</motion.div>

				{/* Clean Features Grid */}
				<div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
					{features.map((feature, index) => {
						const Icon = feature.icon;
						return (
							<motion.div
								key={feature.title}
								initial={{ opacity: 0, y: 20 }}
								whileInView={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.6, delay: index * 0.1 }}
								viewport={{ once: true }}
								whileHover={{ y: -5, scale: 1.02 }}
								className="group relative overflow-hidden rounded-3xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-2xl"
							>
								{/* Gradient Background */}
								<div
									className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 transition-opacity duration-300 group-hover:opacity-5`}
								/>

								{/* Icon */}
								<div
									className={`mb-6 inline-flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-r ${feature.gradient} shadow-lg`}
								>
									<Icon className="h-6 w-6 text-white" />
								</div>

								{/* Content */}
								<div className="relative z-10">
									<h3 className="mb-3 font-bold text-gray-900 text-xl group-hover:text-gray-800">
										{feature.title}
									</h3>
									<p className="mb-4 text-gray-600 leading-relaxed">
										{feature.description}
									</p>

									{/* Benefits List */}
									<ul className="space-y-2">
										{feature.benefits.map((benefit) => (
											<li
												key={benefit}
												className="flex items-center text-gray-500 text-sm"
											>
												<div
													className={`mr-2 h-1.5 w-1.5 rounded-full bg-gradient-to-r ${feature.gradient}`}
												/>
												{benefit}
											</li>
										))}
									</ul>
								</div>

								{/* Hover Arrow */}
								<div className="absolute right-6 bottom-6 translate-x-2 opacity-0 transition-all duration-300 group-hover:translate-x-0 group-hover:opacity-100">
									<ArrowRight className="h-5 w-5 text-gray-400" />
								</div>
							</motion.div>
						);
					})}
				</div>

				{/* Bottom CTA */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6, delay: 0.8 }}
					viewport={{ once: true }}
					className="mt-16 text-center"
				>
					<p className="mb-6 text-gray-600 text-lg">
						Ready to transform your workflow?
					</p>
					<motion.button
						whileHover={{ scale: 1.05 }}
						whileTap={{ scale: 0.95 }}
						className="inline-flex items-center rounded-2xl bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-4 font-semibold text-lg text-white shadow-xl transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-2xl"
					>
						Try Snapback Free
						<ArrowRight className="ml-2 h-5 w-5" />
					</motion.button>
				</motion.div>
			</div>
		</section>
	);
}
