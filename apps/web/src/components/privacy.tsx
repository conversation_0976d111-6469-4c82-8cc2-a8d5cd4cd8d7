import { Database, Eye, Lock, Shield } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const privacyFeatures = [
	{ icon: Database, text: "100% local storage using macOS UserDefaults" },
	{ icon: Shield, text: "No cloud transmission or analytics" },
	{ icon: Eye, text: "Only captures window positions, never document content" },
	{
		icon: Lock,
		text: "Standard macOS Accessibility permissions (one-time setup)",
	},
];

export function Privacy() {
	return (
		<section id="privacy" className="px-6 py-24 lg:px-8">
			<div className="mx-auto max-w-6xl">
				<div className="mb-16 text-center">
					<h2 className="mb-6 font-bold text-4xl text-gray-900">
						Privacy & Security
					</h2>
					<p className="font-semibold text-2xl text-gray-700">
						Your data stays on your Mac.
					</p>
				</div>

				<div className="mb-12 grid gap-6 md:grid-cols-2">
					{privacyFeatures.map((feature) => (
						<Card key={feature.text} className="border-0 bg-white shadow-lg">
							<CardContent className="p-6">
								<div className="flex items-center gap-4">
									<div className="rounded-lg bg-gray-100 p-3">
										<feature.icon className="h-6 w-6 text-gray-600" />
									</div>
									<span className="text-gray-700 text-lg">{feature.text}</span>
								</div>
							</CardContent>
						</Card>
					))}
				</div>

				<Card className="border-0 bg-gray-50 shadow-xl">
					<CardContent className="p-8 text-center">
						<p className="font-medium text-gray-700 text-lg">
							Future CloudKit sync will be completely optional.
						</p>
					</CardContent>
				</Card>
			</div>
		</section>
	);
}
