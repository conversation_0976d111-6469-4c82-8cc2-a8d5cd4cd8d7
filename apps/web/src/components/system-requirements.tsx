import { <PERSON><PERSON>, HardD<PERSON>, <PERSON><PERSON><PERSON>, Shield } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const requirements = [
	{ icon: Laptop, label: "Minimum", value: "macOS 10.15+ (Catalina or later)" },
	{ icon: Cpu, label: "Recommended", value: "macOS 12+ (Monterey or later)" },
	{
		icon: HardDrive,
		label: "Architecture",
		value: "Intel & Apple Silicon (M1/M2/M3) Macs",
	},
	{ icon: HardDrive, label: "Storage", value: "50MB" },
	{
		icon: Shield,
		label: "Permissions",
		value: "Accessibility access (one-time setup)",
	},
];

export function SystemRequirements() {
	return (
		<section id="requirements" className="bg-gray-50 px-6 py-24 lg:px-8">
			<div className="mx-auto max-w-6xl">
				<div className="mb-16 text-center">
					<h2 className="mb-4 font-bold text-4xl text-gray-900">
						System Requirements
					</h2>
				</div>

				<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
					{requirements.map((req) => (
						<Card key={req.label} className="border-0 bg-white shadow-lg">
							<CardContent className="p-6">
								<div className="flex items-start gap-4">
									<div className="flex-shrink-0 rounded-lg bg-gray-100 p-3">
										<req.icon className="h-6 w-6 text-gray-600" />
									</div>
									<div>
										<h3 className="mb-2 font-bold text-gray-900">
											{req.label}
										</h3>
										<p className="text-gray-700 text-sm">{req.value}</p>
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		</section>
	);
}
