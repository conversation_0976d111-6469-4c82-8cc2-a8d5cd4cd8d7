"use client";

import {
	Activity,
	BarChart3,
	CreditCard,
	Key,
	Receipt,
	Settings,
	Shield,
	Users,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarInset,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarProvider,
	SidebarTrigger,
} from "@/components/ui/sidebar";
import UserMenu from "@/components/user-menu";
import { useUserPermissions } from "@/hooks/use-user-management";
import data from "../app/dashboard/data.json";
import { AppSidebar } from "./app-sidebar";
import { ChartAreaInteractive } from "./chart-area-interactive";
import { DataTable } from "./data-table";
import { SectionCards } from "./section-cards";
import { SiteHeader } from "./site-header";

interface DashboardLayoutProps {
	children: React.ReactNode;
}

const navigation = [
	{
		name: "Overview",
		href: "/dashboard",
		icon: Activity,
	},
	{
		name: "Licenses",
		href: "/dashboard/licenses",
		icon: Key,
	},
	{
		name: "Customers",
		href: "/dashboard/customers",
		icon: Users,
	},
	{
		name: "Payment & Billing",
		href: "/dashboard/payments",
		icon: CreditCard,
	},
	{
		name: "Taxes",
		href: "/dashboard/tax",
		icon: Receipt,
		badge: "MX",
	},
	{
		name: "Analytics",
		href: "/dashboard/analytics",
		icon: BarChart3,
	},
	{
		name: "Users",
		href: "/dashboard/users",
		icon: Shield,
	},
	{
		name: "Settings",
		href: "/dashboard/admin",
		icon: Settings,
	},
];

export default function DashboardLayout({ children }: DashboardLayoutProps) {
	return (
		<SidebarProvider
			style={
				{
					"--sidebar-width": "calc(var(--spacing) * 72)",
					"--header-height": "calc(var(--spacing) * 12)",
				} as React.CSSProperties
			}
		>
			<AppSidebar variant="inset" />
			<SidebarInset>
				<SiteHeader />
				<div className="flex flex-1 flex-col">
					<div className="@container/main flex flex-1 flex-col gap-2">
						{/* Container */}
						<div className="flex flex-col gap-4 px-4 py-4 md:gap-6 md:py-6 lg:px-6">
							{children}
						</div>
					</div>
				</div>
			</SidebarInset>
		</SidebarProvider>
	);
}
