// API Types based on the documented API endpoints

export interface License {
	id: string;
	licenseKey: string;
	email: string;
	licenseType: "standard" | "extended";
	maxDevices: number;
	expiresAt: Date | null;
	createdAt: string;
	updatedAt: string;
	stripePaymentIntentId?: string;
	upgradePaymentIntentId?: string[];
	devicesUsed: number;
	devices: Device[];

	// Refund information
	refundedAt?: Date | null;
	refundReason?: string | null;
	refundAmount?: number | null;
	stripeRefundId?: string | null;
	refundRequest?: RefundRequest;
}

export interface Device {
	id: string;
	licenseId: string;
	deviceHash: string;
	salt: string;
	firstSeen: string;
	lastSeen: string;
	appVersion?: string;
	isActive: boolean;

	// Enhanced device metadata
	deviceName?: string;
	deviceType?: string;
	deviceModel?: string;
	operatingSystem?: string;
	architecture?: string;
	screenResolution?: string;
	totalMemory?: string;
	userNickname?: string;
	location?: string;
	notes?: string;
}

export interface RefundRequest {
	id: string;
	licenseId: string;
	status: "PENDING" | "APPROVED" | "REJECTED" | "PROCESSED" | "FAILED";
	reason: string;
	amount?: number | null;
	requestedBy: string;
	adminNotes?: string | null;
	processedBy?: string | null;
	createdAt: string;
	processedAt?: string | null;
	license: License;
}

export interface PricingInfo {
	trial: {
		price: number;
		maxDevices: number;
		duration: string;
	};
	standard: {
		price: number;
		maxDevices: number;
		duration: string;
	};
	extended: {
		price: number;
		maxDevices: number;
		duration: string;
	};
	additionalDevice: {
		price: number;
		description: string;
	};
}

export interface PaymentIntent {
	clientSecret: string;
	amount: number;
	licenseType: string;
	paymentIntentId: string;
}

export interface CheckoutSession {
	sessionId: string;
	url: string;
	amount: number;
	licenseType: string;
}

export interface PaymentStatus {
	paymentIntentId: string;
	status: string;
	amount: number;
	currency: string;
	license?: License;
	metadata?: Record<string, string>;
}

export interface LicenseValidationResponse {
	valid: boolean;
	deviceToken: string;
	licenseType: string;
	expiresAt: Date | null;
	maxDevices: number;
	devicesUsed: number;
	trialDaysRemaining: number;
}

export interface LicenseStatusResponse {
	licenseKey: string;
	licenseType: string;
	email: string;
	createdAt: string;
	expiresAt: Date | null;
	maxDevices: number;
	devicesUsed: number;
	isExpired: boolean;
	isActive: boolean;
	trialDaysRemaining: number;
	devices: Device[];
}

export interface RefundStatusResponse {
	licenseKey: string;
	refunded: boolean;
	refundedAt: string | null;
	refundReason: string | null;
	refundAmount: number | null;
	refundRequest: RefundRequest | null;
}

export interface RefundHistoryResponse {
	refundRequests: RefundRequest[];
	pagination: {
		page: number;
		limit: number;
		totalCount: number;
		totalPages: number;
		hasNextPage: boolean;
		hasPreviousPage: boolean;
	};
}

export interface ApiError {
	error: string;
	code?: string;
	message?: string;
	timestamp: string;
	path?: string;
	details?: Record<string, unknown>;
}

// Request types
export interface CreateLicenseRequest {
	email: string;
	licenseType: "standard" | "extended";
	stripePaymentIntentId?: string;
}

export interface ValidateLicenseRequest {
	licenseKey: string;
	deviceId: string;
	appVersion?: string;
	deviceMetadata?: Partial<Device>;
}

export interface CreatePaymentIntentRequest {
	licenseType: "standard" | "extended";
	additionalDevices?: number;
	email: string;
}

export interface CreateCheckoutSessionRequest {
	licenseType: "standard" | "extended";
	additionalDevices?: number;
	email: string;
	deviceId?: string;
	successUrl: string;
	cancelUrl: string;
}

export interface RequestRefundRequest {
	licenseKey: string;
	reason: string;
	requestedBy: string;
}

export interface ProcessRefundRequest {
	licenseKey: string;
	action: "approve" | "reject";
	adminNotes?: string;
	amount?: number;
}

export interface UpgradeLicenseRequest {
	licenseKey: string;
	additionalDevices: number;
	stripePaymentIntentId?: string;
}

export interface UpdateDeviceMetadataRequest {
	deviceId: string;
	deviceMetadata: Partial<Device>;
}

// Dashboard Analytics Types
export interface DashboardStats {
	totalLicenses: number;
	activeLicenses: number;
	totalRevenue: number;
	monthlyRevenue: number;
	totalCustomers: number;
	activeDevices: number;
	systemHealth: "healthy" | "warning" | "critical";
	pendingRefunds: number;
}

export interface RevenueChartData {
	month: string;
	amount: number;
}

export interface LicenseDistribution {
	standard: number;
	extended: number;
	trial: number;
}

export interface CustomerGrowthData {
	month: string;
	newCustomers: number;
	totalCustomers: number;
}

export interface DeviceAnalytics {
	deviceType: string;
	count: number;
	percentage: number;
}

export interface RecentActivity {
	id: string;
	type:
		| "license_created"
		| "payment_received"
		| "refund_requested"
		| "device_added";
	description: string;
	timestamp: string;
	amount?: number;
	licenseKey?: string;
	email?: string;
}
