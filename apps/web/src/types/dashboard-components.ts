/**
 * TypeScript type definitions for reusable dashboard components
 * Integrates with existing dashboard types and RBAC system
 */

import type { LucideIcon } from "lucide-react";
import type { ReactNode } from "react";

// ============================================================================
// Dashboard Header Types
// ============================================================================

export interface DashboardHeaderAction {
	label: string;
	icon?: LucideIcon;
	onClick: () => void;
	variant?:
		| "default"
		| "outline"
		| "secondary"
		| "ghost"
		| "link"
		| "destructive";
	disabled?: boolean;
	loading?: boolean;
}

export interface DashboardHeaderProps {
	title: string;
	subtitle?: string;
	actions?: DashboardHeaderAction[];
	loading?: boolean;
	className?: string;
}

// ============================================================================
// Data Table Types
// ============================================================================

export interface TableColumn<T = Record<string, unknown>> {
	key: keyof T | string;
	header: string;
	sortable?: boolean;
	width?: string;
	className?: string;
	render?: (value: unknown, row: T, index: number) => ReactNode;
	mobileRender?: (row: T, index: number) => ReactNode;
}

export interface TableRowAction<T = Record<string, unknown>> {
	label: string;
	icon?: LucideIcon;
	onClick: (row: T) => void;
	disabled?: (row: T) => boolean;
	hidden?: (row: T) => boolean;
	variant?: "default" | "destructive";
	separator?: boolean;
}

export interface TableSortConfig {
	key: string;
	direction: "asc" | "desc";
}

export interface DataTableProps<T = Record<string, unknown>> {
	data: T[];
	columns: TableColumn<T>[];
	actions?: TableRowAction<T>[];
	loading?: boolean;
	emptyMessage?: string;
	emptyIcon?: LucideIcon;
	sortConfig?: TableSortConfig;
	onSort?: (config: TableSortConfig) => void;
	className?: string;
	mobileBreakpoint?: string;
}

// ============================================================================
// Filter Types
// ============================================================================

export interface FilterOption {
	label: string;
	value: string;
	icon?: LucideIcon;
}

export interface SearchFilterConfig {
	type: "search";
	key: string;
	placeholder: string;
	icon?: LucideIcon;
	debounceMs?: number;
}

export interface SelectFilterConfig {
	type: "select";
	key: string;
	label: string;
	placeholder: string;
	options: FilterOption[];
	icon?: LucideIcon;
	multiple?: boolean;
}

export interface DateRangeFilterConfig {
	type: "dateRange";
	key: string;
	label: string;
	placeholder: string;
	icon?: LucideIcon;
}

export interface BooleanFilterConfig {
	type: "boolean";
	key: string;
	label: string;
	trueLabel: string;
	falseLabel: string;
	icon?: LucideIcon;
}

export type FilterConfig =
	| SearchFilterConfig
	| SelectFilterConfig
	| DateRangeFilterConfig
	| BooleanFilterConfig;

export type FilterValue = string | string[] | boolean | undefined;

export interface FilterState {
	[key: string]: FilterValue;
}

export interface FilterComponentProps {
	filters: FilterConfig[];
	values: FilterState;
	onChange: (key: string, value: FilterValue) => void;
	onClear?: () => void;
	loading?: boolean;
	className?: string;
}

// ============================================================================
// Pagination Types
// ============================================================================

export interface PaginationMeta {
	page: number;
	limit: number;
	totalCount: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
}

export interface PaginationConfig {
	page: number;
	limit: number;
	showInfo?: boolean;
	showFirstLast?: boolean;
	showPrevNext?: boolean;
	maxVisiblePages?: number;
	pageSizeOptions?: number[];
	onPageChange: (page: number) => void;
	onPageSizeChange?: (limit: number) => void;
}

export interface EnhancedPaginationProps extends PaginationConfig {
	meta: PaginationMeta;
	loading?: boolean;
	className?: string;
}

// ============================================================================
// Combined Dashboard List View Types
// ============================================================================

export interface DashboardListViewProps<T = Record<string, unknown>> {
	// Header
	title: string;
	subtitle?: string;
	actions?: DashboardHeaderAction[];

	// Data & Loading
	data: T[];
	loading?: boolean;
	error?: string | null;

	// Table
	columns: TableColumn<T>[];
	rowActions?: TableRowAction<T>[];
	emptyMessage?: string;
	emptyIcon?: LucideIcon;

	// Filters
	filters?: FilterConfig[];
	filterValues?: FilterState;
	onFilterChange?: (key: string, value: FilterValue) => void;
	onFilterClear?: () => void;

	// Pagination
	pagination?: PaginationMeta;
	paginationConfig?: Omit<PaginationConfig, "page" | "limit">;

	// Sorting
	sortConfig?: TableSortConfig;
	onSort?: (config: TableSortConfig) => void;

	// Styling
	className?: string;
}

// ============================================================================
// Permission-aware Component Types
// ============================================================================

export interface PermissionGatedProps {
	requiredPermissions?: string[];
	fallback?: ReactNode;
	children: ReactNode;
}

// ============================================================================
// Skeleton Loading Types
// ============================================================================

export interface SkeletonTableProps {
	columns: number;
	rows: number;
	showActions?: boolean;
	className?: string;
}

export interface SkeletonHeaderProps {
	showActions?: boolean;
	actionsCount?: number;
	className?: string;
}

export interface SkeletonFiltersProps {
	filtersCount: number;
	className?: string;
}

// ============================================================================
// URL State Management Types
// ============================================================================

export interface URLStateConfig {
	page?: number;
	limit?: number;
	sort?: string;
	order?: "asc" | "desc";
	[key: string]: string | number | boolean | undefined;
}

export interface UseURLStateReturn {
	state: URLStateConfig;
	updateState: (updates: Partial<URLStateConfig>) => void;
	resetState: () => void;
}

// ============================================================================
// Export all types
// ============================================================================

export type {
	// Re-export existing types that might be needed
	PaginationMeta as ExistingPaginationMeta,
} from "@snapback/shared";
