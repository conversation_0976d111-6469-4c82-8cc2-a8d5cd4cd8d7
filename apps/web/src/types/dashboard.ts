// Dashboard-specific types based on API documentation and specification

export type Currency = "USD" | "MXN";

export interface License {
	id: string;
	licenseKey: string;
	email: string;
	licenseType: "standard" | "extended";
	maxDevices: number;
	expiresAt: Date | null;
	createdAt: Date;
	updatedAt: Date;
	stripePaymentIntentId: string;
	upgradePaymentIntentId?: string[];

	// Refund information
	refundedAt?: Date | null;
	refundReason?: string | null;
	refundAmount?: number | null;
	stripeRefundId?: string | null;

	devices: Device[];
	devicesUsed: number;
	refundRequest?: RefundRequest;
}

export interface Device {
	id: string;
	licenseId: string;
	deviceHash: string;
	salt: string;
	firstSeen: Date;
	lastSeen: Date;
	appVersion?: string;
	isActive: boolean;

	// Enhanced device metadata
	deviceName?: string;
	deviceType?: string;
	deviceModel?: string;
	operatingSystem?: string;
	architecture?: string;
	screenResolution?: string;
	totalMemory?: string;
	userNickname?: string;
	location?: string;
	notes?: string;
}

export interface RefundRequest {
	id: string;
	licenseId: string;
	status: "PENDING" | "APPROVED" | "REJECTED" | "PROCESSED" | "FAILED";
	reason: string;
	amount?: number | null;
	requestedBy: string;
	adminNotes?: string | null;
	processedBy?: string | null;
	createdAt: Date;
	processedAt?: Date | null;
	license: License;
}

export interface Customer {
	id: string;
	email: string;
	name: string;
	countryCode: string;
	rfc?: string; // Mexican tax ID
	taxAddress?: {
		street: string;
		city: string;
		state: string;
		postalCode: string;
		country: string;
	};
	phone?: string;
	createdAt: Date;
	updatedAt: Date;
	licenses: License[];
	totalRevenue: number;
	totalLicenses: number;
	lastActivity: Date;
}

export interface Payment {
	id: string;
	stripePaymentIntentId: string;
	customerId: string;
	licenseId: string;
	amount: number;
	currency: Currency;
	taxAmount: number;
	status: "pending" | "succeeded" | "failed" | "canceled";
	paymentMethod: string;
	createdAt: Date;
	processedAt?: Date;
	customer: Customer;
	license: License;
}

export interface TaxTransaction {
	id: string;
	licenseId: string;
	taxRate: number;
	taxAmount: number;
	currency: Currency;
	createdAt: Date;
}

export interface Invoice {
	id: string;
	customerId: string;
	licenseId: string;
	invoiceNumber: string;
	amount: number;
	taxAmount: number;
	currency: Currency;
	status: "draft" | "sent" | "paid" | "overdue" | "canceled";
	generatedAt: Date;
	dueAt?: Date;
	paidAt?: Date;
}

// Dashboard Analytics Types
export interface DashboardStats {
	totalRevenue: number;
	monthlyRevenue: number;
	totalLicenses: number;
	activeLicenses: number;
	totalCustomers: number;
	activeDevices: number;
	pendingRefunds: number;
	systemHealth: "healthy" | "warning" | "critical";
	monthlyGrowthRate: number;
}

export interface RevenueData {
	date: string;
	revenue: number;
	currency: Currency;
}

export interface LicenseAnalytics {
	totalLicenses: number;
	standardLicenses: number;
	extendedLicenses: number;
	trialLicenses: number;
	conversionRate: number;
	averageDevicesPerLicense: number;
}

export interface CustomerAnalytics {
	totalCustomers: number;
	newCustomersThisMonth: number;
	customerAcquisitionCost: number;
	customerLifetimeValue: number;
	churnRate: number;
	averageRevenuePerUser: number;
}

export interface GeographicData {
	country: string;
	countryCode: string;
	revenue: number;
	customers: number;
	licenses: number;
}

export interface SystemHealth {
	apiResponseTime: number;
	databaseStatus: "healthy" | "warning" | "critical";
	stripeStatus: "healthy" | "warning" | "critical";
	emailServiceStatus: "healthy" | "warning" | "critical";
	backgroundJobsStatus: "healthy" | "warning" | "critical";
	memoryUsage: number;
	cpuUsage: number;
	errorRate: number;
}

// Filter and Search Types
export interface LicenseFilters {
	search?: string;
	licenseType?: "all" | "standard" | "extended";
	status?: "all" | "active" | "expired" | "refunded";
	dateRange?: {
		from: Date;
		to: Date;
	};
	page?: number;
	limit?: number;
}

export interface CustomerFilters {
	search?: string;
	country?: string;
	licenseType?: "all" | "standard" | "extended";
	registrationDateRange?: {
		from: Date;
		to: Date;
	};
	taxStatus?: "all" | "rfc_provided" | "rfc_missing";
	page?: number;
	limit?: number;
}

export interface PaymentFilters {
	search?: string;
	status?: "all" | "pending" | "succeeded" | "failed" | "canceled";
	currency?: "all" | "USD" | "MXN";
	dateRange?: {
		from: Date;
		to: Date;
	};
	page?: number;
	limit?: number;
}

// Form Types
export interface CreateLicenseForm {
	customerEmail: string;
	customerName: string;
	rfc?: string;
	taxAddress?: {
		street: string;
		city: string;
		state: string;
		postalCode: string;
		country: string;
	};
	phone?: string;
	licenseType: "standard" | "extended";
	additionalDevices: number;
	currency: Currency;
}

export interface RefundProcessForm {
	licenseId: string;
	reason: string;
	amount?: number;
	adminNotes?: string;
}

export interface TaxReportForm {
	month: number;
	year: number;
	reportType: "revenue" | "iva" | "customer";
	currency: Currency;
	exportFormat: "pdf" | "xml" | "csv";
}

// API Response Types
export interface PaginatedResponse<T> {
	data: T[];
	pagination: {
		page: number;
		limit: number;
		totalCount: number;
		totalPages: number;
		hasNextPage: boolean;
		hasPreviousPage: boolean;
	};
}

export interface ApiResponse<T> {
	success: boolean;
	data?: T;
	error?: string;
	message?: string;
}

// Chart Data Types
export interface ChartDataPoint {
	date: string;
	value: number;
	label?: string;
}

export interface PieChartData {
	name: string;
	value: number;
	color: string;
}

export interface BarChartData {
	category: string;
	value: number;
	color?: string;
}

// Mexican Tax Compliance Types
export interface MexicanTaxInfo {
	rfc: string;
	businessName: string;
	taxRegime: string;
	address: {
		street: string;
		exteriorNumber: string;
		interiorNumber?: string;
		neighborhood: string;
		municipality: string;
		state: string;
		country: string;
		postalCode: string;
	};
}

export interface IVACalculation {
	subtotal: number;
	ivaRate: number;
	ivaAmount: number;
	total: number;
	currency: Currency;
}

// Activity Feed Types
export interface ActivityItem {
	id: string;
	type:
		| "license_created"
		| "payment_received"
		| "refund_requested"
		| "device_added"
		| "license_upgraded";
	description: string;
	timestamp: Date;
	amount?: number;
	currency?: Currency;
	email?: string;
	licenseKey?: string;
}

// Navigation Types
export interface NavigationItem {
	id: string;
	label: string;
	href: string;
	icon: React.ComponentType<{ className?: string }>;
	badge?: number;
	children?: NavigationItem[];
}

export interface BreadcrumbItem {
	label: string;
	href?: string;
}

// Table Types
export interface TableColumn<T> {
	key: keyof T | string;
	label: string;
	sortable?: boolean;
	render?: (value: any, item: T) => React.ReactNode;
	className?: string;
}

export interface TableAction<T> {
	label: string;
	onClick: (item: T) => void;
	icon?: React.ComponentType<{ className?: string }>;
	variant?:
		| "default"
		| "destructive"
		| "outline"
		| "secondary"
		| "ghost"
		| "link";
}

// Modal Types
export interface ModalProps {
	isOpen: boolean;
	onClose: () => void;
	title: string;
	description?: string;
}

// Export Types
export interface ExportOptions {
	format: "csv" | "pdf" | "xlsx";
	filename?: string;
	includeHeaders?: boolean;
	dateRange?: {
		from: Date;
		to: Date;
	};
}
