import {
	type CreateCheckoutSessionRequest,
	type CreateCheckoutSessionResponse,
	createCheckoutSessionSchema,
	emailSchema,
	type LicenseType,
} from "@snapback/shared";

export class PurchaseError extends Error {
	constructor(
		message: string,
		public code?: string,
		public statusCode?: number,
	) {
		super(message);
		this.name = "PurchaseError";
	}
}

/**
 * Creates a Stripe checkout session and redirects to Stripe's hosted checkout page
 */
export async function createCheckoutSession(
	request: CreateCheckoutSessionRequest,
): Promise<CreateCheckoutSessionResponse> {
	const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000";

	try {
		const response = await fetch(
			`${apiUrl}/api/payments/create-checkout-session`,
			{
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(request),
			},
		);

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new PurchaseError(
				errorData.error || "Failed to create checkout session",
				errorData.code,
				response.status,
			);
		}

		const data = await response.json();
		return data;
	} catch (error) {
		if (error instanceof PurchaseError) {
			throw error;
		}

		// Network or other errors
		throw new PurchaseError(
			"Unable to connect to payment service. Please check your internet connection and try again.",
			"NETWORK_ERROR",
		);
	}
}

/**
 * Initiates the purchase flow for a license
 */
export async function purchaseLicense(
	licenseType: LicenseType,
	email: string,
	isDeviceExpansion = false,
	additionalDevices = 2,
): Promise<void> {
	// Validate email using shared schema
	const emailValidation = emailSchema.safeParse(email);
	if (!emailValidation.success) {
		throw new PurchaseError(
			emailValidation.error.issues[0]?.message || "Invalid email address",
			"VALIDATION_ERROR",
			400,
		);
	}

	// Get current domain for redirect URLs
	const baseUrl =
		typeof window !== "undefined"
			? window.location.origin
			: "http://localhost:3001";

	const successUrl = `${baseUrl}/success`;
	const cancelUrl = `${baseUrl}/error?error=cancelled`;

	// Validate the entire request using shared schema
	const requestData = {
		licenseType,
		email: emailValidation.data,
		successUrl,
		cancelUrl,
		isDeviceExpansion,
		additionalDevices,
	};

	const validation = createCheckoutSessionSchema.safeParse(requestData);
	if (!validation.success) {
		throw new PurchaseError(
			validation.error.issues[0]?.message || "Invalid request data",
			"VALIDATION_ERROR",
			400,
		);
	}

	try {
		// Create checkout session
		const session = await createCheckoutSession(validation.data);

		// Redirect to Stripe checkout
		if (typeof window !== "undefined") {
			window.location.href = session.url;
		}
	} catch (error) {
		console.error("Purchase error:", error);

		// Redirect to error page with error details
		if (typeof window !== "undefined") {
			const errorUrl = new URL(`${baseUrl}/error`);

			if (error instanceof PurchaseError) {
				errorUrl.searchParams.set("error", error.code || "unknown");
				errorUrl.searchParams.set("message", error.message);
			} else {
				errorUrl.searchParams.set("error", "unknown");
				errorUrl.searchParams.set("message", "An unexpected error occurred");
			}

			window.location.href = errorUrl.toString();
		}

		throw error;
	}
}

/**
 * Gets pricing information for display
 */
export function getPricingInfo() {
	return {
		pro: {
			price: 4.99,
			devices: 2,
			features: [
				"2 devices included",
				"Unlimited saved workspaces",
				"Cloud sync across devices",
				"Import/export workspaces",
				"Advanced window management",
				"Multi-monitor support",
				"Custom keyboard shortcuts",
				"App state preservation",
				"Priority email support",
			],
		},
	};
}

/**
 * Validates email format using shared schema
 */
export function isValidEmail(email: string): boolean {
	return emailSchema.safeParse(email).success;
}

/**
 * Formats price for display
 */
export function formatPrice(price: number): string {
	return new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: "USD",
	}).format(price);
}
