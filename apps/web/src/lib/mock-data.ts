// Mock data for SnapBack License Management Dashboard

import type {
	ActivityItem,
	Customer,
	CustomerAnalytics,
	DashboardStats,
	Device,
	GeographicData,
	Invoice,
	License,
	LicenseAnalytics,
	Payment,
	RefundRequest,
	RevenueData,
	SystemHealth,
	TaxTransaction,
} from "@/types/dashboard";

// Mock Devices
export const mockDevices: Device[] = [
	{
		id: "dev_1",
		licenseId: "lic_1",
		deviceHash: "hash_1234567890abcdef",
		salt: "salt_abcdef123456",
		firstSeen: new Date("2024-01-15T10:30:00Z"),
		lastSeen: new Date("2024-01-20T14:22:00Z"),
		appVersion: "1.2.3",
		isActive: true,
		deviceName: "MacBook Pro",
		deviceType: "MacBook Pro",
		deviceModel: "14-inch M2",
		operatingSystem: "macOS 14.1",
		architecture: "arm64",
		screenResolution: "3024x1964",
		totalMemory: "16 GB",
		userNickname: "Work Laptop",
		location: "Office",
		notes: "Primary development machine",
	},
	{
		id: "dev_2",
		licenseId: "lic_2",
		deviceHash: "hash_2345678901bcdefg",
		salt: "salt_bcdefg234567",
		firstSeen: new Date("2024-01-14T16:20:00Z"),
		lastSeen: new Date("2024-01-19T09:15:00Z"),
		appVersion: "1.2.2",
		isActive: true,
		deviceName: "iMac",
		deviceType: "iMac",
		deviceModel: "24-inch M1",
		operatingSystem: "macOS 13.6",
		architecture: "arm64",
		screenResolution: "4480x2520",
		totalMemory: "32 GB",
		userNickname: "Home Desktop",
		location: "Home",
	},
];

// Mock Customers
export const mockCustomers: Customer[] = [
	{
		id: "cust_1",
		email: "<EMAIL>",
		name: "Juan Pérez García",
		countryCode: "MX",
		rfc: "PEGJ850101ABC",
		taxAddress: {
			street: "Av. Insurgentes Sur 1234",
			city: "Ciudad de México",
			state: "CDMX",
			postalCode: "03100",
			country: "México",
		},
		phone: "+52 55 1234 5678",
		createdAt: new Date("2024-01-15T10:30:00Z"),
		updatedAt: new Date("2024-01-20T14:22:00Z"),
		licenses: [],
		totalRevenue: 499,
		totalLicenses: 1,
		lastActivity: new Date("2024-01-20T14:22:00Z"),
	},
	{
		id: "cust_2",
		email: "<EMAIL>",
		name: "María González López",
		countryCode: "MX",
		rfc: "GOLM900215XYZ",
		taxAddress: {
			street: "Blvd. Manuel Ávila Camacho 567",
			city: "Guadalajara",
			state: "Jalisco",
			postalCode: "44100",
			country: "México",
		},
		phone: "+52 33 9876 5432",
		createdAt: new Date("2024-01-14T16:20:00Z"),
		updatedAt: new Date("2024-01-19T09:15:00Z"),
		licenses: [],
		totalRevenue: 999,
		totalLicenses: 1,
		lastActivity: new Date("2024-01-19T09:15:00Z"),
	},
	{
		id: "cust_3",
		email: "<EMAIL>",
		name: "John Doe",
		countryCode: "US",
		createdAt: new Date("2024-01-13T09:45:00Z"),
		updatedAt: new Date("2024-01-18T11:30:00Z"),
		licenses: [],
		totalRevenue: 499,
		totalLicenses: 1,
		lastActivity: new Date("2024-01-18T11:30:00Z"),
	},
];

// Mock Licenses
export const mockLicenses: License[] = [
	{
		id: "lic_1",
		licenseKey: "ABCD-1234-EFGH-5678-IJKL",
		email: "<EMAIL>",
		licenseType: "standard",
		maxDevices: 2,
		expiresAt: null,
		createdAt: new Date("2024-01-15T10:30:00Z"),
		updatedAt: new Date("2024-01-15T10:30:00Z"),
		stripePaymentIntentId: "pi_1234567890",
		devices: [mockDevices[0]],
		devicesUsed: 1,
	},
	{
		id: "lic_2",
		licenseKey: "WXYZ-9876-ABCD-5432-EFGH",
		email: "<EMAIL>",
		licenseType: "extended",
		maxDevices: 5,
		expiresAt: null,
		createdAt: new Date("2024-01-14T16:20:00Z"),
		updatedAt: new Date("2024-01-14T16:20:00Z"),
		stripePaymentIntentId: "pi_2345678901",
		devices: [mockDevices[1]],
		devicesUsed: 3,
	},
	{
		id: "lic_3",
		licenseKey: "MNOP-2468-QRST-1357-UVWX",
		email: "<EMAIL>",
		licenseType: "standard",
		maxDevices: 2,
		expiresAt: null,
		createdAt: new Date("2024-01-13T09:45:00Z"),
		updatedAt: new Date("2024-01-13T09:45:00Z"),
		stripePaymentIntentId: "pi_3456789012",
		devices: [],
		devicesUsed: 2,
		refundedAt: new Date("2024-01-14T12:00:00Z"),
		refundReason: "Customer requested refund",
		refundAmount: 499,
		stripeRefundId: "re_1234567890",
	},
];

// Mock Payments
export const mockPayments: Payment[] = [
	{
		id: "pay_1",
		stripePaymentIntentId: "pi_1234567890",
		customerId: "cust_1",
		licenseId: "lic_1",
		amount: 499,
		currency: "USD",
		taxAmount: 0,
		status: "succeeded",
		paymentMethod: "card",
		createdAt: new Date("2024-01-15T10:30:00Z"),
		processedAt: new Date("2024-01-15T10:31:00Z"),
		customer: mockCustomers[0],
		license: mockLicenses[0],
	},
	{
		id: "pay_2",
		stripePaymentIntentId: "pi_2345678901",
		customerId: "cust_2",
		licenseId: "lic_2",
		amount: 999,
		currency: "USD",
		taxAmount: 159.84, // 16% IVA for Mexican customer
		status: "succeeded",
		paymentMethod: "card",
		createdAt: new Date("2024-01-14T16:20:00Z"),
		processedAt: new Date("2024-01-14T16:21:00Z"),
		customer: mockCustomers[1],
		license: mockLicenses[1],
	},
];

// Mock Refund Requests
export const mockRefundRequests: RefundRequest[] = [
	{
		id: "ref_1",
		licenseId: "lic_3",
		status: "PROCESSED",
		reason: "Product not as expected",
		amount: 499,
		requestedBy: "<EMAIL>",
		adminNotes: "Valid refund request, processed successfully",
		processedBy: "<EMAIL>",
		createdAt: new Date("2024-01-13T15:00:00Z"),
		processedAt: new Date("2024-01-14T12:00:00Z"),
		license: mockLicenses[2],
	},
];

// Mock Dashboard Stats
export const mockDashboardStats: DashboardStats = {
	totalRevenue: 25400,
	monthlyRevenue: 2150,
	totalLicenses: 1247,
	activeLicenses: 1198,
	totalCustomers: 892,
	activeDevices: 2156,
	pendingRefunds: 3,
	systemHealth: "healthy",
	monthlyGrowthRate: 12.5,
};

// Mock Revenue Data
export const mockRevenueData: RevenueData[] = [
	{ date: "2024-01-01", revenue: 1200, currency: "USD" },
	{ date: "2024-01-02", revenue: 1450, currency: "USD" },
	{ date: "2024-01-03", revenue: 1800, currency: "USD" },
	{ date: "2024-01-04", revenue: 1650, currency: "USD" },
	{ date: "2024-01-05", revenue: 2100, currency: "USD" },
	{ date: "2024-01-06", revenue: 2150, currency: "USD" },
	{ date: "2024-01-07", revenue: 1950, currency: "USD" },
];

// Mock License Analytics
export const mockLicenseAnalytics: LicenseAnalytics = {
	totalLicenses: 1247,
	standardLicenses: 898,
	extendedLicenses: 349,
	trialLicenses: 156,
	conversionRate: 72.5,
	averageDevicesPerLicense: 1.8,
};

// Mock Customer Analytics
export const mockCustomerAnalytics: CustomerAnalytics = {
	totalCustomers: 892,
	newCustomersThisMonth: 127,
	customerAcquisitionCost: 15.5,
	customerLifetimeValue: 28.5,
	churnRate: 2.1,
	averageRevenuePerUser: 28.47,
};

// Mock Geographic Data
export const mockGeographicData: GeographicData[] = [
	{
		country: "Mexico",
		countryCode: "MX",
		revenue: 12500,
		customers: 456,
		licenses: 623,
	},
	{
		country: "United States",
		countryCode: "US",
		revenue: 8900,
		customers: 298,
		licenses: 387,
	},
	{
		country: "Canada",
		countryCode: "CA",
		revenue: 2800,
		customers: 89,
		licenses: 142,
	},
	{
		country: "Spain",
		countryCode: "ES",
		revenue: 1200,
		customers: 49,
		licenses: 95,
	},
];

// Mock System Health
export const mockSystemHealth: SystemHealth = {
	apiResponseTime: 145,
	databaseStatus: "healthy",
	stripeStatus: "healthy",
	emailServiceStatus: "healthy",
	backgroundJobsStatus: "healthy",
	memoryUsage: 68.5,
	cpuUsage: 23.2,
	errorRate: 0.02,
};

// Mock Activity Items
export const mockActivityItems: ActivityItem[] = [
	{
		id: "act_1",
		type: "license_created",
		description: "New Standard license <NAME_EMAIL>",
		timestamp: new Date(Date.now() - 1000 * 60 * 15),
		amount: 499,
		currency: "USD",
		email: "<EMAIL>",
	},
	{
		id: "act_2",
		type: "payment_received",
		description: "Payment received for Extended license",
		timestamp: new Date(Date.now() - 1000 * 60 * 45),
		amount: 999,
		currency: "USD",
	},
	{
		id: "act_3",
		type: "device_added",
		description: "New device registered for license ABCD1234EFGH5678IJKL",
		timestamp: new Date(Date.now() - 1000 * 60 * 120),
		licenseKey: "ABCD1234EFGH5678IJKL",
	},
	{
		id: "act_4",
		type: "license_upgraded",
		description: "License upgraded with 2 additional devices",
		timestamp: new Date(Date.now() - 1000 * 60 * 180),
		amount: 198,
		currency: "USD",
	},
	{
		id: "act_5",
		type: "refund_requested",
		description: "Refund requested for license WXYZ9876ABCD5432EFGH",
		timestamp: new Date(Date.now() - 1000 * 60 * 240),
		licenseKey: "WXYZ9876ABCD5432EFGH",
	},
];

// Mock Tax Transactions
export const mockTaxTransactions: TaxTransaction[] = [
	{
		id: "tax_1",
		licenseId: "lic_2",
		taxRate: 0.16, // 16% IVA for Mexico
		taxAmount: 159.84,
		currency: "USD",
		createdAt: new Date("2024-01-14T16:20:00Z"),
	},
];

// Mock Invoices
export const mockInvoices: Invoice[] = [
	{
		id: "inv_1",
		customerId: "cust_2",
		licenseId: "lic_2",
		invoiceNumber: "INV-2024-001",
		amount: 999,
		taxAmount: 159.84,
		currency: "USD",
		status: "paid",
		generatedAt: new Date("2024-01-14T16:20:00Z"),
		paidAt: new Date("2024-01-14T16:21:00Z"),
	},
];

// Helper functions for mock data
export const getMockLicenses = (filters?: any) => {
	let filteredLicenses = [...mockLicenses];

	if (filters?.search) {
		const searchTerm = filters.search.toLowerCase();
		filteredLicenses = filteredLicenses.filter(
			(license) =>
				license.email.toLowerCase().includes(searchTerm) ||
				license.licenseKey.toLowerCase().includes(searchTerm),
		);
	}

	if (filters?.licenseType && filters.licenseType !== "all") {
		filteredLicenses = filteredLicenses.filter(
			(license) => license.licenseType === filters.licenseType,
		);
	}

	if (filters?.status && filters.status !== "all") {
		filteredLicenses = filteredLicenses.filter((license) => {
			if (filters.status === "active") {
				return (
					!license.refundedAt &&
					(!license.expiresAt || new Date(license.expiresAt) > new Date())
				);
			}
			if (filters.status === "refunded") {
				return !!license.refundedAt;
			}
			if (filters.status === "expired") {
				return license.expiresAt && new Date(license.expiresAt) <= new Date();
			}
			return true;
		});
	}

	return filteredLicenses;
};

export const getMockCustomers = (filters?: any) => {
	let filteredCustomers = [...mockCustomers];

	if (filters?.search) {
		const searchTerm = filters.search.toLowerCase();
		filteredCustomers = filteredCustomers.filter(
			(customer) =>
				customer.name.toLowerCase().includes(searchTerm) ||
				customer.email.toLowerCase().includes(searchTerm),
		);
	}

	if (filters?.country) {
		filteredCustomers = filteredCustomers.filter(
			(customer) => customer.countryCode === filters.country,
		);
	}

	return filteredCustomers;
};

export const getMockPayments = (filters?: any) => {
	let filteredPayments = [...mockPayments];

	if (filters?.status && filters.status !== "all") {
		filteredPayments = filteredPayments.filter(
			(payment) => payment.status === filters.status,
		);
	}

	if (filters?.currency && filters.currency !== "all") {
		filteredPayments = filteredPayments.filter(
			(payment) => payment.currency === filters.currency,
		);
	}

	return filteredPayments;
};
