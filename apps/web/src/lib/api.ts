import type {
	ApiResponse,
	CreateCheckoutSessionRequest,
	CreateUserRequest,
	GetDashboardStatsResponse,
	GetDeviceAnalyticsResponse,
	GetDeviceResponse,
	GetLicenseAnalyticsResponse,
	GetLicenseResponse,
	GetRevenueAnalyticsResponse,
	GetUserInvitationResponse,
	GetUserResponse,
	PaginatedResponse,
	PaginationMeta,
	PricingInfo,
	RequestRefundRequest,
	SendInvitationRequest,
	SendInvitationResponse,
	UpdateUserRequest,
	ValidateLicenseRequest,
	ValidateLicenseResponse,
} from "@snapback/shared";

const API_BASE_URL =
	process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api";

class ApiError extends Error {
	constructor(
		message: string,
		public status: number,
		public code?: string,
		public details?: Record<string, unknown>,
	) {
		super(message);
		this.name = "ApiError";
	}
}

async function apiRequest<T>(
	endpoint: string,
	options: RequestInit = {},
	retryCount = 0,
): Promise<T> {
	const url = `${API_BASE_URL}${endpoint}`;

	const config: RequestInit = {
		headers: {
			"Content-Type": "application/json",
			...options.headers,
		},
		credentials: "include", // Include cookies for authentication
		...options,
	};

	try {
		const response = await fetch(url, config);

		if (!response.ok) {
			const errorData: { error: string; code: string; details?: unknown } =
				await response.json().catch(() => ({
					error: "Unknown error occurred",
					code: "UNKNOWN_ERROR",
				}));

			// Handle 401 errors with session refresh
			if (response.status === 401 && retryCount === 0) {
				try {
					// Import authClient dynamically to avoid circular dependencies
					const { authClient } = await import("./auth-client");

					// Try to refresh the session
					const session = await authClient.getSession();

					if (session) {
						// Session is still valid, retry the request
						return apiRequest<T>(endpoint, options, retryCount + 1);
					}
				} catch (refreshError) {
					console.error("Session refresh failed:", refreshError);
					// Fall through to throw the original 401 error
				}
			}

			throw new ApiError(
				errorData.error || "Request failed",
				response.status,
				errorData.code,
				errorData.details as Record<string, unknown> | undefined,
			);
		}

		const responseData: ApiResponse<T> = await response.json();

		// Check if the response is in the expected ApiResponse format
		if (
			responseData &&
			typeof responseData === "object" &&
			"success" in responseData
		) {
			if (responseData.success) {
				return responseData.data;
			}
			// Handle API error responses
			throw new ApiError(
				responseData.error?.message ||
					responseData.message ||
					"API error occurred",
				response.status,
				responseData.error?.code || "API_ERROR",
				responseData.error?.details as Record<string, unknown> | undefined,
			);
		}

		// Fallback for non-standard responses
		return responseData as T;
	} catch (error) {
		if (error instanceof ApiError) {
			throw error;
		}

		throw new ApiError("Network error occurred", 0, "NETWORK_ERROR");
	}
}

// ============================================================================
// LICENSE API
// ============================================================================

export const licenseApi = {
	async validate(
		data: ValidateLicenseRequest,
	): Promise<ValidateLicenseResponse> {
		return apiRequest<ValidateLicenseResponse>("/licenses/validate", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},

	async ping(
		licenseKey: string,
		deviceId: string,
	): Promise<{ valid: boolean; message: string }> {
		return apiRequest<{ valid: boolean; message: string }>("/licenses/ping", {
			method: "POST",
			body: JSON.stringify({ licenseKey, deviceId }),
		});
	},

	async getStatus(licenseKey: string): Promise<{
		licenseKey: string;
		licenseType: string;
		status: string;
		maxDevices: number;
		usedDevices: number;
		expiresAt: string | null;
	}> {
		return apiRequest(`/licenses/${licenseKey}/status`);
	},

	async lookup(email: string): Promise<{
		licenseKey: string;
		licenseType: string;
		status: string;
		maxDevices: number;
		usedDevices: number;
		expiresAt: string | null;
	}> {
		return apiRequest("/licenses/lookup", {
			method: "POST",
			body: JSON.stringify({ email }),
		});
	},
};

// ============================================================================
// DEVICE API
// ============================================================================

export const deviceApi = {
	async register(data: {
		licenseKey: string;
		deviceId: string;
		appVersion?: string;
		deviceMetadata?: Record<string, unknown>;
	}): Promise<{
		id: string;
		status: string;
		firstSeen: string;
		lastSeen: string;
	}> {
		return apiRequest("/devices/register", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},

	async update(
		deviceHash: string,
		data: Record<string, unknown>,
	): Promise<{ message: string }> {
		return apiRequest(`/devices/${deviceHash}/update`, {
			method: "PATCH",
			body: JSON.stringify(data),
		});
	},

	async heartbeat(data: {
		licenseKey: string;
		deviceId: string;
		appVersion?: string;
	}): Promise<{ message: string }> {
		return apiRequest("/devices/heartbeat", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},
};

// ============================================================================
// ADMIN API (requires authentication)
// ============================================================================

export const adminApi = {
	// Dashboard & Analytics
	async getDashboardStats(
		period: "7d" | "30d" | "90d" | "1y" = "30d",
	): Promise<GetDashboardStatsResponse> {
		return apiRequest<GetDashboardStatsResponse>(
			`/admin/dashboard/stats?period=${period}`,
		);
	},

	async getRevenueAnalytics(
		period: "monthly" | "yearly" = "monthly",
		year?: number,
	): Promise<GetRevenueAnalyticsResponse> {
		const params = new URLSearchParams({ period });
		if (year) params.set("year", year.toString());
		return apiRequest<GetRevenueAnalyticsResponse>(
			`/admin/dashboard/revenue?${params.toString()}`,
		);
	},

	async getLicenseAnalytics(
		period: "7d" | "30d" | "90d" | "1y" = "30d",
		groupBy: "type" | "status" | "date" = "type",
	): Promise<GetLicenseAnalyticsResponse> {
		return apiRequest<GetLicenseAnalyticsResponse>(
			`/admin/dashboard/licenses?period=${period}&groupBy=${groupBy}`,
		);
	},

	async getDeviceAnalytics(
		period: "7d" | "30d" | "90d" | "1y" = "30d",
		groupBy: "registrations" | "active" | "date" = "registrations",
	): Promise<GetDeviceAnalyticsResponse> {
		return apiRequest<GetDeviceAnalyticsResponse>(
			`/admin/dashboard/devices?period=${period}&groupBy=${groupBy}`,
		);
	},

	// User Management
	async getUsers(params?: {
		page?: number;
		limit?: number;
		search?: string;
		role?: string;
		isActive?: boolean;
	}): Promise<PaginatedResponse<GetUserResponse>> {
		const searchParams = new URLSearchParams();
		if (params?.page) searchParams.set("page", params.page.toString());
		if (params?.limit) searchParams.set("limit", params.limit.toString());
		if (params?.search) searchParams.set("search", params.search);
		if (params?.role) searchParams.set("filters[role]", params.role);
		if (params?.isActive !== undefined)
			searchParams.set("filters[isActive]", params.isActive.toString());

		const query = searchParams.toString();
		return apiRequest<PaginatedResponse<GetUserResponse>>(
			`/admin/users${query ? `?${query}` : ""}`,
		);
	},

	async getUserById(userId: string): Promise<GetUserResponse> {
		return apiRequest<GetUserResponse>(`/admin/users/${userId}`);
	},

	async createUser(data: CreateUserRequest): Promise<GetUserResponse> {
		return apiRequest<GetUserResponse>("/admin/users", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},

	async updateUser(
		userId: string,
		data: UpdateUserRequest,
	): Promise<GetUserResponse> {
		return apiRequest<GetUserResponse>(`/admin/users/${userId}`, {
			method: "PATCH",
			body: JSON.stringify(data),
		});
	},

	async reactivateUser(userId: string): Promise<GetUserResponse> {
		return apiRequest<GetUserResponse>(`/admin/users/${userId}/reactivate`, {
			method: "POST",
		});
	},

	// User Invitations
	async getInvitations(params?: {
		page?: number;
		limit?: number;
		search?: string;
		status?: string;
	}): Promise<PaginatedResponse<GetUserInvitationResponse>> {
		const searchParams = new URLSearchParams();
		if (params?.page) searchParams.set("page", params.page.toString());
		if (params?.limit) searchParams.set("limit", params.limit.toString());
		if (params?.search) searchParams.set("search", params.search);
		if (params?.status) searchParams.set("filters[status]", params.status);

		const query = searchParams.toString();
		return apiRequest<PaginatedResponse<GetUserInvitationResponse>>(
			`/admin/invitations${query ? `?${query}` : ""}`,
		);
	},

	async sendInvitation(
		data: SendInvitationRequest,
	): Promise<SendInvitationResponse> {
		return apiRequest<SendInvitationResponse>("/admin/invitations", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},

	async resendInvitation(
		invitationId: string,
	): Promise<GetUserInvitationResponse> {
		return apiRequest<GetUserInvitationResponse>(
			`/admin/invitations/${invitationId}/resend`,
			{
				method: "POST",
			},
		);
	},

	async cancelInvitation(invitationId: string): Promise<{ message: string }> {
		return apiRequest<{ message: string }>(
			`/admin/invitations/${invitationId}`,
			{
				method: "DELETE",
			},
		);
	},

	// License Management (Admin)
	async getLicenses(params?: {
		page?: number;
		limit?: number;
		search?: string;
		status?: string;
		licenseType?: string;
		sortBy?: string;
		sortOrder?: "asc" | "desc";
	}): Promise<PaginatedResponse<GetLicenseResponse>> {
		const searchParams = new URLSearchParams();
		if (params?.page) searchParams.set("page", params.page.toString());
		if (params?.limit) searchParams.set("limit", params.limit.toString());
		if (params?.search) searchParams.set("search", params.search);
		if (params?.status) searchParams.set("filters[status]", params.status);
		if (params?.licenseType)
			searchParams.set("filters[licenseType]", params.licenseType);
		if (params?.sortBy) searchParams.set("sortBy", params.sortBy);
		if (params?.sortOrder) searchParams.set("sortOrder", params.sortOrder);

		const query = searchParams.toString();
		return apiRequest<PaginatedResponse<GetLicenseResponse>>(
			`/admin/licenses${query ? `?${query}` : ""}`,
		);
	},

	async getLicenseById(licenseId: string): Promise<GetLicenseResponse> {
		return apiRequest<GetLicenseResponse>(`/admin/licenses/${licenseId}`);
	},

	// Device Management (Admin)
	async getDevices(params?: {
		page?: number;
		limit?: number;
		search?: string;
		status?: string;
		licenseId?: string;
		sortBy?: string;
		sortOrder?: "asc" | "desc";
	}): Promise<PaginatedResponse<GetDeviceResponse>> {
		const searchParams = new URLSearchParams();
		if (params?.page) searchParams.set("page", params.page.toString());
		if (params?.limit) searchParams.set("limit", params.limit.toString());
		if (params?.search) searchParams.set("search", params.search);
		if (params?.status) searchParams.set("filters[status]", params.status);
		if (params?.licenseId)
			searchParams.set("filters[licenseId]", params.licenseId);
		if (params?.sortBy) searchParams.set("sortBy", params.sortBy);
		if (params?.sortOrder) searchParams.set("sortOrder", params.sortOrder);

		const query = searchParams.toString();
		return apiRequest<PaginatedResponse<GetDeviceResponse>>(
			`/admin/devices${query ? `?${query}` : ""}`,
		);
	},

	async getDeviceById(deviceId: string): Promise<GetDeviceResponse> {
		return apiRequest<GetDeviceResponse>(`/admin/devices/${deviceId}`);
	},
};

// ============================================================================
// PUBLIC API (no authentication required)
// ============================================================================

export const publicApi = {
	async validateInvitation(token: string): Promise<{
		valid: boolean;
		invitation?: {
			email: string;
			role: string;
			expiresAt: string;
		};
	}> {
		return apiRequest(`/invitations/${token}/validate`);
	},

	async contactSupport(data: {
		name: string;
		email: string;
		subject: string;
		message: string;
	}): Promise<{ message: string }> {
		return apiRequest("/public/support/contact", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},

	async getFaq(): Promise<
		Array<{
			id: string;
			question: string;
			answer: string;
			category: string;
		}>
	> {
		return apiRequest("/public/support/faq");
	},
};

// ============================================================================
// PAYMENT API
// ============================================================================

export const paymentApi = {
	async getPricing(): Promise<PricingInfo> {
		return apiRequest<PricingInfo>("/public/pricing");
	},

	async createCheckoutSession(
		data: CreateCheckoutSessionRequest,
	): Promise<{ url: string; sessionId: string }> {
		return apiRequest<{ url: string; sessionId: string }>(
			"/payments/create-checkout",
			{
				method: "POST",
				body: JSON.stringify(data),
			},
		);
	},

	async getPaymentDetails(paymentIntentId: string): Promise<{
		id: string;
		amount: number;
		currency: string;
		status: string;
		customerEmail: string;
	}> {
		return apiRequest(`/payments/${paymentIntentId}`);
	},
};

// ============================================================================
// REFUND API
// ============================================================================

export const refundApi = {
	async request(data: RequestRefundRequest): Promise<{
		success: boolean;
		message: string;
		refundRequestId?: string;
	}> {
		return apiRequest("/refunds/request", {
			method: "POST",
			body: JSON.stringify(data),
		});
	},

	async getStatus(requestId: string): Promise<{
		id: string;
		status: string;
		reason: string;
		amount?: number;
		createdAt: string;
		processedAt?: string;
	}> {
		return apiRequest(`/refunds/status/${requestId}`);
	},
};
