import { format, formatDistanceToNow } from "date-fns";

export function formatDate(dateString: string | Date) {
	if (dateString instanceof Date) {
		return format(dateString, "PPpp");
	}
	if (typeof dateString === "string") {
		return format(new Date(dateString), "PPpp");
	}
	return "";
}

export function formatRelativeDate(dateString: string | Date) {
	if (dateString instanceof Date) {
		return formatDistanceToNow(dateString, { addSuffix: true });
	}
	if (typeof dateString === "string") {
		return formatDistanceToNow(new Date(dateString), { addSuffix: true });
	}
	return "";
}

export function formatDateShort(dateString: string | Date) {
	if (dateString instanceof Date) {
		return format(dateString, "PP");
	}
	if (typeof dateString === "string") {
		return format(new Date(dateString), "PP");
	}
	return "";
}
