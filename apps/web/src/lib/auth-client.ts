import { inferAdditionalFields } from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";
export const authClient = createAuthClient({
	plugins: [
		inferAdditionalFields({
			user: {
				token: {
					type: "string",
					required: false,
					input: true,
				},
			},
		}),
	],
	baseURL: process.env.NEXT_PUBLIC_SERVER_URL,
	fetchOptions: {
		credentials: "include", // Include cookies for authentication
	},
});

export type Session = typeof authClient.$Infer.Session;
