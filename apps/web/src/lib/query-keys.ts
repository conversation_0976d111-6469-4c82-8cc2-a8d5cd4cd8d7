// Query key factory for consistent query key management
// This pattern helps with cache invalidation and prevents typos

export const queryKeys = {
	// Dashboard queries
	dashboard: {
		all: ["dashboard"] as const,
		stats: (period?: string) =>
			[...queryKeys.dashboard.all, "stats", { period }] as const,
		analytics: (timeRange?: string) =>
			[...queryKeys.dashboard.all, "analytics", { timeRange }] as const,
		recentActivity: (limit?: number) =>
			[...queryKeys.dashboard.all, "recent-activity", { limit }] as const,
		revenueAnalytics: (period?: string, year?: number) =>
			[
				...queryKeys.dashboard.all,
				"revenue-analytics",
				{ period, year },
			] as const,
		licenseAnalytics: (period?: string, groupBy?: string) =>
			[
				...queryKeys.dashboard.all,
				"license-analytics",
				{ period, groupBy },
			] as const,
		deviceAnalytics: (period?: string, groupBy?: string) =>
			[
				...queryKeys.dashboard.all,
				"device-analytics",
				{ period, groupBy },
			] as const,
	},

	// License queries
	licenses: {
		all: ["licenses"] as const,
		lists: () => [...queryKeys.licenses.all, "list"] as const,
		list: (filters: {
			page?: number;
			limit?: number;
			licenseType?: string;
			isActive?: boolean;
			isExpired?: boolean;
			search?: string;
			email?: string;
		}) => [...queryKeys.licenses.lists(), filters] as const,
		details: () => [...queryKeys.licenses.all, "detail"] as const,
		detail: (licenseKey: string) =>
			[...queryKeys.licenses.details(), licenseKey] as const,
		status: (licenseKey: string) =>
			[...queryKeys.licenses.all, "status", licenseKey] as const,
	},

	// Customer queries
	customers: {
		all: ["customers"] as const,
		lists: () => [...queryKeys.customers.all, "list"] as const,
		list: (filters: { page?: number; limit?: number }) =>
			[...queryKeys.customers.lists(), filters] as const,
	},

	// Payment queries
	payments: {
		all: ["payments"] as const,
		pricing: () => [...queryKeys.payments.all, "pricing"] as const,
		paymentIntent: (paymentIntentId: string) =>
			[...queryKeys.payments.all, "payment-intent", paymentIntentId] as const,
		checkoutSession: (sessionId: string) =>
			[...queryKeys.payments.all, "checkout-session", sessionId] as const,
	},

	// Refund queries
	refunds: {
		all: ["refunds"] as const,
		lists: () => [...queryKeys.refunds.all, "list"] as const,
		list: (filters: { page?: number; limit?: number; status?: string }) =>
			[...queryKeys.refunds.lists(), filters] as const,
		status: (licenseKey: string) =>
			[...queryKeys.refunds.all, "status", licenseKey] as const,
		history: (filters?: { page?: number; limit?: number; status?: string }) =>
			[...queryKeys.refunds.all, "history", filters] as const,
	},

	// Analytics queries
	analytics: {
		all: ["analytics"] as const,
		data: (timeRange: string) =>
			[...queryKeys.analytics.all, "data", { timeRange }] as const,
		revenue: (timeRange: string) =>
			[...queryKeys.analytics.all, "revenue", { timeRange }] as const,
		devices: () => [...queryKeys.analytics.all, "devices"] as const,
	},

	// User management queries
	users: {
		all: ["users"] as const,
		lists: () => [...queryKeys.users.all, "list"] as const,
		list: (filters: {
			page?: number;
			limit?: number;
			role?: string;
			isActive?: boolean;
			search?: string;
		}) => [...queryKeys.users.lists(), filters] as const,
		details: () => [...queryKeys.users.all, "detail"] as const,
		detail: (userId: string) => [...queryKeys.users.details(), userId] as const,
		currentUser: () => [...queryKeys.users.all, "current"] as const,
	},

	// User invitations queries
	invitations: {
		all: ["invitations"] as const,
		lists: () => [...queryKeys.invitations.all, "list"] as const,
		list: (filters: { page?: number; limit?: number; status?: string }) =>
			[...queryKeys.invitations.lists(), filters] as const,
	},
} as const;

// Helper function to invalidate related queries
export const getInvalidationKeys = {
	// When a license is created/updated, invalidate these queries
	onLicenseChange: () => [
		queryKeys.dashboard.stats(),
		queryKeys.dashboard.analytics(),
		queryKeys.licenses.all,
		queryKeys.analytics.all,
	],

	// When a payment is processed, invalidate these queries
	onPaymentChange: () => [
		queryKeys.dashboard.stats(),
		queryKeys.dashboard.analytics(),
		queryKeys.payments.all,
		queryKeys.analytics.revenue,
	],

	// When a refund is processed, invalidate these queries
	onRefundChange: () => [
		queryKeys.dashboard.stats(),
		queryKeys.refunds.all,
		queryKeys.analytics.all,
	],

	// When dashboard data needs refresh
	onDashboardRefresh: () => [queryKeys.dashboard.all, queryKeys.analytics.all],

	// When user data changes, invalidate these queries
	onUserChange: () => [
		queryKeys.users.all,
		queryKeys.dashboard.stats(),
		queryKeys.analytics.all,
	],

	// When invitation data changes, invalidate these queries
	onInvitationChange: () => [
		queryKeys.invitations.all,
		queryKeys.users.all,
		queryKeys.dashboard.stats(),
	],
};
