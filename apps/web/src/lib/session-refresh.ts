"use client";

import { authClient } from "./auth-client";

/**
 * Session Refresh Service
 *
 * Provides automatic session refresh functionality to prevent session expiration
 * during user activity. Integrates with Better Auth's session management.
 */

export interface SessionRefreshConfig {
	/** How often to check session status (in milliseconds) */
	checkInterval: number;
	/** How long before expiration to refresh (in milliseconds) */
	refreshThreshold: number;
	/** Maximum number of refresh attempts before giving up */
	maxRetries: number;
	/** Callback when session refresh fails */
	onRefreshFailed?: () => void;
	/** Callback when session is refreshed successfully */
	onRefreshSuccess?: () => void;
	/** Callback when session expires */
	onSessionExpired?: () => void;
}

export interface SessionInfo {
	isValid: boolean;
	expiresAt?: Date;
	timeUntilExpiry?: number;
	needsRefresh: boolean;
}

class SessionRefreshService {
	private config: SessionRefreshConfig;
	private intervalId: NodeJS.Timeout | null = null;
	private isRefreshing = false;
	private retryCount = 0;
	private isActive = false;

	constructor(config: Partial<SessionRefreshConfig> = {}) {
		this.config = {
			checkInterval: 60 * 1000, // Check every minute
			refreshThreshold: 15 * 60 * 1000, // Refresh 15 minutes before expiry
			maxRetries: 3,
			...config,
		};
	}

	/**
	 * Start automatic session monitoring and refresh
	 */
	start(): void {
		if (this.isActive) {
			return;
		}

		this.isActive = true;
		this.scheduleNextCheck();

		// Also check immediately
		this.checkAndRefreshSession();
	}

	/**
	 * Stop automatic session monitoring
	 */
	stop(): void {
		this.isActive = false;
		if (this.intervalId) {
			clearTimeout(this.intervalId);
			this.intervalId = null;
		}
	}

	/**
	 * Manually trigger session refresh
	 *
	 * Note: Better Auth automatically handles session refresh when updateAge is reached.
	 * This method primarily validates that the session is still active and triggers
	 * callbacks for successful refresh or session expiration.
	 */
	async refreshSession(): Promise<boolean> {
		if (this.isRefreshing) {
			return false;
		}

		this.isRefreshing = true;

		try {
			// Check if session is still valid - Better Auth handles automatic refresh
			const sessionResponse = await authClient.getSession();

			if (!sessionResponse?.data) {
				this.handleSessionExpired();
				return false;
			}

			// Session is still valid, reset retry count
			this.retryCount = 0;
			this.config.onRefreshSuccess?.();
			return true;
		} catch (error) {
			console.error("Session refresh failed:", error);
			this.retryCount++;

			if (this.retryCount >= this.config.maxRetries) {
				this.handleRefreshFailed();
				return false;
			}

			// Retry after a delay
			setTimeout(() => this.refreshSession(), 5000);
			return false;
		} finally {
			this.isRefreshing = false;
		}
	}

	/**
	 * Get current session information
	 */
	async getSessionInfo(): Promise<SessionInfo> {
		try {
			const sessionResponse = await authClient.getSession();

			if (!sessionResponse?.data) {
				return {
					isValid: false,
					needsRefresh: false,
				};
			}

			const sessionData = sessionResponse.data;

			// Better Auth provides session expiration directly in the session object
			const now = Date.now();
			let timeUntilExpiry: number;
			let expiresAt: Date;

			// Use the actual session expiration if available
			if (sessionData.session?.expiresAt) {
				expiresAt = new Date(sessionData.session.expiresAt);
				timeUntilExpiry = expiresAt.getTime() - now;
			} else {
				// Fallback to conservative estimate if expiration not available
				const sessionDuration = 7 * 24 * 60 * 60 * 1000; // 7 days in ms
				timeUntilExpiry = sessionDuration;
				expiresAt = new Date(now + timeUntilExpiry);
			}

			// Check if session needs refresh based on our threshold
			const needsRefresh = timeUntilExpiry <= this.config.refreshThreshold;

			return {
				isValid: true,
				expiresAt,
				timeUntilExpiry: Math.max(0, timeUntilExpiry), // Ensure non-negative
				needsRefresh,
			};
		} catch (error) {
			console.error("Failed to get session info:", error);
			return {
				isValid: false,
				needsRefresh: false,
			};
		}
	}

	/**
	 * Check session status and refresh if needed
	 */
	private async checkAndRefreshSession(): Promise<void> {
		if (!this.isActive) {
			return;
		}

		try {
			const sessionInfo = await this.getSessionInfo();

			if (!sessionInfo.isValid) {
				this.handleSessionExpired();
				return;
			}

			if (sessionInfo.needsRefresh) {
				await this.refreshSession();
			}
		} catch (error) {
			console.error("Session check failed:", error);
		}

		// Schedule next check
		this.scheduleNextCheck();
	}

	/**
	 * Schedule the next session check
	 */
	private scheduleNextCheck(): void {
		if (!this.isActive) {
			return;
		}

		this.intervalId = setTimeout(() => {
			this.checkAndRefreshSession();
		}, this.config.checkInterval);
	}

	/**
	 * Handle session expiration
	 */
	private handleSessionExpired(): void {
		this.stop();
		this.config.onSessionExpired?.();
	}

	/**
	 * Handle refresh failure
	 */
	private handleRefreshFailed(): void {
		this.stop();
		this.config.onRefreshFailed?.();
	}

	/**
	 * Update configuration
	 */
	updateConfig(newConfig: Partial<SessionRefreshConfig>): void {
		this.config = { ...this.config, ...newConfig };
	}
}

// Export singleton instance
export const sessionRefreshService = new SessionRefreshService();

// Export class for custom instances
export { SessionRefreshService };
