"use client";

import { motion } from "framer-motion";
import { ArrowLeft, Mail, RefreshCw, XCircle } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense } from "react";
import { Button } from "@/components/ui/button";

// Force dynamic rendering
export const dynamic = "force-dynamic";

function ErrorContent() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const error = searchParams?.get("error") || null;
	const sessionId = searchParams?.get("session_id") || null;

	const getErrorMessage = () => {
		switch (error) {
			case "payment_failed":
				return "Your payment could not be processed. Please check your payment method and try again.";
			case "session_expired":
				return "Your checkout session has expired. Please start the purchase process again.";
			case "cancelled":
				return "You cancelled the purchase process. No charges were made to your account.";
			default:
				return "Something went wrong during the purchase process. Please try again or contact support.";
		}
	};

	const getErrorTitle = () => {
		switch (error) {
			case "payment_failed":
				return "Payment Failed";
			case "session_expired":
				return "Session Expired";
			case "cancelled":
				return "Purchase Cancelled";
			default:
				return "Purchase Error";
		}
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-gray-50 px-6 py-24">
			<div className="mx-auto max-w-2xl">
				{/* Error Animation */}
				<motion.div
					initial={{ scale: 0 }}
					animate={{ scale: 1 }}
					transition={{ duration: 0.5, type: "spring" }}
					className="mb-8 text-center"
				>
					<div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-red-100">
						<XCircle className="h-12 w-12 text-red-600" />
					</div>
					<h1 className="mb-4 font-bold text-4xl text-gray-900">
						{getErrorTitle()}
					</h1>
					<p className="text-gray-600 text-xl">{getErrorMessage()}</p>
				</motion.div>

				{/* Error Details */}
				{sessionId && (
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6, delay: 0.2 }}
						className="mb-8 rounded-2xl bg-white p-8 shadow-lg"
					>
						<h2 className="mb-4 font-semibold text-gray-900 text-xl">
							Error Details
						</h2>
						<div className="rounded-lg bg-gray-50 p-4">
							<p className="font-medium text-gray-500 text-sm">Session ID</p>
							<p className="font-mono text-gray-900 text-sm">{sessionId}</p>
						</div>
						<p className="mt-4 text-gray-600 text-sm">
							Please include this session ID if you need to contact support.
						</p>
					</motion.div>
				)}

				{/* Help Section */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6, delay: 0.4 }}
					className="mb-8 rounded-2xl bg-blue-50 p-8"
				>
					<h2 className="mb-4 font-semibold text-2xl text-gray-900">
						Need Help?
					</h2>
					<div className="space-y-4">
						<div className="flex items-start gap-3">
							<RefreshCw className="mt-1 h-5 w-5 text-blue-600" />
							<div>
								<p className="font-medium text-gray-900">Try Again</p>
								<p className="text-gray-600">
									Most payment issues are temporary. Try the purchase process
									again.
								</p>
							</div>
						</div>
						<div className="flex items-start gap-3">
							<Mail className="mt-1 h-5 w-5 text-blue-600" />
							<div>
								<p className="font-medium text-gray-900">Contact Support</p>
								<p className="text-gray-600">
									If the problem persists, contact our support team at{" "}
									<a
										href="mailto:<EMAIL>"
										className="text-blue-600 hover:text-blue-700"
									>
										<EMAIL>
									</a>
								</p>
							</div>
						</div>
					</div>
				</motion.div>

				{/* Action Buttons */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6, delay: 0.6 }}
					className="flex flex-col gap-4 sm:flex-row sm:justify-center"
				>
					<Button
						size="lg"
						className="bg-blue-600 text-white hover:bg-blue-700"
						onClick={() => router.push("/#pricing")}
					>
						<RefreshCw className="mr-2 h-5 w-5" />
						Try Again
					</Button>
					<Button size="lg" variant="outline" onClick={() => router.push("/")}>
						<ArrowLeft className="mr-2 h-5 w-5" />
						Return to Home
					</Button>
				</motion.div>

				{/* Additional Information */}
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ duration: 0.6, delay: 0.8 }}
					className="mt-12 text-center"
				>
					<p className="text-gray-500 text-sm">
						All transactions are processed securely through Stripe.
						<br />
						No charges are made for failed or cancelled transactions.
					</p>
				</motion.div>
			</div>
		</div>
	);
}

export default function ErrorPage() {
	return (
		<Suspense
			fallback={
				<div className="flex min-h-screen items-center justify-center bg-gray-50">
					<div className="text-center">
						<div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent" />
						<p className="text-gray-600">Loading...</p>
					</div>
				</div>
			}
		>
			<ErrorContent />
		</Suspense>
	);
}
