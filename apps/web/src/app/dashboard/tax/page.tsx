"use client";

import { Calendar, Download, FileText, RefreshCw, Search } from "lucide-react";
// Import new reusable dashboard components
import {
	DashboardHeader,
	DataTable,
	EnhancedPagination,
	Filters,
} from "@/components/dashboard";
import { Badge } from "@/components/ui/badge";
import { useDashboardState } from "@/hooks/use-url-state";
import { formatDateShort } from "@/lib/dates";
import type {
	DashboardHeaderAction,
	FilterConfig,
	TableColumn,
	TableRowAction,
	TableSortConfig,
} from "@/types/dashboard-components";

interface TaxReport {
	id: string;
	month: string;
	year: number;
	totalRevenue: number;
	ivaCollected: number;
	transactionCount: number;
	customersWithRfc: number;
	status: "draft" | "submitted" | "approved";
	createdAt: Date;
	submittedAt?: Date;
}

// Mock tax report data
const mockTaxReports: TaxReport[] = [
	{
		id: "1",
		month: "January",
		year: 2024,
		totalRevenue: 2495,
		ivaCollected: 399,
		transactionCount: 5,
		customersWithRfc: 3,
		status: "approved",
		createdAt: new Date("2024-02-01T10:00:00Z"),
		submittedAt: new Date("2024-02-05T14:30:00Z"),
	},
	{
		id: "2",
		month: "February",
		year: 2024,
		totalRevenue: 1998,
		ivaCollected: 320,
		transactionCount: 4,
		customersWithRfc: 2,
		status: "submitted",
		createdAt: new Date("2024-03-01T10:00:00Z"),
		submittedAt: new Date("2024-03-03T16:15:00Z"),
	},
	{
		id: "3",
		month: "March",
		year: 2024,
		totalRevenue: 3492,
		ivaCollected: 559,
		transactionCount: 7,
		customersWithRfc: 5,
		status: "draft",
		createdAt: new Date("2024-04-01T10:00:00Z"),
	},
];

export default function TaxCompliancePage() {
	// Use URL state management for filters and pagination
	const {
		filters,
		updateFilter,
		page,
		limit,
		updatePage,
		updateLimit,
		sort,
		order,
		updateSort,
		resetAll,
	} = useDashboardState(
		{
			search: "",
			status: "",
			year: "2024",
		},
		1, // default page
		20, // default limit
	);

	// Filter reports
	const filteredReports = mockTaxReports.filter((report) => {
		const matchesSearch =
			!filters.search ||
			report.month.toLowerCase().includes(filters.search.toLowerCase());
		const matchesStatus =
			!filters.status ||
			filters.status === "all" ||
			report.status === filters.status;
		const matchesYear =
			!filters.year ||
			filters.year === "all" ||
			report.year.toString() === filters.year;

		return matchesSearch && matchesStatus && matchesYear;
	});

	// Apply sorting to reports data
	const sortedReports = [...filteredReports].sort((a, b) => {
		if (!sort) return 0;

		const aValue = a[sort as keyof TaxReport];
		const bValue = b[sort as keyof TaxReport];

		if (typeof aValue === "string" && typeof bValue === "string") {
			return order === "asc"
				? aValue.toLowerCase().localeCompare(bValue.toLowerCase())
				: bValue.toLowerCase().localeCompare(aValue.toLowerCase());
		}

		if (typeof aValue === "number" && typeof bValue === "number") {
			return order === "asc" ? aValue - bValue : bValue - aValue;
		}

		if (aValue instanceof Date && bValue instanceof Date) {
			return order === "asc"
				? aValue.getTime() - bValue.getTime()
				: bValue.getTime() - aValue.getTime();
		}

		return 0;
	});

	// Apply pagination
	const startIndex = (page - 1) * limit;
	const endIndex = startIndex + limit;
	const paginatedReports = sortedReports.slice(startIndex, endIndex);

	// Create pagination meta
	const paginationMeta = {
		page,
		limit,
		totalCount: sortedReports.length,
		totalPages: Math.ceil(sortedReports.length / limit),
		hasNextPage: endIndex < sortedReports.length,
		hasPreviousPage: page > 1,
	};

	const formatCurrency = (amount: number, currency = "MXN") => {
		return new Intl.NumberFormat("es-MX", {
			style: "currency",
			currency,
		}).format(amount / 100);
	};

	const getStatusBadge = (status: TaxReport["status"]) => {
		switch (status) {
			case "approved":
				return <Badge variant="default">Approved</Badge>;
			case "submitted":
				return <Badge variant="secondary">Submitted</Badge>;
			case "draft":
				return <Badge variant="outline">Draft</Badge>;
			default:
				return <Badge variant="outline">Unknown</Badge>;
		}
	};

	// Dashboard Header Configuration
	const headerActions: DashboardHeaderAction[] = [
		{
			label: "Generate Report",
			icon: FileText,
			onClick: () => {
				// TODO: Implement report generation
				console.log("Generate tax report");
			},
			variant: "default",
		},
		{
			label: "Export SAT XML",
			icon: Download,
			onClick: () => {
				// TODO: Implement SAT XML export
				console.log("Export SAT XML");
			},
			variant: "outline",
		},
		{
			label: "Refresh",
			icon: RefreshCw,
			onClick: () => {
				// TODO: Implement refresh functionality
				console.log("Refresh tax reports");
			},
			variant: "outline",
		},
	];

	// Filter Configuration
	const filterConfigs: FilterConfig[] = [
		{
			type: "search",
			key: "search",
			placeholder: "Search by month...",
			icon: Search,
		},
		{
			type: "select",
			key: "status",
			label: "Status",
			placeholder: "All Status",
			icon: FileText,
			options: [
				{ label: "Draft", value: "draft" },
				{ label: "Submitted", value: "submitted" },
				{ label: "Approved", value: "approved" },
			],
		},
		{
			type: "select",
			key: "year",
			label: "Year",
			placeholder: "All Years",
			icon: Calendar,
			options: [
				{ label: "2024", value: "2024" },
				{ label: "2023", value: "2023" },
				{ label: "2022", value: "2022" },
			],
		},
	];

	// Table Columns Configuration
	const columns: TableColumn<TaxReport>[] = [
		{
			key: "month",
			header: "Period",
			sortable: true,
			render: (_, report) => (
				<div>
					<div className="font-medium">
						{report.month} {report.year}
					</div>
					<div className="text-muted-foreground text-sm">
						{report.transactionCount} transactions
					</div>
				</div>
			),
			mobileRender: (report) => (
				<div>
					<div className="font-medium">
						{report.month} {report.year}
					</div>
					<div className="text-muted-foreground text-sm">
						{report.transactionCount} transactions
					</div>
				</div>
			),
		},
		{
			key: "totalRevenue",
			header: "Revenue",
			sortable: true,
			render: (value) => formatCurrency(value as number),
		},
		{
			key: "ivaCollected",
			header: "IVA Collected",
			sortable: true,
			render: (value) => formatCurrency(value as number),
		},
		{
			key: "customersWithRfc",
			header: "RFC Customers",
			sortable: true,
			render: (value) => (
				<div className="text-center">
					<span className="font-medium">{String(value)}</span>
				</div>
			),
		},
		{
			key: "status",
			header: "Status",
			sortable: true,
			render: (_, report) => getStatusBadge(report.status),
		},
		{
			key: "createdAt",
			header: "Created",
			sortable: true,
			render: (value) => formatDateShort(value as Date),
		},
		{
			key: "submittedAt",
			header: "Submitted",
			sortable: true,
			render: (value) => (value ? formatDateShort(value as Date) : "—"),
		},
	];

	// Table Row Actions Configuration
	const rowActions: TableRowAction<TaxReport>[] = [
		{
			label: "View Details",
			icon: FileText,
			onClick: (report) => {
				// TODO: Implement view details
				console.log("View tax report:", report.id);
			},
		},
		{
			label: "Download XML",
			icon: Download,
			onClick: (report) => {
				// TODO: Implement XML download
				console.log("Download XML for:", report.id);
			},
		},
	];

	// Handle sort changes
	const handleSort = (sortConfig: TableSortConfig) => {
		updateSort(sortConfig.key, sortConfig.direction);
	};

	// Handle filter changes
	const handleFilterChange = (
		key: string,
		value: string | string[] | boolean | undefined,
	) => {
		updateFilter(key as keyof typeof filters, value as string);
	};

	// Handle filter clear
	const handleFilterClear = () => {
		resetAll();
	};

	return (
		<div className="space-y-6">
			{/* Dashboard Header */}
			<DashboardHeader
				title="Tax Compliance (Mexico)"
				subtitle="SAT-compliant reporting and tax management"
				actions={headerActions}
			/>

			{/* Filters */}
			<Filters
				filters={filterConfigs}
				values={filters}
				onChange={handleFilterChange}
				onClear={handleFilterClear}
			/>

			{/* Data Table */}
			<DataTable
				data={paginatedReports}
				columns={columns}
				actions={rowActions}
				sortConfig={sort ? { key: sort, direction: order } : undefined}
				onSort={handleSort}
				emptyMessage="No tax reports found"
				emptyIcon={FileText}
			/>

			{/* Pagination */}
			<EnhancedPagination
				meta={paginationMeta}
				page={page}
				limit={limit}
				onPageChange={updatePage}
				onPageSizeChange={updateLimit}
				showInfo={true}
			/>
		</div>
	);
}
