"use client";

import { useState } from "react";
import { ChartAreaInteractive } from "@/components/chart-area-interactive";
import { SectionCards } from "@/components/section-cards";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { mockRevenueData } from "@/lib/mock-data";
import type { Currency } from "@/types/dashboard";

interface OverviewDashboardProps {
	className?: string;
}

export default function OverviewDashboard({
	className,
}: OverviewDashboardProps) {
	const [currency, setCurrency] = useState<Currency>("USD");
	const [timeRange, setTimeRange] = useState("7d");

	const revenueData = mockRevenueData;

	return (
		<div className={`space-y-6 ${className}`}>
			{/* Header with Controls */}
			<div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
				<div>
					<h1 className="font-bold text-3xl tracking-tight">
						Dashboard Overview
					</h1>
					<p className="text-muted-foreground">
						Welcome to your SnapBack license management dashboard
					</p>
				</div>
				<div className="flex gap-2">
					<Select
						value={currency}
						onValueChange={(value: Currency) => setCurrency(value)}
					>
						<SelectTrigger className="w-[100px]">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="USD">USD</SelectItem>
							<SelectItem value="MXN">MXN</SelectItem>
						</SelectContent>
					</Select>
					<Select value={timeRange} onValueChange={setTimeRange}>
						<SelectTrigger className="w-[120px]">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="7d">Last 7 days</SelectItem>
							<SelectItem value="30d">Last 30 days</SelectItem>
							<SelectItem value="90d">Last 90 days</SelectItem>
							<SelectItem value="1y">Last year</SelectItem>
						</SelectContent>
					</Select>
				</div>
			</div>

			<SectionCards />

			<ChartAreaInteractive />
		</div>
	);
}
