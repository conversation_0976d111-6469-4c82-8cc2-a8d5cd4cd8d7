/**
 * User Management Page
 * Admin page for managing users and invitations with RBAC
 */

import type { Metadata } from "next";
import {
	DebugPermissions,
	UserManagementDashboard,
} from "@/components/user-management";

export const metadata: Metadata = {
	title: "User Management | SnapBack Admin",
	description: "Manage user accounts, roles, and invitations",
};

export default function UserManagementPage() {
	return (
		<div className="space-y-6">
			{/* <DebugPermissions /> */}
			<UserManagementDashboard />
		</div>
	);
}
