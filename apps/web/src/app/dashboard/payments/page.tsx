"use client";

import { CreditCard, Download, Eye, RefreshCw, Search } from "lucide-react";
import { useState } from "react";
// Import new reusable dashboard components
import {
	DashboardHeader,
	DataTable,
	EnhancedPagination,
	Filters,
} from "@/components/dashboard";
import { Badge } from "@/components/ui/badge";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useDashboardState } from "@/hooks/use-url-state";
import { formatDate } from "@/lib/dates";
import type {
	DashboardHeaderAction,
	FilterConfig,
	TableColumn,
	TableRowAction,
	TableSortConfig,
} from "@/types/dashboard-components";

interface Payment {
	id: string;
	customerEmail: string;
	customerName: string;
	amount: number;
	currency: string;
	status: "completed" | "pending" | "failed" | "refunded";
	paymentMethod: string;
	stripePaymentId: string;
	licenseType: "standard" | "extended";
	createdAt: Date;
	refundedAt?: Date;
	refundAmount?: number;
}

// Mock payment data
const mockPayments: Payment[] = [
	{
		id: "1",
		customerEmail: "<EMAIL>",
		customerName: "John Doe",
		amount: 499,
		currency: "USD",
		status: "completed",
		paymentMethod: "card",
		stripePaymentId: "pi_1234567890",
		licenseType: "standard",
		createdAt: new Date("2024-01-15T10:30:00Z"),
	},
	{
		id: "2",
		customerEmail: "<EMAIL>",
		customerName: "Jane Smith",
		amount: 999,
		currency: "USD",
		status: "completed",
		paymentMethod: "card",
		stripePaymentId: "pi_0987654321",
		licenseType: "extended",
		createdAt: new Date("2024-01-14T16:21:00Z"),
	},
	{
		id: "3",
		customerEmail: "<EMAIL>",
		customerName: "Bob Wilson",
		amount: 499,
		currency: "USD",
		status: "refunded",
		paymentMethod: "card",
		stripePaymentId: "pi_1122334455",
		licenseType: "standard",
		createdAt: new Date("2024-01-10T09:15:00Z"),
		refundedAt: new Date("2024-01-12T14:30:00Z"),
		refundAmount: 499,
	},
];

export default function PaymentsPage() {
	const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
	const [isDetailsOpen, setIsDetailsOpen] = useState(false);

	// Use URL state management for filters and pagination
	const {
		filters,
		updateFilter,
		page,
		limit,
		updatePage,
		updateLimit,
		sort,
		order,
		updateSort,
		resetAll,
	} = useDashboardState(
		{
			search: "",
			status: "",
		},
		1, // default page
		20, // default limit
	);

	// Filter payments based on search and status
	const filteredPayments = mockPayments.filter((payment) => {
		const matchesSearch =
			!filters.search ||
			payment.customerEmail
				.toLowerCase()
				.includes(filters.search.toLowerCase()) ||
			payment.customerName
				.toLowerCase()
				.includes(filters.search.toLowerCase()) ||
			payment.stripePaymentId
				.toLowerCase()
				.includes(filters.search.toLowerCase());

		const matchesStatus =
			!filters.status ||
			filters.status === "all" ||
			payment.status === filters.status;

		return matchesSearch && matchesStatus;
	});

	// Apply sorting
	const sortedPayments = [...filteredPayments].sort((a, b) => {
		if (!sort) return 0;

		const aValue = a[sort as keyof Payment];
		const bValue = b[sort as keyof Payment];

		if (typeof aValue === "string" && typeof bValue === "string") {
			return order === "asc"
				? aValue.toLowerCase().localeCompare(bValue.toLowerCase())
				: bValue.toLowerCase().localeCompare(aValue.toLowerCase());
		}

		if (typeof aValue === "number" && typeof bValue === "number") {
			return order === "asc" ? aValue - bValue : bValue - aValue;
		}

		if (aValue instanceof Date && bValue instanceof Date) {
			return order === "asc"
				? aValue.getTime() - bValue.getTime()
				: bValue.getTime() - aValue.getTime();
		}

		return 0;
	});

	// Apply pagination
	const startIndex = (page - 1) * limit;
	const endIndex = startIndex + limit;
	const paginatedPayments = sortedPayments.slice(startIndex, endIndex);

	// Create pagination meta
	const paginationMeta = {
		page,
		limit,
		totalCount: sortedPayments.length,
		totalPages: Math.ceil(sortedPayments.length / limit),
		hasNextPage: endIndex < sortedPayments.length,
		hasPreviousPage: page > 1,
	};

	const formatCurrency = (amount: number, currency = "USD") => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency,
		}).format(amount / 100);
	};

	const getStatusBadge = (status: Payment["status"]) => {
		switch (status) {
			case "completed":
				return <Badge variant="default">Completed</Badge>;
			case "pending":
				return <Badge variant="secondary">Pending</Badge>;
			case "failed":
				return <Badge variant="destructive">Failed</Badge>;
			case "refunded":
				return <Badge variant="outline">Refunded</Badge>;
			default:
				return <Badge variant="outline">Unknown</Badge>;
		}
	};

	const openPaymentDetails = (payment: Payment) => {
		setSelectedPayment(payment);
		setIsDetailsOpen(true);
	};

	// Dashboard Header Configuration
	const headerActions: DashboardHeaderAction[] = [
		{
			label: "Export",
			icon: Download,
			onClick: () => {
				// TODO: Implement export functionality
				console.log("Export payments");
			},
			variant: "outline",
		},
		{
			label: "Sync Stripe",
			icon: RefreshCw,
			onClick: () => {
				// TODO: Implement Stripe sync
				console.log("Sync with Stripe");
			},
			variant: "outline",
		},
	];

	// Filter Configuration
	const filterConfigs: FilterConfig[] = [
		{
			type: "search",
			key: "search",
			placeholder: "Search payments...",
			icon: Search,
		},
		{
			type: "select",
			key: "status",
			label: "Status",
			placeholder: "All Status",
			icon: CreditCard,
			options: [
				{ label: "Completed", value: "completed" },
				{ label: "Pending", value: "pending" },
				{ label: "Failed", value: "failed" },
				{ label: "Refunded", value: "refunded" },
			],
		},
	];

	// Table Columns Configuration
	const columns: TableColumn<Payment>[] = [
		{
			key: "customerName",
			header: "Customer",
			sortable: true,
			render: (_, payment) => (
				<div>
					<div className="font-medium">{payment.customerName}</div>
					<div className="text-muted-foreground text-sm">
						{payment.customerEmail}
					</div>
				</div>
			),
			mobileRender: (payment) => (
				<div>
					<div className="font-medium">{payment.customerName}</div>
					<div className="text-muted-foreground text-sm">
						{payment.customerEmail}
					</div>
				</div>
			),
		},
		{
			key: "amount",
			header: "Amount",
			sortable: true,
			render: (_, payment) => formatCurrency(payment.amount, payment.currency),
		},
		{
			key: "status",
			header: "Status",
			sortable: true,
			render: (_, payment) => getStatusBadge(payment.status),
		},
		{
			key: "licenseType",
			header: "License",
			sortable: true,
			render: (value) => (
				<Badge variant="outline">
					{value === "standard" ? "Standard" : "Extended"}
				</Badge>
			),
		},
		{
			key: "createdAt",
			header: "Date",
			sortable: true,
			render: (value) => formatDate(value as Date),
		},
		{
			key: "stripePaymentId",
			header: "Payment ID",
			render: (value) => <code className="text-xs">{String(value)}</code>,
		},
	];

	// Table Row Actions Configuration
	const rowActions: TableRowAction<Payment>[] = [
		{
			label: "View Details",
			icon: Eye,
			onClick: openPaymentDetails,
		},
	];

	// Handle sort changes
	const handleSort = (sortConfig: TableSortConfig) => {
		updateSort(sortConfig.key, sortConfig.direction);
	};

	// Handle filter changes
	const handleFilterChange = (
		key: string,
		value: string | string[] | boolean | undefined,
	) => {
		updateFilter(key as keyof typeof filters, value as string);
	};

	// Handle filter clear
	const handleFilterClear = () => {
		resetAll();
	};

	return (
		<div className="space-y-6">
			{/* Dashboard Header */}
			<DashboardHeader
				title="Payment & Billing"
				subtitle="Track payments, process refunds, and manage billing"
				actions={headerActions}
			/>

			{/* Filters */}
			<Filters
				filters={filterConfigs}
				values={filters}
				onChange={handleFilterChange}
				onClear={handleFilterClear}
			/>

			{/* Data Table */}
			<DataTable
				data={paginatedPayments}
				columns={columns}
				actions={rowActions}
				sortConfig={sort ? { key: sort, direction: order } : undefined}
				onSort={handleSort}
				emptyMessage="No payments found"
				emptyIcon={CreditCard}
			/>

			{/* Pagination */}
			<EnhancedPagination
				meta={paginationMeta}
				page={page}
				limit={limit}
				onPageChange={updatePage}
				onPageSizeChange={updateLimit}
				showInfo={true}
			/>

			{/* Payment Details Modal */}
			<Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
				<DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
					<DialogHeader>
						<DialogTitle>Payment Details</DialogTitle>
						<DialogDescription>
							Detailed information about this payment transaction
						</DialogDescription>
					</DialogHeader>
					{selectedPayment && (
						<div className="grid gap-4 py-4">
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label className="font-medium text-sm">Customer</Label>
									<p className="mt-1">{selectedPayment.customerName}</p>
									<p className="text-muted-foreground text-sm">
										{selectedPayment.customerEmail}
									</p>
								</div>
								<div>
									<Label className="font-medium text-sm">Status</Label>
									<div className="mt-1">
										{getStatusBadge(selectedPayment.status)}
									</div>
								</div>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label className="font-medium text-sm">Amount</Label>
									<p className="mt-1 font-bold text-lg">
										{formatCurrency(
											selectedPayment.amount,
											selectedPayment.currency,
										)}
									</p>
								</div>
								<div>
									<Label className="font-medium text-sm">License Type</Label>
									<p className="mt-1 capitalize">
										{selectedPayment.licenseType}
									</p>
								</div>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label className="font-medium text-sm">Payment Method</Label>
									<p className="mt-1 capitalize">
										{selectedPayment.paymentMethod}
									</p>
								</div>
								<div>
									<Label className="font-medium text-sm">Date</Label>
									<p className="mt-1">
										{formatDate(selectedPayment.createdAt)}
									</p>
								</div>
							</div>
							<div>
								<Label className="font-medium text-sm">Stripe Payment ID</Label>
								<p className="mt-1 font-mono text-sm">
									{selectedPayment.stripePaymentId}
								</p>
							</div>
							{selectedPayment.refundedAt && (
								<div className="grid grid-cols-2 gap-4">
									<div>
										<Label className="font-medium text-sm">Refund Date</Label>
										<p className="mt-1">
											{formatDate(selectedPayment.refundedAt)}
										</p>
									</div>
									<div>
										<Label className="font-medium text-sm">Refund Amount</Label>
										<p className="mt-1">
											{selectedPayment.refundAmount
												? formatCurrency(
														selectedPayment.refundAmount,
														selectedPayment.currency,
													)
												: "N/A"}
										</p>
									</div>
								</div>
							)}
						</div>
					)}
				</DialogContent>
			</Dialog>
		</div>
	);
}
