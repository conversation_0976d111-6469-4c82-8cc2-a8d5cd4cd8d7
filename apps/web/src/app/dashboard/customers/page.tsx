"use client";

import {
	Download,
	Edit,
	Eye,
	Filter,
	Mail,
	MapPin,
	Plus,
	Receipt,
	Search,
	User,
} from "lucide-react";
import { useState } from "react";
// Import new reusable dashboard components
import {
	DashboardHeader,
	DashboardListSkeleton,
	DataTable,
	EnhancedPagination,
	Filters,
} from "@/components/dashboard";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useDashboardState } from "@/hooks/use-url-state";
import { formatDateShort } from "@/lib/dates";
import { getMockCustomers } from "@/lib/mock-data";
import type { Customer, CustomerFilters } from "@/types/dashboard";
import type {
	DashboardHeaderAction,
	FilterConfig,
	TableColumn,
	TableRowAction,
	TableSortConfig,
} from "@/types/dashboard-components";

export default function CustomersPage() {
	const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
		null,
	);
	const [isDetailsOpen, setIsDetailsOpen] = useState(false);
	const [isCreateCustomerOpen, setIsCreateCustomerOpen] = useState(false);

	// Use URL state management for filters and pagination
	const {
		filters,
		updateFilter,
		page,
		limit,
		updatePage,
		updateLimit,
		sort,
		order,
		updateSort,
		resetAll,
	} = useDashboardState(
		{
			search: "",
			country: "",
			taxStatus: "",
		},
		1, // default page
		20, // default limit
	);

	// Mock data with filters
	const customerFilters: CustomerFilters = {
		search: filters.search || "",
		country: filters.country === "" ? undefined : filters.country,
		taxStatus:
			filters.taxStatus === ""
				? undefined
				: (filters.taxStatus as "rfc_provided" | "rfc_missing"),
	};

	const allCustomers = getMockCustomers(customerFilters);

	// Apply sorting
	const sortedCustomers = [...allCustomers].sort((a, b) => {
		if (!sort) return 0;

		const aValue = a[sort as keyof Customer];
		const bValue = b[sort as keyof Customer];

		// Handle different data types
		if (typeof aValue === "string" && typeof bValue === "string") {
			return order === "asc"
				? aValue.toLowerCase().localeCompare(bValue.toLowerCase())
				: bValue.toLowerCase().localeCompare(aValue.toLowerCase());
		}

		if (typeof aValue === "number" && typeof bValue === "number") {
			return order === "asc" ? aValue - bValue : bValue - aValue;
		}

		if (aValue instanceof Date && bValue instanceof Date) {
			return order === "asc"
				? aValue.getTime() - bValue.getTime()
				: bValue.getTime() - aValue.getTime();
		}

		return 0;
	});

	// Apply pagination
	const startIndex = (page - 1) * limit;
	const endIndex = startIndex + limit;
	const paginatedCustomers = sortedCustomers.slice(startIndex, endIndex);

	// Create pagination meta
	const paginationMeta = {
		page,
		limit,
		totalCount: sortedCustomers.length,
		totalPages: Math.ceil(sortedCustomers.length / limit),
		hasNextPage: endIndex < sortedCustomers.length,
		hasPreviousPage: page > 1,
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
		}).format(amount / 100);
	};

	const getCountryFlag = (countryCode: string) => {
		const flags: Record<string, string> = {
			MX: "🇲🇽",
			US: "🇺🇸",
			CA: "🇨🇦",
			ES: "🇪🇸",
		};
		return flags[countryCode] || "🌍";
	};

	const getCountryName = (countryCode: string) => {
		const countries: Record<string, string> = {
			MX: "Mexico",
			US: "United States",
			CA: "Canada",
			ES: "Spain",
		};
		return countries[countryCode] || countryCode;
	};

	const getRfcStatus = (customer: Customer) => {
		if (customer.countryCode === "MX") {
			return customer.rfc ? "rfc_provided" : "rfc_missing";
		}
		return "not_applicable";
	};

	const getRfcBadge = (customer: Customer) => {
		const status = getRfcStatus(customer);
		switch (status) {
			case "rfc_provided":
				return <Badge variant="default">RFC Provided</Badge>;
			case "rfc_missing":
				return <Badge variant="destructive">RFC Missing</Badge>;
			case "not_applicable":
				return <Badge variant="secondary">N/A</Badge>;
			default:
				return <Badge variant="outline">Unknown</Badge>;
		}
	};

	const openCustomerDetails = (customer: Customer) => {
		setSelectedCustomer(customer);
		setIsDetailsOpen(true);
	};

	// Dashboard Header Configuration
	const headerActions: DashboardHeaderAction[] = [
		{
			label: "Export",
			icon: Download,
			onClick: () => {
				// TODO: Implement export functionality
				console.log("Export customers");
			},
			variant: "outline",
		},
		{
			label: "Add Customer",
			icon: Plus,
			onClick: () => setIsCreateCustomerOpen(true),
			variant: "default",
		},
	];

	// Filter Configuration
	const filterConfigs: FilterConfig[] = [
		{
			type: "search",
			key: "search",
			placeholder: "Search customers...",
			icon: Search,
		},
		{
			type: "select",
			key: "country",
			label: "Country",
			placeholder: "All Countries",
			icon: MapPin,
			options: [
				{ label: "🇲🇽 Mexico", value: "MX" },
				{ label: "🇺🇸 United States", value: "US" },
				{ label: "🇨🇦 Canada", value: "CA" },
				{ label: "🇪🇸 Spain", value: "ES" },
			],
		},
		{
			type: "select",
			key: "taxStatus",
			label: "Tax Status",
			placeholder: "All Status",
			icon: Filter,
			options: [
				{ label: "RFC Provided", value: "rfc_provided" },
				{ label: "RFC Missing", value: "rfc_missing" },
			],
		},
	];

	// Table Columns Configuration
	const columns: TableColumn<Customer>[] = [
		{
			key: "name",
			header: "Customer",
			sortable: true,
			render: (_, customer) => (
				<div className="flex items-center space-x-3">
					<div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
						<User className="h-4 w-4 text-gray-600" />
					</div>
					<div>
						<div className="font-medium">{customer.name}</div>
						<div className="text-muted-foreground text-sm">
							{customer.email}
						</div>
					</div>
				</div>
			),
			mobileRender: (customer) => (
				<div className="flex items-center space-x-3">
					<div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
						<User className="h-4 w-4 text-gray-600" />
					</div>
					<div>
						<div className="font-medium">{customer.name}</div>
						<div className="text-muted-foreground text-sm">
							{customer.email}
						</div>
					</div>
				</div>
			),
		},
		{
			key: "countryCode",
			header: "Country",
			sortable: true,
			render: (_, customer) => (
				<div className="flex items-center space-x-2">
					<span className="text-lg">
						{getCountryFlag(customer.countryCode)}
					</span>
					<span>{getCountryName(customer.countryCode)}</span>
				</div>
			),
		},
		{
			key: "totalLicenses",
			header: "Licenses",
			sortable: true,
			render: (value) => (
				<Badge variant="outline">{String(value)} licenses</Badge>
			),
		},
		{
			key: "totalRevenue",
			header: "Revenue",
			sortable: true,
			render: (value) => formatCurrency(value as number),
		},
		{
			key: "rfc",
			header: "Tax Status",
			render: (_, customer) => getRfcBadge(customer),
		},
		{
			key: "lastActivity",
			header: "Last Activity",
			sortable: true,
			render: (value) => formatDateShort(value as string),
		},
	];

	// Table Row Actions Configuration
	const rowActions: TableRowAction<Customer>[] = [
		{
			label: "View Profile",
			icon: Eye,
			onClick: openCustomerDetails,
		},
		{
			label: "Edit Customer",
			icon: Edit,
			onClick: (customer) => {
				// TODO: Implement edit functionality
				console.log("Edit customer:", customer.id);
			},
		},
		{
			label: "Send Email",
			icon: Mail,
			onClick: (customer) => {
				// TODO: Implement email functionality
				console.log("Send email to:", customer.email);
			},
		},
		{
			label: "Generate Invoice",
			icon: Receipt,
			onClick: (customer) => {
				// TODO: Implement invoice generation
				console.log("Generate invoice for:", customer.id);
			},
			separator: true,
		},
	];

	// Handle sort changes
	const handleSort = (sortConfig: TableSortConfig) => {
		updateSort(sortConfig.key, sortConfig.direction);
	};

	// Handle filter changes
	const handleFilterChange = (
		key: string,
		value: string | string[] | boolean | undefined,
	) => {
		updateFilter(key as keyof typeof filters, value as string);
	};

	// Handle filter clear
	const handleFilterClear = () => {
		resetAll();
	};

	return (
		<div className="space-y-6">
			{/* Dashboard Header */}
			<DashboardHeader
				title="Customer Management"
				subtitle="Manage customer information and tax compliance"
				actions={headerActions}
			/>

			{/* Filters */}
			<Filters
				filters={filterConfigs}
				values={filters}
				onChange={handleFilterChange}
				onClear={handleFilterClear}
			/>

			{/* Data Table */}
			<DataTable
				data={paginatedCustomers}
				columns={columns}
				actions={rowActions}
				sortConfig={sort ? { key: sort, direction: order } : undefined}
				onSort={handleSort}
				emptyMessage="No customers found"
				emptyIcon={User}
			/>

			{/* Pagination */}
			<EnhancedPagination
				meta={paginationMeta}
				page={page}
				limit={limit}
				onPageChange={updatePage}
				onPageSizeChange={updateLimit}
				showInfo={true}
			/>

			{/* Add Customer Dialog */}
			<Dialog
				open={isCreateCustomerOpen}
				onOpenChange={setIsCreateCustomerOpen}
			>
				<DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
					<DialogHeader>
						<DialogTitle>Add New Customer</DialogTitle>
						<DialogDescription>
							Add a new customer to the system with tax information.
						</DialogDescription>
					</DialogHeader>
					<div className="grid gap-4 py-4">
						{/* Mobile-first form layout */}
						<div className="grid grid-cols-1 items-start gap-2 sm:grid-cols-4 sm:items-center sm:gap-4">
							<Label
								htmlFor="customerName"
								className="font-medium sm:text-right"
							>
								Name
							</Label>
							<Input
								id="customerName"
								placeholder="Customer Name"
								className="sm:col-span-3"
							/>
						</div>
						<div className="grid grid-cols-1 items-start gap-2 sm:grid-cols-4 sm:items-center sm:gap-4">
							<Label
								htmlFor="customerEmail"
								className="font-medium sm:text-right"
							>
								Email
							</Label>
							<Input
								id="customerEmail"
								type="email"
								placeholder="<EMAIL>"
								className="sm:col-span-3"
							/>
						</div>
						<div className="grid grid-cols-1 items-start gap-2 sm:grid-cols-4 sm:items-center sm:gap-4">
							<Label htmlFor="country" className="font-medium sm:text-right">
								Country
							</Label>
							<Select>
								<SelectTrigger className="sm:col-span-3">
									<SelectValue placeholder="Select country" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="MX">🇲🇽 Mexico</SelectItem>
									<SelectItem value="US">🇺🇸 United States</SelectItem>
									<SelectItem value="CA">🇨🇦 Canada</SelectItem>
									<SelectItem value="ES">🇪🇸 Spain</SelectItem>
								</SelectContent>
							</Select>
						</div>
						<div className="grid grid-cols-1 items-start gap-2 sm:grid-cols-4 sm:items-center sm:gap-4">
							<Label
								htmlFor="customerRfc"
								className="font-medium sm:text-right"
							>
								RFC (Mexico)
							</Label>
							<Input
								id="customerRfc"
								placeholder="ABCD123456EFG (optional)"
								className="sm:col-span-3"
							/>
						</div>
						<div className="grid grid-cols-1 items-start gap-2 sm:grid-cols-4 sm:items-center sm:gap-4">
							<Label htmlFor="phone" className="font-medium sm:text-right">
								Phone
							</Label>
							<Input
								id="phone"
								placeholder="+52 55 1234 5678"
								className="sm:col-span-3"
							/>
						</div>
					</div>
					<div className="flex justify-end space-x-2">
						<Button
							variant="outline"
							onClick={() => setIsCreateCustomerOpen(false)}
						>
							Cancel
						</Button>
						<Button onClick={() => setIsCreateCustomerOpen(false)}>
							Add Customer
						</Button>
					</div>
				</DialogContent>
			</Dialog>

			{/* Customer Details Modal */}
			<Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
				<DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[700px]">
					<DialogHeader>
						<DialogTitle>Customer Profile</DialogTitle>
						<DialogDescription>
							Detailed customer information and tax compliance status
						</DialogDescription>
					</DialogHeader>
					{selectedCustomer && (
						<div className="grid gap-4 py-4">
							<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
								<div>
									<Label className="font-medium text-sm">Name</Label>
									<p className="mt-1">{selectedCustomer.name}</p>
								</div>
								<div>
									<Label className="font-medium text-sm">Email</Label>
									<p className="mt-1">{selectedCustomer.email}</p>
								</div>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<Label className="font-medium text-sm">Country</Label>
									<div className="mt-1 flex items-center space-x-2">
										<span className="text-lg">
											{getCountryFlag(selectedCustomer.countryCode)}
										</span>
										<span>{getCountryName(selectedCustomer.countryCode)}</span>
									</div>
								</div>
								<div>
									<Label className="font-medium text-sm">Tax Status</Label>
									<div className="mt-1">{getRfcBadge(selectedCustomer)}</div>
								</div>
							</div>
							{selectedCustomer.rfc && (
								<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
									<div>
										<Label className="font-medium text-sm">RFC Number</Label>
										<p className="mt-1 font-mono text-sm">
											{selectedCustomer.rfc}
										</p>
									</div>
									<div>
										<Label className="font-medium text-sm">Phone</Label>
										<p className="mt-1">
											{selectedCustomer.phone || "Not provided"}
										</p>
									</div>
								</div>
							)}
							{selectedCustomer.taxAddress && (
								<div>
									<Label className="font-medium text-sm">Tax Address</Label>
									<div className="mt-1 rounded-md bg-muted p-3 text-sm">
										<p>{selectedCustomer.taxAddress.street}</p>
										<p>
											{selectedCustomer.taxAddress.city},{" "}
											{selectedCustomer.taxAddress.state}{" "}
											{selectedCustomer.taxAddress.postalCode}
										</p>
										<p>{selectedCustomer.taxAddress.country}</p>
									</div>
								</div>
							)}
							<div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
								<div>
									<Label className="font-medium text-sm">Total Licenses</Label>
									<p className="mt-1 font-bold text-2xl">
										{selectedCustomer.totalLicenses}
									</p>
								</div>
								<div>
									<Label className="font-medium text-sm">Total Revenue</Label>
									<p className="mt-1 font-bold text-2xl text-green-600">
										{formatCurrency(selectedCustomer.totalRevenue)}
									</p>
								</div>
								<div>
									<Label className="font-medium text-sm">Last Activity</Label>
									<p className="mt-1">
										{formatDate(selectedCustomer.lastActivity)}
									</p>
								</div>
							</div>
						</div>
					)}
				</DialogContent>
			</Dialog>
		</div>
	);
}
