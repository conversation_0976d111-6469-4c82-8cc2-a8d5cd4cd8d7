"use client";

import type { LicenseType } from "@snapback/shared";
import { Download, Eye, Key, RefreshCw, Search } from "lucide-react";
// Import new reusable dashboard components
import {
	DashboardHeader,
	DataTable,
	EnhancedPagination,
	Filters,
} from "@/components/dashboard";
import { Badge } from "@/components/ui/badge";
import { useLicenses } from "@/hooks/use-license-management";
import { useDashboardState } from "@/hooks/use-url-state";
import { formatDate, formatDateShort } from "@/lib/dates";
import type {
	DashboardHeaderAction,
	FilterConfig,
	TableColumn,
	TableRowAction,
	TableSortConfig,
} from "@/types/dashboard-components";

export default function LicensesPage() {
	// Use URL state management for filters and pagination
	const {
		filters,
		updateFilter,
		page,
		limit,
		updatePage,
		updateLimit,
		sort,
		order,
		updateSort,
		resetAll,
	} = useDashboardState(
		{
			search: "",
			email: "",
			licenseType: "",
			isActive: "",
			isExpired: "",
		},
		1, // default page
		20, // default limit
	);

	// Fetch licenses with current filters
	const { data: licensesData, error } = useLicenses({
		page,
		limit,
		licenseType:
			filters.licenseType === ""
				? undefined
				: (filters.licenseType as LicenseType),
		isActive: filters.isActive === "" ? undefined : filters.isActive === "true",
		isExpired:
			filters.isExpired === "" ? undefined : filters.isExpired === "true",
		search: filters.search || undefined,
		email: filters.email || undefined,
	});

	// Apply sorting to licenses data
	const sortedLicenses = licensesData?.licenses
		? [...licensesData.licenses].sort((a, b) => {
				if (!sort) return 0;

				const aValue = a[sort as keyof typeof a];
				const bValue = b[sort as keyof typeof b];

				if (typeof aValue === "string" && typeof bValue === "string") {
					return order === "asc"
						? aValue.toLowerCase().localeCompare(bValue.toLowerCase())
						: bValue.toLowerCase().localeCompare(aValue.toLowerCase());
				}

				if (typeof aValue === "number" && typeof bValue === "number") {
					return order === "asc" ? aValue - bValue : bValue - aValue;
				}

				if (aValue instanceof Date && bValue instanceof Date) {
					return order === "asc"
						? aValue.getTime() - bValue.getTime()
						: bValue.getTime() - aValue.getTime();
				}

				return 0;
			})
		: [];

	// Create pagination meta from the API response
	const paginationMeta = licensesData
		? {
				page: licensesData.pagination.page,
				limit: licensesData.pagination.limit,
				totalCount: licensesData.pagination.totalCount,
				totalPages: licensesData.pagination.totalPages,
				hasNextPage: licensesData.pagination.hasNextPage,
				hasPreviousPage: licensesData.pagination.hasPreviousPage,
			}
		: {
				page: 1,
				limit: 20,
				totalCount: 0,
				totalPages: 0,
				hasNextPage: false,
				hasPreviousPage: false,
			};

	const getLicenseTypeBadge = (type: string) => {
		const variants = {
			trial: "secondary",
			standard: "default",
			extended: "destructive",
		} as const;

		return (
			<Badge variant={variants[type as keyof typeof variants] || "outline"}>
				{type.charAt(0).toUpperCase() + type.slice(1)}
			</Badge>
		);
	};

	const getStatusBadge = (isActive: boolean, isExpired: boolean) => {
		if (!isActive) {
			return <Badge variant="destructive">Refunded</Badge>;
		}
		if (isExpired) {
			return <Badge variant="secondary">Expired</Badge>;
		}
		return <Badge variant="default">Active</Badge>;
	};

	// Dashboard Header Configuration
	const headerActions: DashboardHeaderAction[] = [
		{
			label: "Export",
			icon: Download,
			onClick: () => {
				// TODO: Implement export functionality
				console.log("Export licenses");
			},
			variant: "outline",
		},
		{
			label: "Refresh",
			icon: RefreshCw,
			onClick: () => {
				// TODO: Implement refresh functionality
				console.log("Refresh licenses");
			},
			variant: "outline",
		},
	];

	// Filter Configuration
	const filterConfigs: FilterConfig[] = [
		{
			type: "search",
			key: "search",
			placeholder: "Search licenses...",
			icon: Search,
		},
		{
			type: "search",
			key: "email",
			placeholder: "Search by email...",
			icon: Search,
		},
		{
			type: "select",
			key: "licenseType",
			label: "License Type",
			placeholder: "All Types",
			icon: Key,
			options: [
				{ label: "Trial", value: "trial" },
				{ label: "Standard", value: "standard" },
				{ label: "Extended", value: "extended" },
			],
		},
		{
			type: "boolean",
			key: "isActive",
			label: "Status",
			trueLabel: "Active",
			falseLabel: "Inactive",
			icon: Key,
		},
		{
			type: "boolean",
			key: "isExpired",
			label: "Expiry",
			trueLabel: "Expired",
			falseLabel: "Not Expired",
			icon: Key,
		},
	];

	// Table Columns Configuration
	const columns: TableColumn[] = [
		{
			key: "email",
			header: "User Email",
			sortable: true,
		},
		{
			key: "licenseType",
			header: "Type",
			sortable: true,
			render: (value) => getLicenseTypeBadge(value as string),
		},
		{
			key: "isActive",
			header: "Status",
			render: (_, license) =>
				getStatusBadge(
					license.isActive as boolean,
					license.isExpired as boolean,
				),
		},
		{
			key: "createdAt",
			header: "Created",
			sortable: true,
			render: (value) => formatDateShort(value as string),
		},
		{
			key: "expiresAt",
			header: "Expires",
			sortable: true,
			render: (value) => (value ? formatDateShort(value as string) : "Never"),
		},
	];

	// Table Row Actions Configuration
	const rowActions: TableRowAction[] = [
		{
			label: "View Details",
			icon: Eye,
			onClick: (license) => {
				// TODO: Implement view details
				console.log("View license:", license.id);
			},
		},
	];

	// Handle sort changes
	const handleSort = (sortConfig: TableSortConfig) => {
		updateSort(sortConfig.key, sortConfig.direction);
	};

	// Handle filter changes
	const handleFilterChange = (
		key: string,
		value: string | string[] | boolean | undefined,
	) => {
		updateFilter(key as keyof typeof filters, value as string);
	};

	// Handle filter clear
	const handleFilterClear = () => {
		resetAll();
	};

	if (error) {
		return (
			<div className="container mx-auto py-8">
				<div className="text-center">
					<h1 className="mb-4 font-bold text-2xl text-red-600">Error</h1>
					<p className="text-gray-600">
						Failed to load licenses. Please try again later.
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Dashboard Header */}
			<DashboardHeader
				title="License Management"
				subtitle="Manage and track all software licenses"
				actions={headerActions}
			/>

			{/* Filters */}
			<Filters
				filters={filterConfigs}
				values={filters}
				onChange={handleFilterChange}
				onClear={handleFilterClear}
			/>

			{/* Data Table */}
			<DataTable
				data={sortedLicenses as unknown as Record<string, unknown>[]}
				columns={columns}
				actions={rowActions}
				sortConfig={sort ? { key: sort, direction: order } : undefined}
				onSort={handleSort}
				emptyMessage="No licenses found"
				emptyIcon={Key}
			/>

			{/* Pagination */}
			<EnhancedPagination
				meta={paginationMeta}
				page={page}
				limit={limit}
				onPageChange={updatePage}
				onPageSizeChange={updateLimit}
				showInfo={true}
			/>
		</div>
	);
}
