"use client";

import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>,
	Download,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
	TrendingUp,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import {
	Area,
	AreaChart,
	Bar,
	BarChart,
	CartesianGrid,
	Cell,
	Pie,
	<PERSON>hart,
	XAxis,
	YAxis,
} from "recharts";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	ChartContainer,
	ChartLegend,
	ChartLegendContent,
	ChartTooltip,
	ChartTooltipContent,
} from "@/components/ui/chart";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

// Force dynamic rendering
export const dynamic = "force-dynamic";

interface AnalyticsData {
	revenue: {
		total: number;
		monthly: number;
		growth: number;
		chartData: { month: string; amount: number }[];
	};
	licenses: {
		total: number;
		monthly: number;
		growth: number;
		byType: { standard: number; extended: number };
	};
	customers: {
		total: number;
		monthly: number;
		growth: number;
		retention: number;
	};
	refunds: {
		total: number;
		rate: number;
		amount: number;
	};
}

export default function DashboardAnalytics() {
	const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
	const [loading, setLoading] = useState(true);
	const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d" | "1y">(
		"30d",
	);

	const fetchAnalytics = useCallback(async () => {
		try {
			setLoading(true);

			// TODO: Replace with actual API call
			// const response = await fetch(`/api/admin/analytics?range=${timeRange}`);
			// const data = await response.json();

			// Mock data for now
			const mockAnalytics: AnalyticsData = {
				revenue: {
					total: 8435.5,
					monthly: 2150.75,
					growth: 12.5,
					chartData: [
						{ month: "Jan", amount: 1200 },
						{ month: "Feb", amount: 1450 },
						{ month: "Mar", amount: 1800 },
						{ month: "Apr", amount: 1650 },
						{ month: "May", amount: 2100 },
						{ month: "Jun", amount: 2150 },
					],
				},
				licenses: {
					total: 1247,
					monthly: 89,
					growth: 8.3,
					byType: { standard: 892, extended: 355 },
				},
				customers: {
					total: 892,
					monthly: 67,
					growth: 15.2,
					retention: 94.5,
				},
				refunds: {
					total: 23,
					rate: 1.8,
					amount: 127.45,
				},
			};

			setAnalytics(mockAnalytics);
		} catch (error) {
			console.error("Failed to fetch analytics:", error);
		} finally {
			setLoading(false);
		}
	}, []);

	// Load mock data on component mount and when time range changes
	useEffect(() => {
		fetchAnalytics();
	}, [fetchAnalytics]);

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
		}).format(amount);
	};

	if (loading) {
		return (
			<div className="flex min-h-screen items-center justify-center">
				<div className="text-center">
					<div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent" />
					<p className="text-gray-600">Loading analytics...</p>
				</div>
			</div>
		);
	}

	if (!analytics) {
		return (
			<div className="flex min-h-screen items-center justify-center">
				<div className="text-center">
					<p className="text-gray-600">Failed to load analytics data</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
				<div>
					<h1 className="font-bold text-3xl tracking-tight">
						Analytics & Reports
					</h1>
					<p className="text-muted-foreground">
						Detailed insights and performance metrics for your business
					</p>
				</div>
				<div className="flex gap-2">
					<Select
						value={timeRange}
						onValueChange={(value: "7d" | "30d" | "90d" | "1y") =>
							setTimeRange(value)
						}
					>
						<SelectTrigger className="w-[140px]">
							<Calendar className="mr-2 h-4 w-4" />
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="7d">Last 7 days</SelectItem>
							<SelectItem value="30d">Last 30 days</SelectItem>
							<SelectItem value="90d">Last 90 days</SelectItem>
							<SelectItem value="1y">Last year</SelectItem>
						</SelectContent>
					</Select>
					<Button variant="outline">
						<Download className="mr-2 h-4 w-4" />
						Export Report
					</Button>
				</div>
			</div>

			{/* Charts and Detailed Analytics */}
			<div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
				{/* Revenue Chart */}
				<Card>
					<CardHeader>
						<CardTitle>Revenue Trend</CardTitle>
						<CardDescription>
							Monthly revenue over the last 6 months
						</CardDescription>
					</CardHeader>
					<CardContent>
						<ChartContainer
							config={{
								revenue: {
									label: "Revenue",
									color: "hsl(var(--chart-1))",
								},
							}}
							className="min-h-[200px] w-full"
						>
							<AreaChart accessibilityLayer data={analytics.revenue.chartData}>
								<CartesianGrid vertical={false} />
								<XAxis
									dataKey="month"
									tickLine={false}
									tickMargin={10}
									axisLine={false}
									tickFormatter={(value) => value.slice(0, 3)}
								/>
								<YAxis
									tickLine={false}
									axisLine={false}
									tickMargin={8}
									tickFormatter={(value) => formatCurrency(value)}
								/>
								<defs>
									<linearGradient id="fillRevenue" x1="0" y1="0" x2="0" y2="1">
										<stop
											offset="5%"
											stopColor="var(--color-revenue)"
											stopOpacity={0.8}
										/>
										<stop
											offset="95%"
											stopColor="var(--color-revenue)"
											stopOpacity={0.1}
										/>
									</linearGradient>
								</defs>
								<Area
									dataKey="amount"
									type="monotone"
									fill="url(#fillRevenue)"
									fillOpacity={0.4}
									stroke="var(--color-revenue)"
									strokeWidth={2}
								/>
								<ChartTooltip
									content={
										<ChartTooltipContent
											formatter={(value) => [
												formatCurrency(Number(value)),
												"Revenue",
											]}
										/>
									}
								/>
							</AreaChart>
						</ChartContainer>
					</CardContent>
				</Card>

				{/* License Distribution */}
				<Card>
					<CardHeader>
						<CardTitle>License Distribution</CardTitle>
						<CardDescription>Breakdown of license types</CardDescription>
					</CardHeader>
					<CardContent className="overflow-hidden">
						<ChartContainer
							config={{
								standard: {
									label: "Standard",
									color: "hsl(var(--chart-1))",
								},
								extended: {
									label: "Extended",
									color: "hsl(var(--chart-2))",
								},
							}}
							className="min-h-[200px] w-full"
						>
							<PieChart accessibilityLayer>
								<Pie
									data={[
										{
											name: "Standard",
											value: analytics.licenses.byType.standard,
											fill: "var(--color-standard)",
										},
										{
											name: "Extended",
											value: analytics.licenses.byType.extended,
											fill: "var(--color-extended)",
										},
									]}
									cx="50%"
									cy="50%"
									outerRadius={80}
									dataKey="value"
									label={({ name, value }) => `${name}: ${value}`}
									labelLine={false}
								>
									{[
										{
											name: "Standard",
											value: analytics.licenses.byType.standard,
											fill: "var(--color-standard)",
										},
										{
											name: "Extended",
											value: analytics.licenses.byType.extended,
											fill: "var(--color-extended)",
										},
									].map((entry) => (
										<Cell key={`cell-${entry.name}`} fill={entry.fill} />
									))}
								</Pie>
								<ChartTooltip
									content={
										<ChartTooltipContent
											formatter={(value) => [
												`${Number(value).toLocaleString()} licenses`,
												"Count",
											]}
										/>
									}
								/>
								<ChartLegend content={<ChartLegendContent nameKey="name" />} />
							</PieChart>
						</ChartContainer>
					</CardContent>
				</Card>
			</div>

			{/* Additional Metrics */}
			<Card>
				<CardHeader>
					<CardTitle>Key Performance Indicators</CardTitle>
					<CardDescription>Important metrics at a glance</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
						<div className="space-y-2 text-center">
							<p className="font-bold text-2xl">
								{formatCurrency(analytics.revenue.monthly)}
							</p>
							<p className="text-muted-foreground text-sm">Monthly Revenue</p>
						</div>
						<div className="space-y-2 text-center">
							<p className="font-bold text-2xl">{analytics.licenses.monthly}</p>
							<p className="text-muted-foreground text-sm">Monthly Licenses</p>
						</div>
						<div className="space-y-2 text-center">
							<p className="font-bold text-2xl">
								{analytics.customers.retention}%
							</p>
							<p className="text-muted-foreground text-sm">
								Customer Retention
							</p>
						</div>
						<div className="space-y-2 text-center">
							<p className="font-bold text-2xl">{analytics.refunds.total}</p>
							<p className="text-muted-foreground text-sm">Total Refunds</p>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Additional Analytics Charts */}
			<div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
				{/* License Type Distribution */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<PieChartIcon className="h-5 w-5" />
							License Type Distribution
						</CardTitle>
						<CardDescription>Breakdown of license types sold</CardDescription>
					</CardHeader>
					<CardContent className="overflow-hidden">
						<ChartContainer
							config={{
								standard: {
									label: "Standard",
									color: "hsl(var(--chart-1))",
								},
								extended: {
									label: "Extended",
									color: "hsl(var(--chart-2))",
								},
							}}
							className="min-h-[180px] w-full"
						>
							<PieChart accessibilityLayer>
								<Pie
									data={[
										{
											name: "Standard",
											value: 65,
											fill: "var(--color-standard)",
										},
										{
											name: "Extended",
											value: 35,
											fill: "var(--color-extended)",
										},
									]}
									cx="50%"
									cy="50%"
									outerRadius={60}
									dataKey="value"
									label={({ name, value }) => `${name}: ${value}%`}
									labelLine={false}
								>
									<Cell fill="var(--color-standard)" />
									<Cell fill="var(--color-extended)" />
								</Pie>
								<ChartTooltip
									content={
										<ChartTooltipContent
											formatter={(value) => [`${value}%`, "Percentage"]}
										/>
									}
								/>
								<ChartLegend content={<ChartLegendContent nameKey="name" />} />
							</PieChart>
						</ChartContainer>
					</CardContent>
				</Card>

				{/* Customer Growth Chart */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<TrendingUp className="h-5 w-5" />
							Customer Growth
						</CardTitle>
						<CardDescription>New customers acquired over time</CardDescription>
					</CardHeader>
					<CardContent>
						<ChartContainer
							config={{
								customers: {
									label: "New Customers",
									color: "hsl(var(--chart-3))",
								},
							}}
							className="min-h-[180px] w-full"
						>
							<BarChart
								accessibilityLayer
								data={[
									{ month: "Jan", customers: 45 },
									{ month: "Feb", customers: 52 },
									{ month: "Mar", customers: 48 },
									{ month: "Apr", customers: 61 },
									{ month: "May", customers: 55 },
									{ month: "Jun", customers: 67 },
								]}
							>
								<CartesianGrid vertical={false} />
								<XAxis
									dataKey="month"
									tickLine={false}
									tickMargin={10}
									axisLine={false}
									tickFormatter={(value) => value.slice(0, 3)}
								/>
								<YAxis
									tickLine={false}
									axisLine={false}
									tickMargin={8}
									tickFormatter={(value) => `${value}`}
								/>
								<Bar
									dataKey="customers"
									fill="var(--color-customers)"
									radius={[4, 4, 0, 0]}
								/>
								<ChartTooltip
									content={
										<ChartTooltipContent
											formatter={(value) => [
												`${value} customers`,
												"New Customers",
											]}
										/>
									}
								/>
							</BarChart>
						</ChartContainer>
					</CardContent>
				</Card>

				{/* Geographic Distribution */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<MapPin className="h-5 w-5" />
							Geographic Distribution
						</CardTitle>
						<CardDescription>Customer distribution by country</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<span className="text-lg">🇲🇽</span>
									<span className="font-medium">Mexico</span>
								</div>
								<div className="flex items-center gap-2">
									<div className="h-2 w-20 rounded-full bg-gray-200">
										<div
											className="h-2 rounded-full bg-blue-600"
											style={{ width: "45%" }}
										/>
									</div>
									<span className="font-medium text-sm">45%</span>
								</div>
							</div>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<span className="text-lg">🇺🇸</span>
									<span className="font-medium">United States</span>
								</div>
								<div className="flex items-center gap-2">
									<div className="h-2 w-20 rounded-full bg-gray-200">
										<div
											className="h-2 rounded-full bg-green-600"
											style={{ width: "30%" }}
										/>
									</div>
									<span className="font-medium text-sm">30%</span>
								</div>
							</div>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<span className="text-lg">🇨🇦</span>
									<span className="font-medium">Canada</span>
								</div>
								<div className="flex items-center gap-2">
									<div className="h-2 w-20 rounded-full bg-gray-200">
										<div
											className="h-2 rounded-full bg-purple-600"
											style={{ width: "15%" }}
										/>
									</div>
									<span className="font-medium text-sm">15%</span>
								</div>
							</div>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<span className="text-lg">🇪🇸</span>
									<span className="font-medium">Spain</span>
								</div>
								<div className="flex items-center gap-2">
									<div className="h-2 w-20 rounded-full bg-gray-200">
										<div
											className="h-2 rounded-full bg-orange-600"
											style={{ width: "10%" }}
										/>
									</div>
									<span className="font-medium text-sm">10%</span>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Revenue vs Refunds Comparison */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<BarChart3 className="h-5 w-5" />
						Revenue vs Refunds Analysis
					</CardTitle>
					<CardDescription>
						Monthly comparison of revenue and refund amounts
					</CardDescription>
				</CardHeader>
				<CardContent>
					<ChartContainer
						config={{
							revenue: {
								label: "Revenue",
								color: "hsl(var(--chart-1))",
							},
							refunds: {
								label: "Refunds",
								color: "hsl(var(--chart-5))",
							},
						}}
						className="min-h-[250px] w-full"
					>
						<BarChart
							accessibilityLayer
							data={[
								{ month: "Jan", revenue: 2495, refunds: 150 },
								{ month: "Feb", revenue: 1998, refunds: 200 },
								{ month: "Mar", revenue: 3492, refunds: 100 },
								{ month: "Apr", revenue: 2890, refunds: 250 },
								{ month: "May", revenue: 4200, refunds: 180 },
								{ month: "Jun", revenue: 3800, refunds: 120 },
							]}
						>
							<CartesianGrid vertical={false} />
							<XAxis
								dataKey="month"
								tickLine={false}
								tickMargin={10}
								axisLine={false}
								tickFormatter={(value) => value.slice(0, 3)}
							/>
							<YAxis
								tickLine={false}
								axisLine={false}
								tickMargin={8}
								tickFormatter={(value) => formatCurrency(value)}
							/>
							<Bar
								dataKey="revenue"
								fill="var(--color-revenue)"
								radius={[4, 4, 0, 0]}
							/>
							<Bar
								dataKey="refunds"
								fill="var(--color-refunds)"
								radius={[4, 4, 0, 0]}
							/>
							<ChartTooltip
								content={
									<ChartTooltipContent
										formatter={(value, name) => [
											formatCurrency(Number(value)),
											name === "revenue" ? "Revenue" : "Refunds",
										]}
									/>
								}
							/>
							<ChartLegend content={<ChartLegendContent />} />
						</BarChart>
					</ChartContainer>
				</CardContent>
			</Card>

			{/* Performance Metrics */}
			<Card>
				<CardHeader>
					<CardTitle>Performance Metrics</CardTitle>
					<CardDescription>
						Detailed performance indicators and trends
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
						<div className="space-y-2">
							<p className="font-medium text-muted-foreground text-sm">
								Average Order Value
							</p>
							<p className="font-bold text-2xl">{formatCurrency(650)}</p>
							<p className="text-green-600 text-xs">+12.5% from last month</p>
						</div>
						<div className="space-y-2">
							<p className="font-medium text-muted-foreground text-sm">
								Customer Lifetime Value
							</p>
							<p className="font-bold text-2xl">{formatCurrency(1250)}</p>
							<p className="text-green-600 text-xs">+8.3% from last month</p>
						</div>
						<div className="space-y-2">
							<p className="font-medium text-muted-foreground text-sm">
								Churn Rate
							</p>
							<p className="font-bold text-2xl">2.1%</p>
							<p className="text-red-600 text-xs">+0.3% from last month</p>
						</div>
						<div className="space-y-2">
							<p className="font-medium text-muted-foreground text-sm">
								License Utilization
							</p>
							<p className="font-bold text-2xl">87%</p>
							<p className="text-green-600 text-xs">+5.2% from last month</p>
						</div>
						<div className="space-y-2">
							<p className="font-medium text-muted-foreground text-sm">
								Support Tickets
							</p>
							<p className="font-bold text-2xl">23</p>
							<p className="text-green-600 text-xs">-15% from last month</p>
						</div>
						<div className="space-y-2">
							<p className="font-medium text-muted-foreground text-sm">
								API Usage
							</p>
							<p className="font-bold text-2xl">45.2K</p>
							<p className="text-green-600 text-xs">+22% from last month</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
