"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON>, CheckCircle, Download, Mail } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import { CopyToClipboard } from "@/components/copy-to-clipboard";
import { Button } from "@/components/ui/button";

// Force dynamic rendering
export const dynamic = "force-dynamic";

interface CheckoutSession {
	sessionId: string;
	paymentStatus: string;
	customerEmail: string;
	amountTotal: number;
	currency: string;
	license: {
		licenseKey: string;
		licenseType: string;
		maxDevices: number;
		expiresAt: string | null;
		email: string;
	} | null;
	metadata: Record<string, string>;
}

function SuccessContent() {
	const searchParams = useSearchParams();
	const router = useRouter();
	const [session, setSession] = useState<CheckoutSession | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const sessionId = searchParams.get("session_id");

	useEffect(() => {
		if (!sessionId) {
			setError("No session ID provided");
			setLoading(false);
			return;
		}

		// Fetch checkout session details
		const fetchSession = async () => {
			try {
				const response = await fetch(
					`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000"}/api/payments/checkout-session/${sessionId}`,
				);

				if (!response.ok) {
					throw new Error("Failed to fetch session details");
				}

				const sessionData = await response.json();
				setSession(sessionData);
			} catch (err) {
				setError("Failed to load purchase details");
				console.error("Error fetching session:", err);
			} finally {
				setLoading(false);
			}
		};

		fetchSession();
	}, [sessionId]);

	if (loading) {
		return (
			<div className="flex min-h-screen items-center justify-center bg-gray-50">
				<div className="text-center">
					<div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent" />
					<p className="text-gray-600">Loading your purchase details...</p>
				</div>
			</div>
		);
	}

	if (error || !session) {
		return (
			<div className="flex min-h-screen items-center justify-center bg-gray-50">
				<div className="mx-auto max-w-md rounded-lg bg-white p-8 text-center shadow-lg">
					<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
						<CheckCircle className="h-6 w-6 text-red-600" />
					</div>
					<h1 className="mb-2 font-semibold text-gray-900 text-xl">
						Unable to Load Purchase Details
					</h1>
					<p className="mb-6 text-gray-600">
						{error ||
							"Something went wrong while loading your purchase information."}
					</p>
					<Button
						onClick={() => router.push("/")}
						className="bg-blue-600 text-white hover:bg-blue-700"
					>
						Return to Home
					</Button>
				</div>
			</div>
		);
	}

	const formatAmount = (amount: number, currency: string) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: currency.toUpperCase(),
		}).format(amount / 100);
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 px-6 py-24">
			<div className="mx-auto max-w-2xl">
				{/* Success Animation */}
				<motion.div
					initial={{ scale: 0 }}
					animate={{ scale: 1 }}
					transition={{ duration: 0.5, type: "spring" }}
					className="mb-8 text-center"
				>
					<div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
						<CheckCircle className="h-12 w-12 text-green-600" />
					</div>
					<h1 className="mb-4 font-bold text-4xl text-gray-900">
						Purchase Successful!
					</h1>
					<p className="text-gray-600 text-xl">
						Thank you for purchasing Snapback. Your license is ready!
					</p>
				</motion.div>

				{/* Purchase Details */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6, delay: 0.2 }}
					className="mb-8 rounded-2xl bg-white p-8 shadow-lg"
				>
					<h2 className="mb-6 font-semibold text-2xl text-gray-900">
						Purchase Details
					</h2>

					<div className="grid gap-4 md:grid-cols-2">
						<div>
							<p className="font-medium text-gray-500 text-sm">License Type</p>
							<p className="font-semibold text-gray-900 text-lg capitalize">
								{session.license?.licenseType || "Standard"}
							</p>
						</div>
						<div>
							<p className="font-medium text-gray-500 text-sm">Amount Paid</p>
							<p className="font-semibold text-gray-900 text-lg">
								{formatAmount(session.amountTotal, session.currency)}
							</p>
						</div>
						<div>
							<p className="font-medium text-gray-500 text-sm">Max Devices</p>
							<p className="font-semibold text-gray-900 text-lg">
								{session.license?.maxDevices || "2"} devices
							</p>
						</div>
						<div>
							<p className="font-medium text-gray-500 text-sm">Email</p>
							<p className="font-semibold text-gray-900 text-lg">
								{session.customerEmail}
							</p>
						</div>
					</div>

					{session.license && (
						<div className="mt-6 rounded-lg bg-gray-50 p-4">
							<div className="mb-2 flex items-center justify-between">
								<p className="font-medium text-gray-500 text-sm">License Key</p>
								<CopyToClipboard text={session.license.licenseKey} size="sm">
									Copy Key
								</CopyToClipboard>
							</div>
							{/* <CopyToClipboard
								text={session.license.licenseKey}
								variant="inline"
								className="w-full"
							> */}
							<p className="break-all font-mono font-semibold text-gray-900 text-lg">
								{session.license.licenseKey}
							</p>
							{/* </CopyToClipboard> */}
							<p className="mt-2 text-gray-500 text-xs">
								Click the copy icon or button to copy your license key
							</p>
						</div>
					)}
				</motion.div>

				{/* Next Steps */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6, delay: 0.4 }}
					className="mb-8 rounded-2xl bg-blue-50 p-8"
				>
					<h2 className="mb-4 font-semibold text-2xl text-gray-900">
						What's Next?
					</h2>
					<div className="space-y-4">
						<div className="flex items-start gap-3">
							<Mail className="mt-1 h-5 w-5 text-blue-600" />
							<div>
								<p className="font-medium text-gray-900">Check Your Email</p>
								<p className="text-gray-600">
									We've sent your license key and download instructions to{" "}
									{session.customerEmail}
								</p>
							</div>
						</div>
						<div className="flex items-start gap-3">
							<Download className="mt-1 h-5 w-5 text-blue-600" />
							<div>
								<p className="font-medium text-gray-900">Download Snapback</p>
								<p className="text-gray-600">
									Download the app and enter your license key to unlock all
									premium features
								</p>
							</div>
						</div>
					</div>
				</motion.div>

				{/* CTA Buttons */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6, delay: 0.6 }}
					className="flex flex-col gap-4 sm:flex-row sm:justify-center"
				>
					<Button
						size="lg"
						className="bg-blue-600 text-white hover:bg-blue-700"
						onClick={() => window.open("#", "_blank")} // Replace with actual download link
					>
						<Download className="mr-2 h-5 w-5" />
						Download Snapback
					</Button>
					<Button size="lg" variant="outline" onClick={() => router.push("/")}>
						Return to Home
						<ArrowRight className="ml-2 h-5 w-5" />
					</Button>
				</motion.div>
			</div>
		</div>
	);
}

export default function SuccessPage() {
	return (
		<Suspense
			fallback={
				<div className="flex min-h-screen items-center justify-center bg-gray-50">
					<div className="text-center">
						<div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent" />
						<p className="text-gray-600">Loading...</p>
					</div>
				</div>
			}
		>
			<SuccessContent />
		</Suspense>
	);
}
