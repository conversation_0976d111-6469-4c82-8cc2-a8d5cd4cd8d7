import { transporter } from "@/lib/mailer";

const sendLicenseEmail = async (
	email: string,
	licenseKey: string,
	licenseType: string,
	expiresAt: Date | null = null,
) => {
	try {
		console.log(`[EMAIL] Attempting to send license email to: ${email}`);
		console.log(
			`[EMAIL] SMTP Config - Host: ${process.env.SMTP_HOST}, Port: ${process.env.SMTP_PORT}`,
		);

		const expirationText = expiresAt
			? `This trial license expires on ${new Date(
					expiresAt,
				).toLocaleDateString()}. Download SnapBack and enter this license key to start your free trial!`
			: "This is a permanent license.";

		const isTrialLicense = expiresAt !== null;
		const subject = isTrialLicense
			? "Your SnapBack Free Trial License Key"
			: "Your SnapBack License Key";
		const greeting = isTrialLicense
			? "Thank you for trying SnapBack! Here's your free trial license:"
			: "Thank you for your purchase! Here's your license information:";

		const mailOptions = {
			from: process.env.FROM_EMAIL,
			to: email,
			subject: subject,
			html: `
	      <h2>${subject}</h2>
	      <p>${greeting}</p>

	      <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
	        <h3>License Key:</h3>
	        <code style="font-size: 18px; font-weight: bold; color: #2563eb;">${licenseKey}</code>
	      </div>

	      <p><strong>License Type:</strong> ${licenseType.toUpperCase()}</p>
	      <p><strong>Devices Allowed:</strong> ${
					licenseType === "pro" ? "2" : "1"
				}</p>
	      <p>${expirationText}</p>

	      <p>Keep this email safe - you'll need your license key to activate the app.</p>

	      ${
					isTrialLicense
						? `
	      <div style="background: #e0f2fe; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #0284c7;">
	        <h4 style="margin: 0 0 10px 0; color: #0284c7;">Getting Started with Your Trial:</h4>
	        <ol style="margin: 0; padding-left: 20px;">
	          <li>Download SnapBack from our website</li>
	          <li>Install the app on your device</li>
	          <li>Enter your license key when prompted</li>
	          <li>Start backing up your data!</li>
	        </ol>
	      </div>
	      `
						: ""
				}

	      <hr>
	      <p><small>If you lose this email, you can request your license key again using the same email address.</small></p>
	    `,
		};

		console.log("[EMAIL] Sending email with options:", {
			from: mailOptions.from,
			to: mailOptions.to,
			subject: mailOptions.subject,
		});

		const result = await transporter.sendMail(mailOptions);
		console.log(
			`[EMAIL] ✅ Email sent successfully to ${email}:`,
			result.messageId,
		);
		return result;
	} catch (error) {
		console.error(`[EMAIL] ❌ Failed to send email to ${email}:`, error);
		throw error;
	}
};

const sendRefundConfirmationEmail = async (
	email: string,
	licenseKey: string,
	refundAmount: number,
	refundReason: string,
	refundId: string,
) => {
	const formattedAmount = (refundAmount / 100).toFixed(2);

	const mailOptions = {
		from: process.env.FROM_EMAIL,
		to: email,
		subject: "Refund Confirmation - Your License Has Been Refunded",
		html: `
      <h2>Refund Confirmation</h2>
      <p>Your refund request has been processed successfully.</p>

      <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>Refund Details:</h3>
        <p><strong>License Key:</strong> <code style="font-size: 16px; color: #2563eb;">${licenseKey}</code></p>
        <p><strong>Refund Amount:</strong> $${formattedAmount} USD</p>
        <p><strong>Refund ID:</strong> ${refundId}</p>
        <p><strong>Reason:</strong> ${refundReason}</p>
      </div>

      <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
        <h4 style="margin-top: 0; color: #92400e;">Important Notice:</h4>
        <p style="margin-bottom: 0; color: #92400e;">
          Your license has been deactivated and can no longer be used to activate the application.
          All devices previously registered with this license will no longer have access.
        </p>
      </div>

      <p><strong>Refund Processing:</strong> The refund will appear on your original payment method within 5-10 business days.</p>

      <p>If you have any questions about this refund, please contact our support team.</p>

      <hr>
      <p><small>This is an automated confirmation email. Please keep this for your records.</small></p>
    `,
	};

	await transporter.sendMail(mailOptions);
};

const sendRefundRequestConfirmationEmail = async (
	email: string,
	licenseKey: string,
	reason: string,
	requestId: string,
) => {
	const mailOptions = {
		from: process.env.FROM_EMAIL,
		to: email,
		subject: "Refund Request Received - We're Processing Your Request",
		html: `
      <h2>Refund Request Received</h2>
      <p>We have received your refund request and are currently reviewing it.</p>

      <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>Request Details:</h3>
        <p><strong>License Key:</strong> <code style="font-size: 16px; color: #2563eb;">${licenseKey}</code></p>
        <p><strong>Request ID:</strong> ${requestId}</p>
        <p><strong>Reason:</strong> ${reason}</p>
        <p><strong>Status:</strong> Pending Review</p>
      </div>

      <p><strong>What happens next?</strong></p>
      <ul>
        <li>Our team will review your request within 1-2 business days</li>
        <li>You will receive an email notification once the request is processed</li>
        <li>If approved, the refund will be processed to your original payment method</li>
      </ul>

      <p>You can check the status of your refund request at any time by contacting our support team with your request ID.</p>

      <hr>
      <p><small>This is an automated confirmation email. Please keep this for your records.</small></p>
    `,
	};

	await transporter.sendMail(mailOptions);
};

const sendRefundRejectionEmail = async (
	email: string,
	licenseKey: string,
	reason: string,
	adminNotes?: string,
) => {
	const mailOptions = {
		from: process.env.FROM_EMAIL,
		to: email,
		subject: "Refund Request Update - Request Not Approved",
		html: `
      <h2>Refund Request Update</h2>
      <p>We have reviewed your refund request and unfortunately cannot approve it at this time.</p>

      <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>Request Details:</h3>
        <p><strong>License Key:</strong> <code style="font-size: 16px; color: #2563eb;">${licenseKey}</code></p>
        <p><strong>Your Reason:</strong> ${reason}</p>
        <p><strong>Status:</strong> Not Approved</p>
        ${adminNotes ? `<p><strong>Additional Information:</strong> ${adminNotes}</p>` : ""}
      </div>

      <p>If you believe this decision was made in error or if you have additional information that might affect the outcome, please contact our support team.</p>

      <p>Your license remains active and can continue to be used normally.</p>

      <hr>
      <p><small>If you have questions about this decision, please contact our support team.</small></p>
    `,
	};

	await transporter.sendMail(mailOptions);
};

const sendDeviceExpansionEmailTemplate = async (
	email: string,
	licenseKey: string,
	additionalDevices: number,
	newMaxDevices: number,
) => {
	try {
		const mailOptions = {
			from: process.env.FROM_EMAIL,
			to: email,
			subject: "SnapBack License Expanded - More Devices Added!",
			html: `
	      <h2>Your SnapBack License Has Been Expanded!</h2>
	      <p>Great news! Your license capacity has been successfully increased.</p>

	      <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
	        <h3>License Information:</h3>
	        <p><strong>License Key:</strong> <code style="font-size: 18px; font-weight: bold; color: #2563eb;">${licenseKey}</code></p>
	        <p><strong>Devices Added:</strong> +${additionalDevices} devices</p>
	        <p><strong>Total Device Capacity:</strong> ${newMaxDevices} devices</p>
	      </div>

	      <div style="background: #e0f2fe; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #0284c7;">
	        <h4 style="margin: 0 0 10px 0; color: #0284c7;">What's Next?</h4>
	        <ul style="margin: 0; padding-left: 20px;">
	          <li>You can now install SnapBack on ${additionalDevices} additional device${additionalDevices > 1 ? "s" : ""}</li>
	          <li>Use the same license key on all your devices</li>
	          <li>Your existing devices will continue working normally</li>
	          <li>All premium features remain available across all devices</li>
	        </ul>
	      </div>

	      <p>Your license key remains the same - simply enter it on your new devices to activate them.</p>

	      <hr>
	      <p><small>Keep this email safe for your records. If you need help, contact our support team.</small></p>
	    `,
		};

		console.log("[EMAIL] 📧 Sending email via SMTP transporter...");
		const result = await transporter.sendMail(mailOptions);
		console.log("=".repeat(60));
		console.log("[EMAIL] ✅ EMAIL SENT SUCCESSFULLY!");
		console.log(`[EMAIL] ✅ Message ID: ${result.messageId}`);
		console.log(`[EMAIL] ✅ Recipient: ${email}`);
		console.log("[EMAIL] ✅ Device expansion notification delivered");
		console.log("=".repeat(60));
		return result;
	} catch (error) {
		console.log("=".repeat(60));
		console.log("[EMAIL] ❌ EMAIL SEND FAILED!");
		console.log(`[EMAIL] ❌ Recipient: ${email}`);
		console.log(
			`[EMAIL] ❌ Error: ${error instanceof Error ? error.message : String(error)}`,
		);
		if (error instanceof Error && error.stack) {
			console.log(`[EMAIL] ❌ Stack: ${error.stack}`);
		}
		console.log("=".repeat(60));
		throw error;
	}
};

const sendTierUpgradeEmailTemplate = async (
	email: string,
	licenseKey: string,
	fromTier: string,
	toTier: string,
	newMaxDevices: number,
) => {
	try {
		console.log("=".repeat(60));
		console.log("[EMAIL] 🚀 TIER UPGRADE EMAIL TEMPLATE CALLED");
		console.log(`[EMAIL] 📧 Recipient: ${email}`);
		console.log(`[EMAIL] 🔑 License: ${licenseKey.substring(0, 8)}...`);
		console.log(`[EMAIL] ⬆️ Upgrade: ${fromTier} → ${toTier}`);
		console.log(`[EMAIL] 📊 New Max Devices: ${newMaxDevices}`);
		console.log(`[EMAIL] 🌐 SMTP Host: ${process.env.SMTP_HOST}`);
		console.log("=".repeat(60));

		const mailOptions = {
			from: process.env.FROM_EMAIL,
			to: email,
			subject: "SnapBack License Upgraded - More Features Unlocked!",
			html: `
	      <h2>Your SnapBack License Has Been Upgraded!</h2>
	      <p>Great news! Your license has been successfully upgraded with enhanced features and capacity.</p>

	      <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
	        <h3>Upgrade Details:</h3>
	        <p><strong>License Key:</strong> <code style="font-size: 18px; font-weight: bold; color: #2563eb;">${licenseKey}</code></p>
	        <p><strong>Upgraded From:</strong> ${fromTier}</p>
	        <p><strong>Upgraded To:</strong> ${toTier}</p>
	        <p><strong>Total Device Capacity:</strong> ${newMaxDevices} devices</p>
	      </div>

	      <div style="background: #e0f2fe; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #0284c7;">
	        <h4 style="margin: 0 0 10px 0; color: #0284c7;">What's New with Your ${toTier} License?</h4>
	        <ul style="margin: 0; padding-left: 20px;">
	          <li>Increased device capacity: Now supports ${newMaxDevices} devices</li>
	          <li>All premium features remain available</li>
	          <li>Enhanced support and priority assistance</li>
	          <li>Your existing devices continue working seamlessly</li>
	        </ul>
	      </div>

	      <p>Your license key remains the same - no need to re-enter it on your existing devices. You can now install SnapBack on additional devices up to your new limit.</p>

	      <p>Thank you for choosing SnapBack ${toTier}!</p>

	      <hr>
	      <p><small>Keep this email safe for your records. If you have any questions, please don't hesitate to contact our support team.</small></p>
	    `,
		};

		const result = await transporter.sendMail(mailOptions);

		return result;
	} catch (error) {
		console.log("=".repeat(60));
		console.log("[EMAIL] ❌ TIER UPGRADE EMAIL SEND FAILED!");
		console.log(`[EMAIL] ❌ Recipient: ${email}`);
		console.log(`[EMAIL] ❌ Upgrade: ${fromTier} → ${toTier}`);
		console.log(
			`[EMAIL] ❌ Error: ${error instanceof Error ? error.message : String(error)}`,
		);
		if (error instanceof Error && error.stack) {
			console.log(`[EMAIL] ❌ Stack: ${error.stack}`);
		}
		console.log("=".repeat(60));
		throw error;
	}
};

export {
	sendLicenseEmail,
	sendRefundConfirmationEmail,
	sendRefundRequestConfirmationEmail,
	sendRefundRejectionEmail,
	sendDeviceExpansionEmailTemplate as sendDeviceExpansionEmail,
	sendTierUpgradeEmailTemplate as sendTierUpgradeEmail,
};
