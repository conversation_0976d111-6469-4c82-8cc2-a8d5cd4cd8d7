import { type ErrorC<PERSON>, ErrorCodes } from "@snapback/shared/types";

// Re-export ErrorCode for backward compatibility
export type { ErrorCode };

import type { NextFunction, Request, Response } from "express";
import { z } from "zod";
import { EndpointPrefix, Logger } from "./logger";

/**
 * Prisma error interface for database operations
 */
export interface PrismaError extends Error {
	code?: string;
	meta?: {
		target?: string[];
		field_name?: string;
	};
}

/**
 * Stripe error interface for payment operations
 */
export interface StripeError extends Error {
	type?: string;
	code?: string;
	decline_code?: string;
	param?: string;
	payment_intent?: {
		id: string;
		status: string;
	};
}

/**
 * Generic error with optional code property
 */
interface ErrorWithCode extends Error {
	code?: string;
	statusCode?: number;
}

/**
 * Standard error response interface
 */
export interface ErrorResponse {
	error: string;
	message?: string;
	details?: Record<string, unknown>;
	code?: string;
	timestamp: string;
	path?: string;
}

/**
 * Error codes are now imported from @snapback/shared/types for consistency
 * across the entire monorepo. This ensures client and server use identical error codes.
 */

/**
 * Unified error handling utility that automatically detects error types
 * and provides consistent responses with proper logging.
 *
 * @param res - Express Response object
 * @param error - The error to handle (any type)
 * @param context - Optional context for logging (EndpointPrefix or string)
 */
export function handleError(
	res: Response,
	error: unknown,
	context?: EndpointPrefix | string,
): void {
	// Log the error with context
	const logContext =
		(context as EndpointPrefix) || EndpointPrefix.SYSTEM_HEALTH;

	// Handle Zod validation errors
	if (error instanceof z.ZodError) {
		Logger.error(logContext, "Validation error occurred", {
			body: { validationErrors: error.issues },
		});
		handleZodError(res, error);
		return;
	}

	// Handle Prisma database errors
	if (isPrismaError(error)) {
		Logger.error(
			logContext,
			`Database error: ${error.code} - ${error.message}`,
			{ body: { prismaCode: error.code, meta: error.meta } },
		);
		handleDatabaseError(res, error);
		return;
	}

	// Handle Stripe payment errors
	if (isStripeError(error)) {
		Logger.error(logContext, `Stripe error: ${error.type} - ${error.message}`, {
			body: { stripeType: error.type, stripeCode: error.code },
		});
		handleStripeError(res, error);
		return;
	}

	// Handle custom application errors with status codes
	if (isErrorWithCode(error)) {
		const statusCode = error.statusCode || 500;
		const errorCode = (error.code as ErrorCode) || ErrorCodes.INTERNAL_ERROR;

		Logger.error(logContext, `Application error: ${error.message}`, {
			body: { statusCode, errorCode, stack: error.stack },
		});

		sendErrorResponse(
			res,
			error.message,
			statusCode,
			errorCode,
			"An application error occurred",
		);
		return;
	}

	// Handle generic JavaScript errors
	if (error instanceof Error) {
		Logger.error(logContext, `Unexpected error: ${error.message}`, {
			body: { stack: error.stack },
		});

		sendErrorResponse(
			res,
			"Internal server error",
			500,
			ErrorCodes.INTERNAL_ERROR,
			"An unexpected error occurred",
		);
		return;
	}

	// Handle unknown error types
	Logger.error(logContext, `Unknown error type: ${typeof error}`, {
		body: { error: String(error) },
	});

	sendErrorResponse(
		res,
		"Internal server error",
		500,
		ErrorCodes.INTERNAL_ERROR,
		"An unexpected error occurred",
	);
}

/**
 * Type guard to check if error is a Prisma error
 */
function isPrismaError(error: unknown): error is PrismaError {
	return (
		typeof error === "object" &&
		error !== null &&
		"code" in error &&
		typeof (error as any).code === "string" &&
		(error as any).code.startsWith("P")
	);
}

/**
 * Type guard to check if error is a Stripe error
 */
function isStripeError(error: unknown): error is StripeError {
	return (
		typeof error === "object" &&
		error !== null &&
		"type" in error &&
		typeof (error as any).type === "string" &&
		((error as any).type.includes("Stripe") ||
			(error as any).type.includes("card"))
	);
}

/**
 * Type guard to check if error has custom status code
 */
function isErrorWithCode(error: unknown): error is ErrorWithCode {
	return error instanceof Error && ("statusCode" in error || "code" in error);
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
	error: string,
	statusCode = 500,
	code?: ErrorCode,
	message?: string,
	details?: Record<string, unknown>,
	path?: string,
): { statusCode: number; response: ErrorResponse } {
	return {
		statusCode,
		response: {
			error,
			message,
			details,
			code,
			timestamp: new Date().toISOString(),
			path,
		},
	};
}

/**
 * Send a standardized error response
 */
export function sendErrorResponse(
	res: Response,
	error: string,
	statusCode = 500,
	code?: ErrorCode,
	message?: string,
	details?: Record<string, unknown>,
): void {
	const path = res.req?.originalUrl || res.req?.url;
	const { response } = createErrorResponse(
		error,
		statusCode,
		code,
		message,
		details,
		path,
	);

	// Log error for debugging (exclude sensitive information)
	if (statusCode >= 500) {
		console.error(`[${response.timestamp}] ${statusCode} ${error}`, {
			path,
			code,
			message,
			// Don't log sensitive details in production
			details: process.env.NODE_ENV === "development" ? details : undefined,
		});
	}

	res.status(statusCode).json(response);
}

/**
 * Handle Zod validation errors
 */
export function handleZodError(res: Response, error: z.ZodError): void {
	const validationErrors = error.issues.map((issue) => ({
		field: issue.path.join("."),
		message: issue.message,
		code: issue.code,
	}));

	sendErrorResponse(
		res,
		"Validation failed",
		400,
		ErrorCodes.VALIDATION_ERROR,
		"The provided data is invalid",
		{ validationErrors },
	);
}

/**
 * Handle database errors
 */
export function handleDatabaseError(res: Response, error: PrismaError): void {
	console.error("Database error:", error);

	// Check for common Prisma errors
	if (error.code === "P2002") {
		// Unique constraint violation
		sendErrorResponse(
			res,
			"Resource already exists",
			409,
			ErrorCodes.ALREADY_EXISTS,
			"A resource with this information already exists",
		);
		return;
	}

	if (error.code === "P2025") {
		// Record not found
		sendErrorResponse(
			res,
			"Resource not found",
			404,
			ErrorCodes.NOT_FOUND,
			"The requested resource was not found",
		);
		return;
	}

	// Generic database error
	sendErrorResponse(
		res,
		"Database operation failed",
		500,
		ErrorCodes.DATABASE_ERROR,
		"An error occurred while accessing the database",
	);
}

/**
 * Handle Stripe payment errors
 */
export function handleStripeError(res: Response, error: StripeError): void {
	console.error("Stripe error:", error);

	if (error.type === "StripeCardError") {
		sendErrorResponse(
			res,
			"Payment failed",
			400,
			ErrorCodes.PAYMENT_FAILED,
			error.message || "Your card was declined",
		);
		return;
	}

	if (error.type === "StripeInvalidRequestError") {
		sendErrorResponse(
			res,
			"Invalid payment request",
			400,
			ErrorCodes.INVALID_PAYMENT,
			"The payment request is invalid",
		);
		return;
	}

	// Generic Stripe error
	sendErrorResponse(
		res,
		"Payment processing error",
		500,
		ErrorCodes.EXTERNAL_SERVICE_ERROR,
		"An error occurred while processing your payment",
	);
}

/**
 * License-specific error responses
 */
export const LicenseErrors = {
	notFound: (res: Response): void =>
		sendErrorResponse(
			res,
			"License not found",
			404,
			ErrorCodes.LICENSE_NOT_FOUND,
			"The specified license key was not found",
		),

	expired: (res: Response): void =>
		sendErrorResponse(
			res,
			"License expired",
			403,
			ErrorCodes.LICENSE_EXPIRED,
			"This license has expired and is no longer valid",
		),

	maxDevicesReached: (
		res: Response,
		maxDevices: number,
		currentDevices: number,
	): void =>
		sendErrorResponse(
			res,
			"Maximum devices reached",
			403,
			ErrorCodes.MAX_DEVICES_REACHED,
			`This license supports a maximum of ${maxDevices} devices`,
			{ maxDevices, currentDevices },
		),

	alreadyExists: (res: Response, licenseType: string): void =>
		sendErrorResponse(
			res,
			"License already exists",
			409,
			ErrorCodes.ALREADY_EXISTS,
			"An active license already exists for this email address",
			{ existingLicenseType: licenseType },
		),

	refunded: (res: Response, refundedAt: Date): void =>
		sendErrorResponse(
			res,
			"License refunded",
			403,
			ErrorCodes.LICENSE_EXPIRED,
			"This license has been refunded and is no longer valid",
			{ refundedAt: refundedAt.toISOString() },
		),
} as const;

/**
 * Payment-specific error responses
 */
export const PaymentErrors = {
	incomplete: (res: Response, paymentStatus: string): void =>
		sendErrorResponse(
			res,
			"Payment not completed",
			400,
			ErrorCodes.PAYMENT_INCOMPLETE,
			"The payment has not been completed successfully",
			{ paymentStatus },
		),

	failed: (res: Response, reason?: string): void =>
		sendErrorResponse(
			res,
			"Payment failed",
			400,
			ErrorCodes.PAYMENT_FAILED,
			reason || "The payment could not be processed",
		),
} as const;

/**
 * User management error responses
 */
export const UserErrors = {
	notFound: (res: Response): void =>
		sendErrorResponse(
			res,
			"User not found",
			404,
			ErrorCodes.USER_NOT_FOUND,
			"The specified user was not found",
		),

	alreadyExists: (res: Response, email: string): void =>
		sendErrorResponse(
			res,
			"User already exists",
			409,
			ErrorCodes.USER_ALREADY_EXISTS,
			"A user with this email address already exists",
			{ email },
		),

	insufficientPermissions: (res: Response, action?: string): void =>
		sendErrorResponse(
			res,
			"Insufficient permissions",
			403,
			ErrorCodes.INSUFFICIENT_PERMISSIONS,
			action
				? `You don't have permission to ${action}`
				: "You don't have permission to perform this action",
		),

	invalidRoleAssignment: (res: Response, role: string): void =>
		sendErrorResponse(
			res,
			"Invalid role assignment",
			403,
			ErrorCodes.INVALID_ROLE_ASSIGNMENT,
			`You cannot assign the role: ${role}`,
			{ attemptedRole: role },
		),

	cannotModifySelf: (res: Response, action: string): void =>
		sendErrorResponse(
			res,
			"Cannot modify own account",
			400,
			ErrorCodes.CANNOT_MODIFY_SELF,
			`You cannot ${action} your own account`,
		),

	accountDeactivated: (res: Response): void =>
		sendErrorResponse(
			res,
			"Account deactivated",
			403,
			ErrorCodes.ACCOUNT_DEACTIVATED,
			"Your account has been deactivated. Please contact an administrator.",
		),

	invitationNotFound: (res: Response): void =>
		sendErrorResponse(
			res,
			"Invitation not found",
			404,
			ErrorCodes.INVITATION_NOT_FOUND,
			"The specified invitation was not found or has expired",
		),

	invitationAlreadyExists: (res: Response, email: string): void =>
		sendErrorResponse(
			res,
			"Invitation already exists",
			409,
			ErrorCodes.INVITATION_ALREADY_EXISTS,
			"An invitation has already been sent to this email address",
			{ email },
		),

	invitationExpired: (res: Response): void =>
		sendErrorResponse(
			res,
			"Invitation expired",
			400,
			ErrorCodes.INVITATION_EXPIRED,
			"This invitation has expired. Please request a new invitation.",
		),
} as const;

/**
 * Generic error handler middleware
 */
export function errorHandler(
	error: ErrorWithCode | z.ZodError | PrismaError | StripeError | Error,
	_req: Request,
	res: Response,
	_next: NextFunction,
): void {
	// Handle Zod validation errors
	if (error instanceof z.ZodError) {
		handleZodError(res, error);
		return;
	}

	// Handle database errors
	if (
		"code" in error &&
		typeof error.code === "string" &&
		error.code.startsWith("P")
	) {
		handleDatabaseError(res, error as PrismaError);
		return;
	}

	// Handle Stripe errors
	if (
		"type" in error &&
		typeof error.type === "string" &&
		error.type.startsWith("Stripe")
	) {
		handleStripeError(res, error as StripeError);
		return;
	}

	// Generic error
	sendErrorResponse(
		res,
		"Internal server error",
		500,
		ErrorCodes.INTERNAL_ERROR,
		"An unexpected error occurred",
	);
}
