import type { PaginationMeta } from "@snapback/shared";

/**
 * Pagination configuration constants
 */
export const PAGINATION_DEFAULTS = {
	DEFAULT_PAGE: 1,
	DEFAULT_LIMIT: 20,
	MAX_LIMIT: 100,
	MIN_LIMIT: 1,
} as const;

/**
 * Pagination parameters interface
 */
export interface PaginationParams {
	page: number;
	limit: number;
}

/**
 * Pagination calculation result
 */
export interface PaginationCalculation {
	skip: number;
	take: number;
	meta: (totalCount: number) => PaginationMeta;
}

/**
 * Validates and normalizes pagination parameters
 */
export function validatePaginationParams(
	page?: number | string,
	limit?: number | string,
): PaginationParams {
	const normalizedPage = Math.max(
		PAGINATION_DEFAULTS.MIN_LIMIT,
		Number.parseInt(String(page)) || PAGINATION_DEFAULTS.DEFAULT_PAGE,
	);

	const normalizedLimit = Math.min(
		PAGINATION_DEFAULTS.MAX_LIMIT,
		Math.max(
			PAGINATION_DEFAULTS.MIN_LIMIT,
			Number.parseInt(String(limit)) || PAGINATION_DEFAULTS.DEFAULT_LIMIT,
		),
	);

	return {
		page: normalizedPage,
		limit: normalizedLimit,
	};
}

/**
 * Calculates pagination values for database queries
 */
export function calculatePagination(
	params: PaginationParams,
): PaginationCalculation {
	const { page, limit } = params;
	const skip = (page - 1) * limit;

	return {
		skip,
		take: limit,
		meta: (totalCount: number): PaginationMeta => {
			const totalPages = Math.ceil(totalCount / limit);

			return {
				page,
				limit,
				totalCount,
				totalPages,
				hasNextPage: page < totalPages,
				hasPreviousPage: page > 1,
			};
		},
	};
}

/**
 * Creates a standardized paginated response
 */
export function createPaginatedResponse<T>(
	data: T[],
	totalCount: number,
	params: PaginationParams,
): { data: T[]; pagination: PaginationMeta } {
	const pagination = calculatePagination(params);

	return {
		data,
		pagination: pagination.meta(totalCount),
	};
}

/**
 * Extracts pagination parameters from Express request query
 */
export function extractPaginationFromQuery(query: {
	page?: string;
	limit?: string;
}): PaginationParams {
	return validatePaginationParams(query.page, query.limit);
}

// ============================================================================
// NEW ENHANCED PAGINATION UTILITIES (Matching Current Architecture)
// ============================================================================

/**
 * USAGE PATTERN:
 *
 * 1. Route Layer:
 *    const { page, limit, search, sortBy, sortOrder, filters } = adminPaginationSchema.parse(req.query);
 *    const params = { page, limit, search, sortBy, sortOrder, filters };
 *    const { items, total } = await controllerFunction(params);
 *    const pagination = paginate(total, page, limit);
 *
 * 2. Controller Layer:
 *    export async function getItems(params: StandardPaginationParams) {
 *      const { skip, take, where, orderBy } = prepareDatabaseQuery(
 *        params,
 *        ['name', 'email'], // searchFields
 *        { status: 'ACTIVE' } // additionalFilters
 *      );
 *      const [items, total] = await Promise.all([
 *        prisma.model.findMany({ where, skip, take, orderBy }),
 *        prisma.model.count({ where })
 *      ]);
 *      return { items, total };
 *    }
 */

/**
 * Standard pagination parameters interface matching adminPaginationSchema
 */
export interface StandardPaginationParams {
	page: number;
	limit: number;
	search?: string;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
	filters?: Record<string, any>;
}

/**
 * Database query helpers result
 */
export interface DatabaseQueryHelpers {
	skip: number;
	take: number;
	where: Record<string, any>;
	orderBy: Record<string, "asc" | "desc">;
}

/**
 * Calculates skip value for database queries
 * Eliminates manual (page - 1) * limit calculations across controllers
 */
export function calculateSkip(page: number, limit: number): number {
	return (page - 1) * limit;
}

/**
 * Builds a standardized WHERE clause for search functionality
 * Supports searching across multiple fields with case-insensitive matching
 */
export function buildSearchWhere(
	search: string | undefined,
	searchFields: string[],
	additionalFilters?: Record<string, any>,
): Record<string, any> {
	const where: Record<string, any> = { ...additionalFilters };

	if (search && searchFields.length > 0) {
		where.OR = searchFields.map((field) => ({
			[field]: { contains: search, mode: "insensitive" },
		}));
	}

	return where;
}

/**
 * Builds a standardized ORDER BY clause
 * Provides consistent sorting with fallback to createdAt desc
 */
export function buildOrderBy(
	sortBy?: string,
	sortOrder: "asc" | "desc" = "desc",
	fallbackField = "createdAt",
): Record<string, "asc" | "desc"> {
	const orderBy: Record<string, "asc" | "desc"> = {};

	if (sortBy) {
		orderBy[sortBy] = sortOrder;
	} else {
		orderBy[fallbackField] = "desc";
	}

	return orderBy;
}

/**
 * Comprehensive helper that prepares all database query parameters
 * Eliminates repetitive code across all controller functions
 */
export function prepareDatabaseQuery(
	params: StandardPaginationParams,
	searchFields: string[] = [],
	additionalFilters?: Record<string, any>,
): DatabaseQueryHelpers {
	const { page, limit, search, sortBy, sortOrder, filters } = params;

	// Merge filters with additional filters
	const allFilters = { ...additionalFilters, ...filters };

	return {
		skip: calculateSkip(page, limit),
		take: limit,
		where: buildSearchWhere(search, searchFields, allFilters),
		orderBy: buildOrderBy(sortBy, sortOrder),
	};
}

/**
 * Simple paginate function for backward compatibility
 * Used in response formatting - DO NOT REMOVE
 */
export function paginate(
	total: number,
	page: number,
	limit: number,
): PaginationMeta {
	const totalPages = Math.ceil(total / limit);

	return {
		page,
		limit,
		totalCount: total,
		totalPages,
		hasNextPage: page < totalPages,
		hasPreviousPage: page > 1,
	};
}
