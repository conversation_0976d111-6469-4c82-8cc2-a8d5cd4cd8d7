/**
 * Structured logging utility for the Snapback License Server
 * Provides consistent, filterable logging across all API endpoints
 * Enhanced with color coding for development environments
 */

import chalk from "chalk";
import type { NextFunction, Request, Response } from "express";

// Environment detection for color support
const isDevelopment =
	process.env.NODE_ENV === "development" || process.env.NODE_ENV === "test";
const supportsColor = process.stdout.isTTY && isDevelopment;

// Log levels
export enum LogLevel {
	ERROR = "ERROR",
	WARN = "WARN",
	INFO = "INFO",
	DEBUG = "DEBUG",
}

// Color configuration for log levels
const LOG_LEVEL_COLORS = {
	[LogLevel.ERROR]: chalk.red.bold,
	[LogLevel.WARN]: chalk.yellow.bold,
	[LogLevel.INFO]: chalk.blue.bold,
	[LogLevel.DEBUG]: chalk.gray.bold,
} as const;

// Endpoint prefixes for easy filtering in Xcode
export enum EndpointPrefix {
	LICENSE_CREATE = "LICENSE_CREATE",
	LICENSE_CREATE_TRIAL = "LICENSE_CREATE_TRIAL",
	LICENSE_VALIDATE = "LICENSE_VALIDATE",
	LICENSE_STATUS = "LICENSE_STATUS",
	LICENSE_LIST = "LICENSE_LIST",
	LICENSE_RESEND = "LICENSE_RESEND",
	LICENSE_UPGRADE = "LICENSE_UPGRADE",
	LICENSE_REMOVE_DEVICE = "LICENSE_REMOVE_DEVICE",
	LICENSE_UPDATE_DEVICE = "LICENSE_UPDATE_DEVICE",
	LICENSE_UPDATE = "LICENSE_UPDATE",
	LICENSE_DELETE = "LICENSE_DELETE",
	LICENSE_EXTEND = "LICENSE_EXTEND",
	LICENSE_SUSPEND = "LICENSE_SUSPEND",
	LICENSE_REACTIVATE = "LICENSE_REACTIVATE",
	DEVICE_LIST = "DEVICE_LIST",
	DEVICE_DETAILS = "DEVICE_DETAILS",
	DEVICE_REMOVE = "DEVICE_REMOVE",
	DEVICE_UPDATE = "DEVICE_UPDATE",
	DEVICE_EXPANSION = "DEVICE_EXPANSION",
	DEVICE_EXPANSION_LIST = "DEVICE_EXPANSION_LIST",
	DEVICE_EXPANSION_PROCESS = "DEVICE_EXPANSION_PROCESS",
	AUTH_INVITATION = "AUTH_INVITATION",
	USER_REACTIVATE = "USER_REACTIVATE",
	ANALYTICS_DASHBOARD = "ANALYTICS_DASHBOARD",
	ANALYTICS_REVENUE = "ANALYTICS_REVENUE",
	ANALYTICS_LICENSES = "ANALYTICS_LICENSES",
	ANALYTICS_DEVICES = "ANALYTICS_DEVICES",
	REPORTS_EXPORT = "REPORTS_EXPORT",
	EMAIL_TEMPLATES = "EMAIL_TEMPLATES",
	EMAIL_BULK = "EMAIL_BULK",
	EMAIL_STATS = "EMAIL_STATS",
	NOTIFICATIONS = "NOTIFICATIONS",
	NOTIFICATION_SETTINGS = "NOTIFICATION_SETTINGS",
	WEBHOOK_MANAGEMENT = "WEBHOOK_MANAGEMENT",

	// Support System
	SUPPORT_TICKET_CREATE = "SUPPORT_TICKET_CREATE",
	SUPPORT_FAQ = "SUPPORT_FAQ",

	PAYMENT_CREATE_INTENT = "PAYMENT_CREATE_INTENT",
	PAYMENT_CREATE_CHECKOUT = "PAYMENT_CREATE_CHECKOUT",
	PAYMENT_WEBHOOK = "PAYMENT_WEBHOOK",
	PAYMENT_STATUS = "PAYMENT_STATUS",
	PAYMENT_PRICING = "PAYMENT_PRICING",
	REFUND_REQUEST = "REFUND_REQUEST",
	REFUND_STATUS = "REFUND_STATUS",
	REFUND_LIST = "REFUND_LIST",
	REFUND_APPROVE = "REFUND_APPROVE",
	REFUND_REJECT = "REFUND_REJECT",
	REFUND_PROCESS = "REFUND_PROCESS",
	REFUND_UPDATE = "REFUND_UPDATE",
	USER_CREATE = "USER_CREATE",
	USER_UPDATE = "USER_UPDATE",
	USER_LIST = "USER_LIST",
	USER_DELETE = "USER_DELETE",
	USER_INVITE = "USER_INVITE",
	SYSTEM_HEALTH = "SYSTEM_HEALTH",
	SYSTEM_STATUS = "SYSTEM_STATUS",
	SYSTEM_METRICS = "SYSTEM_METRICS",
	EMAIL_VALIDATION = "EMAIL_VALIDATION",
	STRIPE_STATUS = "STRIPE_STATUS",
	USER_ACCEPT_INVITE = "USER_ACCEPT_INVITE",
	USER_REVOKE_INVITE = "USER_REVOKE_INVITE",
	USER_RESEND_INVITE = "USER_RESEND_INVITE",
	USER_LIST_INVITATIONS = "USER_LIST_INVITATIONS",
	USER_ROLE_CHANGE = "USER_ROLE_CHANGE",
	AUTH = "AUTH",
	GENERAL = "GENERAL",
}

// Helper function to apply colors conditionally
const applyColor = (
	text: string,
	colorFn: (text: string) => string,
): string => {
	return supportsColor ? colorFn(text) : text;
};

// Advanced type utilities for logging
type SanitizableValue = string | number | boolean | null | undefined;
type LoggableObject = Record<
	string,
	SanitizableValue | SanitizableValue[] | Record<string, unknown>
>;

// Express request body types (more specific than 'any')
type RequestBody =
	| Record<string, unknown>
	| unknown[]
	| string
	| Buffer
	| undefined;

// Express request params (route parameters like :id)
type RequestParams = Record<string, string>;

// Express request query (query string parameters) - using Request['query'] type
type RequestQuery = Request["query"];

// Enhanced LogContext interface with proper typing
export interface LogContext {
	method: string;
	url: string;
	ip: string;
	userAgent?: string;
	contentType?: string;
	authorization?: string;
	body?: RequestBody;
	params?: RequestParams;
	query?: RequestQuery;
	responseTime?: number;
	statusCode?: number;
}

/**
 * Sanitize sensitive data from logs
 */
function sanitizeData<T>(data: T): T extends object ? LoggableObject : T {
	if (!data || typeof data !== "object") {
		return data as T extends object ? LoggableObject : T;
	}

	const sanitized = { ...data } as Record<string, unknown>;

	// Redact sensitive fields
	const sensitiveFields = [
		"password",
		"token",
		"secret",
		"key",
		"authorization",
		"stripePaymentIntentId",
		"deviceToken",
		"jwt",
	] as const;

	for (const field of sensitiveFields) {
		if (sanitized[field]) {
			if (typeof sanitized[field] === "string") {
				// Show first 4 and last 4 characters for identification
				const value = sanitized[field] as string;
				if (value.length > 8) {
					sanitized[field] =
						`${value.substring(0, 4)}...${value.substring(value.length - 4)}`;
				} else {
					sanitized[field] = "[REDACTED]";
				}
			} else {
				sanitized[field] = "[REDACTED]";
			}
		}
	}

	// Recursively sanitize nested objects
	for (const key in sanitized) {
		if (typeof sanitized[key] === "object" && sanitized[key] !== null) {
			sanitized[key] = sanitizeData(sanitized[key]);
		}
	}

	return sanitized as T extends object ? LoggableObject : T;
}

/**
 * Format log message with consistent structure and color coding
 */
function formatLogMessage(
	level: LogLevel,
	prefix: EndpointPrefix,
	message: string,
	context?: Partial<LogContext>,
): string {
	const timestamp = new Date().toISOString();

	// Apply colors conditionally based on environment (log levels only)
	const coloredLevel = applyColor(`[${level}]`, LOG_LEVEL_COLORS[level]);
	const coloredTimestamp = applyColor(`[${timestamp}]`, chalk.gray);

	const baseMessage = `${coloredTimestamp} ${coloredLevel} [${prefix}] ${message}`;

	if (!context) {
		return baseMessage;
	}

	const parts = [baseMessage];

	if (context.method && context.url) {
		parts.push(`${context.method} ${context.url}`);
	}

	if (context.ip) {
		parts.push(`(${context.ip})`);
	}

	const details: string[] = [];

	if (context.body && Object.keys(context.body).length > 0) {
		details.push(`Body: ${JSON.stringify(sanitizeData(context.body))}`);
	}

	if (context.params && Object.keys(context.params).length > 0) {
		details.push(`Params: ${JSON.stringify(context.params)}`);
	}

	if (context.query && Object.keys(context.query).length > 0) {
		details.push(`Query: ${JSON.stringify(context.query)}`);
	}

	if (context.userAgent) {
		details.push(`UA: ${context.userAgent}`);
	}

	if (context.authorization) {
		details.push(
			`Auth: ${sanitizeData({ authorization: context.authorization }).authorization}`,
		);
	}

	if (context.responseTime !== undefined) {
		details.push(`${context.responseTime}ms`);
	}

	if (context.statusCode !== undefined) {
		details.push(`Status: ${context.statusCode}`);
	}

	if (details.length > 0) {
		parts.push(`- ${details.join(" ")}`);
	}

	return parts.join(" ");
}

/**
 * Logger class with structured logging methods
 */

// biome-ignore lint/complexity/noStaticOnlyClass: Intentional for logging
export class Logger {
	private static shouldLog(level: LogLevel): boolean {
		const configLevel = process.env.LOG_LEVEL?.toUpperCase() || "INFO";
		const levels = ["ERROR", "WARN", "INFO", "DEBUG"];
		const configIndex = levels.indexOf(configLevel);
		const messageIndex = levels.indexOf(level);
		return messageIndex <= configIndex;
	}

	static error(
		prefix: EndpointPrefix,
		message: string,
		context?: Partial<LogContext>,
	): void {
		if (Logger.shouldLog(LogLevel.ERROR)) {
			console.error(formatLogMessage(LogLevel.ERROR, prefix, message, context));
		}
	}

	static warn(
		prefix: EndpointPrefix,
		message: string,
		context?: Partial<LogContext>,
	): void {
		if (Logger.shouldLog(LogLevel.WARN)) {
			console.warn(formatLogMessage(LogLevel.WARN, prefix, message, context));
		}
	}

	static info(
		prefix: EndpointPrefix,
		message: string,
		context?: Partial<LogContext>,
	): void {
		if (Logger.shouldLog(LogLevel.INFO)) {
			console.log(formatLogMessage(LogLevel.INFO, prefix, message, context));
		}
	}

	static debug(
		prefix: EndpointPrefix,
		message: string,
		context?: Partial<LogContext>,
	): void {
		if (Logger.shouldLog(LogLevel.DEBUG)) {
			console.log(formatLogMessage(LogLevel.DEBUG, prefix, message, context));
		}
	}
}

/**
 * Extract request context for logging
 */
export function extractRequestContext(req: Request): LogContext {
	return {
		method: req.method,
		url: req.originalUrl || req.url,
		ip: req.ip || req.socket?.remoteAddress || "unknown",
		userAgent: req.get("User-Agent"),
		contentType: req.get("Content-Type"),
		authorization: req.get("Authorization"),
		body: req.body,
		params: req.params,
		query: req.query,
	};
}

/**
 * Request logging middleware
 * Logs all incoming requests with comprehensive details
 */
export function requestLoggingMiddleware(prefix: EndpointPrefix) {
	return (req: Request, res: Response, next: NextFunction) => {
		const startTime = Date.now();
		const context = extractRequestContext(req);

		// Log incoming request
		Logger.info(prefix, "Incoming request", context);

		// Capture response details
		const originalSend = res.send;
		res.send = function (body) {
			const responseTime = Date.now() - startTime;
			const responseContext = {
				...context,
				responseTime,
				statusCode: res.statusCode,
			};

			// Log response
			if (res.statusCode >= 400) {
				Logger.error(prefix, "Request failed", responseContext);
			} else {
				Logger.info(prefix, "Request completed", responseContext);
			}

			return originalSend.call(this, body);
		};

		next();
	};
}
