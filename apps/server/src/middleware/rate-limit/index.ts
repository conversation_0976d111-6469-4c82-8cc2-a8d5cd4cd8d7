import { ErrorCodes } from "@snapback/shared";
import crypto from "crypto";
import rateLimit, { ip<PERSON>eyGenerator } from "express-rate-limit";
import { sendErrorResponse } from "@/utils/errors";

/**
 * Enhanced rate limiting with standardized error responses
 */

// General rate limiting configuration
const defaultWindowMs = Number.parseInt(
	process.env.RATE_LIMIT_WINDOW_MS || "900000",
); // 15 minutes
const defaultMaxRequests = Number.parseInt(
	process.env.RATE_LIMIT_MAX_REQUESTS || "100",
);

// Create license rate limiting (more restrictive)
const createLicenseLimit = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 5, // limit each IP to 5 requests per windowMs
	standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
	legacyHeaders: false, // Disable the `X-RateLimit-*` headers
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many license creation requests",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for license creation. Please try again later.",
			{
				windowMs: 15 * 60 * 1000,
				maxRequests: 5,
				retryAfter: Math.ceil((15 * 60 * 1000) / 1000), // seconds
			},
		);
	},
});

// Validate license rate limiting
const validateLicenseLimit = rateLimit({
	windowMs: 1 * 60 * 1000, // 1 minute
	max: 30, // 30 requests per minute per IP
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many validation requests",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for license validation. Please try again later.",
			{
				windowMs: 1 * 60 * 1000,
				maxRequests: 30,
				retryAfter: 60, // seconds
			},
		);
	},
});

// Resend license rate limiting (most restrictive)
const resendLicenseLimit = rateLimit({
	windowMs: 60 * 60 * 1000, // 1 hour
	max: 3, // 3 resend requests per hour
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many resend requests",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for license resend. Please try again later.",
			{
				windowMs: 60 * 60 * 1000,
				maxRequests: 3,
				retryAfter: 3600, // seconds (1 hour)
			},
		);
	},
});

// Payment rate limiting
const paymentLimit = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 10, // 10 payment attempts per 15 minutes
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many payment requests",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for payment processing. Please try again later.",
			{
				windowMs: 15 * 60 * 1000,
				maxRequests: 10,
				retryAfter: Math.ceil((15 * 60 * 1000) / 1000), // seconds
			},
		);
	},
});

// General API rate limiting
const generalApiLimit = rateLimit({
	windowMs: defaultWindowMs,
	max: defaultMaxRequests,
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Rate limit exceeded",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You have exceeded the general API rate limit. Please try again later.",
			{
				windowMs: defaultWindowMs,
				maxRequests: defaultMaxRequests,
				retryAfter: Math.ceil(defaultWindowMs / 1000), // seconds
			},
		);
	},
});

// User management rate limiting
const createUserLimit = rateLimit({
	windowMs: 60 * 60 * 1000, // 1 hour
	max: 10, // 10 user creations per hour per IP
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many user creation requests",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for user creation. Please try again later.",
			{
				windowMs: 60 * 60 * 1000,
				maxRequests: 10,
				retryAfter: 3600, // seconds (1 hour)
			},
		);
	},
});

// User invitation rate limiting (most restrictive to prevent spam)
const inviteUserLimit = rateLimit({
	windowMs: 60 * 60 * 1000, // 1 hour
	max: 20, // 20 invitations per hour per IP
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many invitation requests",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for sending invitations. Please try again later.",
			{
				windowMs: 60 * 60 * 1000,
				maxRequests: 20,
				retryAfter: 3600, // seconds (1 hour)
			},
		);
	},
});

// User update rate limiting
const updateUserLimit = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 50, // 50 user updates per 15 minutes per IP
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many user update requests",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for user updates. Please try again later.",
			{
				windowMs: 15 * 60 * 1000,
				maxRequests: 50,
				retryAfter: Math.ceil((15 * 60 * 1000) / 1000), // seconds
			},
		);
	},
});

// User listing rate limiting (more lenient for read operations)
const listUsersLimit = rateLimit({
	windowMs: 5 * 60 * 1000, // 5 minutes
	max: 100, // 100 list requests per 5 minutes per IP
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many user listing requests",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for user listing. Please try again later.",
			{
				windowMs: 5 * 60 * 1000,
				maxRequests: 100,
				retryAfter: Math.ceil((5 * 60 * 1000) / 1000), // seconds
			},
		);
	},
});

/**
 * Rate limiter for trial license creation
 * Much stricter than paid licenses to prevent abuse
 * 5 trials per IP per hour
 */
const trialLicenseLimit = rateLimit({
	windowMs: 60 * 60 * 1000, // 1 hour
	max: 5, // 5 trials per IP per hour
	standardHeaders: true,
	legacyHeaders: false,
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Too many trial license requests",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You have exceeded the rate limit for trial license creation. Please try again later.",
			{
				windowMs: 60 * 60 * 1000,
				maxRequests: 5,
				retryAfter: 3600, // seconds (1 hour)
			},
		);
	},
});

/**
 * Additional email-based rate limiter for trial licenses
 * 1 trial per email per day
 */
const trialEmailLimit = rateLimit({
	windowMs: 24 * 60 * 60 * 1000, // 24 hours
	max: 1, // 1 trial per email per day
	standardHeaders: true,
	legacyHeaders: false,
	keyGenerator: (req, _res) => {
		const email = req.body?.email;
		// Use email if provided, otherwise fall back to IP using proper IPv6 handling
		return email
			? `trial_email_${email.toLowerCase()}`
			: `trial_fallback_${ipKeyGenerator(req.ip || "unknown")}`;
	},
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Trial license limit exceeded",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"You can only create one trial license per email per day.",
			{
				windowMs: 24 * 60 * 60 * 1000,
				maxRequests: 1,
				retryAfter: 86400, // seconds (24 hours)
			},
		);
	},
});

/**
 * Device-based rate limiter for in-app trial requests
 * Uses device fingerprint and hardware UUID for more accurate limiting
 */
const deviceBasedTrialLimit = rateLimit({
	windowMs: 24 * 60 * 60 * 1000, // 24 hours
	max: 1, // 1 trial per device per day
	standardHeaders: true,
	legacyHeaders: false,
	keyGenerator: (req) => {
		const deviceId = req.body?.deviceId;
		const deviceFingerprint = req.body?.deviceFingerprint;
		const hardwareUUID = req.body?.deviceMetadata?.hardwareUUID;

		// Create composite key from multiple device identifiers
		const deviceKey = [deviceId, deviceFingerprint, hardwareUUID]
			.filter(Boolean)
			.join("|");

		return `device_trial_${crypto.createHash("sha256").update(deviceKey).digest("hex")}`;
	},
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Device trial limit exceeded",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"This device has already requested a trial license. Each device can only request one trial per day.",
			{
				windowMs: 24 * 60 * 60 * 1000,
				maxRequests: 1,
				retryAfter: 86400, // seconds (24 hours)
			},
		);
	},
});

/**
 * Hardware-based rate limiter for additional protection
 * Prevents abuse through hardware spoofing
 */
const hardwareBasedTrialLimit = rateLimit({
	windowMs: 60 * 60 * 1000, // 1 hour
	max: 3, // 3 trials per hardware signature per hour
	standardHeaders: true,
	legacyHeaders: false,
	keyGenerator: (req, _res) => {
		const deviceMetadata = req.body?.deviceMetadata;
		if (!deviceMetadata)
			return `fallback_${ipKeyGenerator(req.ip || "unknown")}`;

		// Create hardware signature from multiple components
		const hardwareSignature = [
			deviceMetadata.operatingSystem,
			deviceMetadata.architecture,
			deviceMetadata.totalMemory,
			deviceMetadata.screenResolution,
			deviceMetadata.deviceModel,
		].join("|");

		return `hardware_trial_${crypto.createHash("sha256").update(hardwareSignature).digest("hex")}`;
	},
	handler: (_req, res) => {
		sendErrorResponse(
			res,
			"Hardware trial limit exceeded",
			429,
			ErrorCodes.RATE_LIMIT_EXCEEDED,
			"Too many trial requests from similar hardware configurations. Please try again later.",
			{
				windowMs: 60 * 60 * 1000,
				maxRequests: 3,
				retryAfter: 3600, // seconds (1 hour)
			},
		);
	},
});

export {
	createLicenseLimit,
	validateLicenseLimit,
	resendLicenseLimit,
	paymentLimit,
	generalApiLimit,
	// User management rate limits
	createUserLimit,
	inviteUserLimit,
	updateUserLimit,
	listUsersLimit,
	// Trial license rate limits
	trialLicenseLimit,
	trialEmailLimit,
	// In-app trial rate limits
	deviceBasedTrialLimit,
	hardwareBasedTrialLimit,
};
