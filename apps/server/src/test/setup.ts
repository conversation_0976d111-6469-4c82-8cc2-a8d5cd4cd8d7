import { vi } from "vitest";

// Mock environment variables for testing
process.env.NODE_ENV = "test";
process.env.JWT_SECRET = "test-jwt-secret";
process.env.STRIPE_SECRET_KEY = "sk_test_fake_key_for_testing";
process.env.FROM_EMAIL = "<EMAIL>";
process.env.SMTP_HOST = "localhost";
process.env.SMTP_PORT = "587";
process.env.SMTP_USER = "test";
process.env.SMTP_PASS = "test";

// Mock Prisma client for testing
export const testPrisma = {
	license: {
		create: vi.fn(),
		findUnique: vi.fn(),
		update: vi.fn(),
		deleteMany: vi.fn(),
	},
	refundRequest: {
		create: vi.fn(),
		findUnique: vi.fn(),
		findMany: vi.fn(),
		update: vi.fn(),
		deleteMany: vi.fn(),
		createMany: vi.fn(),
	},
	device: {
		findMany: vi.fn(),
		updateMany: vi.fn(),
		deleteMany: vi.fn(),
		createMany: vi.fn(),
	},
	auditLog: {
		create: vi.fn(),
		findMany: vi.fn(),
		findFirst: vi.fn(),
		deleteMany: vi.fn(),
	},
	$transaction: vi.fn(),
	$connect: vi.fn(),
	$disconnect: vi.fn(),
};
