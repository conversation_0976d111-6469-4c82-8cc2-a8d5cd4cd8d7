/**
 * Standardized API Response Types
 *
 * All API endpoints should return responses in this format for consistency
 */

export interface ApiResponse<T> {
	success: boolean;
	data: T;
	message: string;
	error?: {
		code: string;
		message: string;
		details?: unknown;
	};
}

export interface PaginationMeta {
	page: number;
	limit: number;
	total: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}

export interface PaginatedResponse<T> {
	data: T[];
	pagination: PaginationMeta;
}

// Common error codes used across the API
export const ErrorCodes = {
	// Authentication & Authorization
	UNAUTHORIZED: "UNAUTHORIZED",
	FORBIDDEN: "FORBIDDEN",
	INVALID_TOKEN: "INVALID_TOKEN",
	TOKEN_EXPIRED: "TOKEN_EXPIRED",
	INSUFFICIENT_PERMISSIONS: "INSUFFICIENT_PERMISSIONS",

	// Validation
	VALIDATION_ERROR: "VALIDATION_ERROR",
	INVALID_REQUEST_DATA: "INVALID_REQUEST_DATA",
	MISSING_REQUIRED_FIELD: "MISSING_REQUIRED_FIELD",

	// License Management
	LICENSE_NOT_FOUND: "LICENSE_NOT_FOUND",
	LICENSE_EXISTS: "LICENSE_EXISTS",
	LICENSE_NOT_ACTIVE: "LICENSE_NOT_ACTIVE",
	LICENSE_EXPIRED: "LICENSE_EXPIRED",
	LICENSE_SUSPENDED: "LICENSE_SUSPENDED",
	LICENSE_CREATION_FAILED: "LICENSE_CREATION_FAILED",
	INVALID_LICENSE_KEY: "INVALID_LICENSE_KEY",

	// Device Management
	DEVICE_NOT_FOUND: "DEVICE_NOT_FOUND",
	DEVICE_LIMIT_EXCEEDED: "DEVICE_LIMIT_EXCEEDED",
	DEVICE_ALREADY_REGISTERED: "DEVICE_ALREADY_REGISTERED",
	DEVICE_REGISTRATION_FAILED: "DEVICE_REGISTRATION_FAILED",
	INVALID_DEVICE_ID: "INVALID_DEVICE_ID",

	// Payment Processing
	PAYMENT_NOT_FOUND: "PAYMENT_NOT_FOUND",
	PAYMENT_FAILED: "PAYMENT_FAILED",
	PAYMENT_ALREADY_PROCESSED: "PAYMENT_ALREADY_PROCESSED",
	STRIPE_ERROR: "STRIPE_ERROR",
	CHECKOUT_SESSION_EXPIRED: "CHECKOUT_SESSION_EXPIRED",
	INVALID_PAYMENT_INTENT: "INVALID_PAYMENT_INTENT",

	// Refund Management
	REFUND_REQUEST_NOT_FOUND: "REFUND_REQUEST_NOT_FOUND",
	REFUND_REQUEST_EXISTS: "REFUND_REQUEST_EXISTS",
	REFUND_NOT_ELIGIBLE: "REFUND_NOT_ELIGIBLE",
	REFUND_PROCESSING_FAILED: "REFUND_PROCESSING_FAILED",
	ALREADY_REFUNDED: "ALREADY_REFUNDED",

	// User Management
	USER_NOT_FOUND: "USER_NOT_FOUND",
	USER_EXISTS: "USER_EXISTS",
	USER_NOT_ACTIVE: "USER_NOT_ACTIVE",
	INVITATION_NOT_FOUND: "INVITATION_NOT_FOUND",
	INVITATION_EXPIRED: "INVITATION_EXPIRED",
	INVITATION_ALREADY_ACCEPTED: "INVITATION_ALREADY_ACCEPTED",

	// System & Infrastructure
	INTERNAL_ERROR: "INTERNAL_ERROR",
	SERVICE_UNAVAILABLE: "SERVICE_UNAVAILABLE",
	DATABASE_ERROR: "DATABASE_ERROR",
	EXTERNAL_SERVICE_ERROR: "EXTERNAL_SERVICE_ERROR",
	RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED",

	// Email & Notifications
	EMAIL_DELIVERY_FAILED: "EMAIL_DELIVERY_FAILED",
	INVALID_EMAIL_ADDRESS: "INVALID_EMAIL_ADDRESS",
	EMAIL_TEMPLATE_NOT_FOUND: "EMAIL_TEMPLATE_NOT_FOUND",

	// Webhook Processing
	WEBHOOK_VERIFICATION_FAILED: "WEBHOOK_VERIFICATION_FAILED",
	WEBHOOK_PROCESSING_FAILED: "WEBHOOK_PROCESSING_FAILED",
	WEBHOOK_ALREADY_PROCESSED: "WEBHOOK_ALREADY_PROCESSED",
} as const;

export type ErrorCode = (typeof ErrorCodes)[keyof typeof ErrorCodes];

// HTTP Status Code mappings for common error types
export const HttpStatusCodes = {
	// Success
	OK: 200,
	CREATED: 201,
	ACCEPTED: 202,
	NO_CONTENT: 204,

	// Client Errors
	BAD_REQUEST: 400,
	UNAUTHORIZED: 401,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	METHOD_NOT_ALLOWED: 405,
	CONFLICT: 409,
	UNPROCESSABLE_ENTITY: 422,
	TOO_MANY_REQUESTS: 429,

	// Server Errors
	INTERNAL_SERVER_ERROR: 500,
	BAD_GATEWAY: 502,
	SERVICE_UNAVAILABLE: 503,
	GATEWAY_TIMEOUT: 504,
} as const;

// Request/Response type definitions for common operations
export interface CreateLicenseRequest {
	email: string;
	licenseType: "TRIAL" | "PRO" | "ENTERPRISE";
	deviceId?: string;
	trialDurationDays?: number;
}

export interface ValidateLicenseRequest {
	licenseKey: string;
	deviceId: string;
	appVersion?: string;
	deviceMetadata?: {
		deviceName?: string;
		deviceType?: string;
		deviceModel?: string;
		operatingSystem?: string;
		architecture?: string;
		screenResolution?: string;
		totalMemory?: string;
		userNickname?: string;
		location?: string;
		notes?: string;
	};
}

export interface CreateCheckoutRequest {
	licenseType: "PRO" | "ENTERPRISE";
	customerEmail: string;
	customerName?: string;
	successUrl: string;
	cancelUrl: string;
}

export interface RefundRequestRequest {
	licenseKey: string;
	reason: string;
	requestedAmount?: number;
}

export interface CreateInvitationRequest {
	email: string;
	role: "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "USER" | "VIEWER";
	expiresInDays?: number;
}

// Common query parameters for list endpoints
export interface ListQueryParams {
	page?: number;
	limit?: number;
	search?: string;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
}

// License-specific query parameters
export interface LicenseListQueryParams extends ListQueryParams {
	status?: "ACTIVE" | "EXPIRED" | "SUSPENDED" | "REFUNDED" | "CANCELLED";
	licenseType?: "TRIAL" | "PRO" | "ENTERPRISE";
	email?: string;
}

// Device-specific query parameters
export interface DeviceListQueryParams extends ListQueryParams {
	status?: "ACTIVE" | "INACTIVE" | "REMOVED";
	licenseId?: string;
}

// Refund-specific query parameters
export interface RefundListQueryParams extends ListQueryParams {
	status?: "PENDING" | "APPROVED" | "REJECTED" | "PROCESSED" | "FAILED";
}

// Audit log query parameters
export interface AuditLogQueryParams extends ListQueryParams {
	action?: string;
	actorId?: string;
	customerEmail?: string;
	licenseId?: string;
	startDate?: string;
	endDate?: string;
}

// Re-export helper functions from shared package for backward compatibility
export {
	createErrorResponse,
	createPaginatedResponse,
	createPaginationMeta,
	createSuccessResponse,
	formatValidationError,
	getHttpStatusFromErrorCode,
	validatePaginationParams,
} from "@snapback/shared";
