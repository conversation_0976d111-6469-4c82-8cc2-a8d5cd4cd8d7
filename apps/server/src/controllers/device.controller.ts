import { hashDeviceId } from "@/utils";
import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../../../packages/shared/src/prisma";
import type {
	Device,
	DeviceExpansion,
	License,
} from "../../../../packages/shared/src/prisma/generated/client";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface DeviceMetadata {
	deviceName?: string;
	deviceType?: string;
	deviceModel?: string;
	operatingSystem?: string;
	architecture?: string;
	screenResolution?: string;
	totalMemory?: string;
	userNickname?: string;
	location?: string;
	notes?: string;
}

export interface DeviceRegistrationOptions {
	licenseId: string;
	deviceId: string;
	appVersion?: string;
	deviceMetadata?: DeviceMetadata;
}

export interface DeviceRegistrationResult {
	success: boolean;
	device?: Device;
	error?: string;
	errorCode?: string;
}

export interface DeviceExpansionOptions {
	licenseId: string;
	paymentIntentId: string;
	additionalDevices: number;
	amount: number;
}

export interface DeviceExpansionResult {
	success: boolean;
	expansion?: DeviceExpansion;
	license?: License;
	error?: string;
}

// ============================================================================
// DEVICE REGISTRATION & MANAGEMENT
// ============================================================================

/**
 * Register a new device for a license
 */
export async function registerDevice(
	options: DeviceRegistrationOptions,
): Promise<DeviceRegistrationResult> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_VALIDATE,
			`Registering device for license: ${options.licenseId}`,
			{ body: { licenseId: options.licenseId } },
		);

		// Get license with current devices
		const license = await prisma.license.findUnique({
			where: { id: options.licenseId },
			include: {
				devices: {
					where: { status: "ACTIVE" },
				},
			},
		});

		if (!license) {
			return {
				success: false,
				error: "License not found",
				errorCode: "LICENSE_NOT_FOUND",
			};
		}

		// Check if license is active
		if (license.status !== "ACTIVE") {
			return {
				success: false,
				error: `License is ${license.status.toLowerCase()}`,
				errorCode: "LICENSE_NOT_ACTIVE",
			};
		}

		// Hash device ID for security
		const { hash: deviceHash, salt } = hashDeviceId(options.deviceId);

		// Check if device is already registered
		const existingDevice = await prisma.device.findFirst({
			where: {
				licenseId: options.licenseId,
				deviceHash,
				status: "ACTIVE",
			},
		});

		if (existingDevice) {
			// Update existing device
			const updatedDevice = await prisma.device.update({
				where: { id: existingDevice.id },
				data: {
					lastSeen: new Date(),
					appVersion: options.appVersion,
					...options.deviceMetadata,
				},
			});

			Logger.info(
				EndpointPrefix.LICENSE_VALIDATE,
				`Device updated: ${existingDevice.id}`,
				{ body: { deviceId: existingDevice.id, licenseId: options.licenseId } },
			);

			return {
				success: true,
				device: updatedDevice,
			};
		}

		// Check device limit
		const activeDeviceCount = license.devices.length;
		if (activeDeviceCount >= license.maxDevices) {
			Logger.warn(
				EndpointPrefix.LICENSE_VALIDATE,
				`Device limit exceeded for license: ${options.licenseId}`,
				{
					body: {
						licenseId: options.licenseId,
						activeDevices: activeDeviceCount,
						maxDevices: license.maxDevices,
					},
				},
			);

			return {
				success: false,
				error: `Device limit exceeded. Maximum ${license.maxDevices} devices allowed.`,
				errorCode: "DEVICE_LIMIT_EXCEEDED",
			};
		}

		// Register new device
		const device = await prisma.device.create({
			data: {
				licenseId: options.licenseId,
				deviceHash,
				salt,
				firstSeen: new Date(),
				lastSeen: new Date(),
				status: "ACTIVE",
				appVersion: options.appVersion,
				...options.deviceMetadata,
			},
		});

		// Update license used devices count and activation status
		await prisma.license.update({
			where: { id: options.licenseId },
			data: {
				usedDevices: activeDeviceCount + 1,
				activatedAt: license.activatedAt || new Date(),
			},
		});

		Logger.info(
			EndpointPrefix.LICENSE_VALIDATE,
			`New device registered: ${device.id}`,
			{
				body: {
					deviceId: device.id,
					licenseId: options.licenseId,
					deviceCount: activeDeviceCount + 1,
				},
			},
		);

		return {
			success: true,
			device,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_VALIDATE,
			`Failed to register device: ${error}`,
			{ body: { licenseId: options.licenseId } },
		);

		return {
			success: false,
			error: "Failed to register device",
			errorCode: "REGISTRATION_ERROR",
		};
	}
}

/**
 * Remove/deactivate a device
 */
export async function removeDevice(
	licenseId: string,
	deviceId: string,
): Promise<boolean> {
	try {
		Logger.info(
			EndpointPrefix.LICENSE_REMOVE_DEVICE,
			`Removing device: ${deviceId} from license: ${licenseId}`,
			{ body: { licenseId, deviceId } },
		);

		// Hash device ID for lookup
		const { hash: deviceHash } = hashDeviceId(deviceId);

		// Find and update device status
		const device = await prisma.device.findFirst({
			where: {
				licenseId,
				deviceHash,
				status: "ACTIVE",
			},
		});

		if (!device) {
			Logger.warn(
				EndpointPrefix.LICENSE_REMOVE_DEVICE,
				`Device not found or already removed: ${deviceId}`,
				{ body: { licenseId, deviceId } },
			);
			return false;
		}

		// Update device status to removed
		await prisma.device.update({
			where: { id: device.id },
			data: {
				status: "REMOVED",
				removedAt: new Date(),
			},
		});

		// Update license used devices count
		const license = await prisma.license.findUnique({
			where: { id: licenseId },
			include: {
				devices: {
					where: { status: "ACTIVE" },
				},
			},
		});

		if (license) {
			await prisma.license.update({
				where: { id: licenseId },
				data: {
					usedDevices: license.devices.length,
				},
			});
		}

		Logger.info(
			EndpointPrefix.LICENSE_REMOVE_DEVICE,
			`Device removed successfully: ${device.id}`,
			{ body: { deviceId: device.id, licenseId } },
		);

		return true;
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_REMOVE_DEVICE,
			`Failed to remove device: ${error}`,
			{ body: { licenseId, deviceId } },
		);
		return false;
	}
}

// ============================================================================
// DEVICE EXPANSION
// ============================================================================

/**
 * Process device expansion purchase
 */
export async function processDeviceExpansion(
	options: DeviceExpansionOptions,
): Promise<DeviceExpansionResult> {
	try {
		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Processing device expansion for license: ${options.licenseId}`,
			{
				body: {
					licenseId: options.licenseId,
					additionalDevices: options.additionalDevices,
					amount: options.amount,
				},
			},
		);

		// Create device expansion record
		const expansion = await prisma.deviceExpansion.create({
			data: {
				licenseId: options.licenseId,
				paymentIntentId: options.paymentIntentId,
				additionalDevices: options.additionalDevices,
				amount: options.amount,
				status: "PROCESSED",
				processedAt: new Date(),
			},
		});

		// Update license max devices and get the updated license
		const updatedLicense = await prisma.license.update({
			where: { id: options.licenseId },
			data: {
				maxDevices: { increment: options.additionalDevices },
				totalPaidAmount: { increment: options.amount },
			},
		});

		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Device expansion processed: ${expansion.id}`,
			{
				body: {
					expansionId: expansion.id,
					licenseId: options.licenseId,
					additionalDevices: options.additionalDevices,
				},
			},
		);

		return {
			success: true,
			expansion,
			license: updatedLicense,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Failed to process device expansion: ${error}`,
			{
				body: {
					licenseId: options.licenseId,
					additionalDevices: options.additionalDevices,
				},
			},
		);

		return {
			success: false,
			error: "Failed to process device expansion",
		};
	}
}

// ============================================================================
// LOOKUP FUNCTIONS
// ============================================================================

/**
 * Get devices for a license
 */
export async function getDevicesForLicense(
	licenseId: string,
): Promise<Device[]> {
	try {
		const devices = await prisma.device.findMany({
			where: {
				licenseId,
				status: "ACTIVE",
			},
			orderBy: {
				firstSeen: "asc",
			},
		});

		return devices;
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_STATUS,
			`Failed to get devices for license: ${error}`,
			{ body: { licenseId } },
		);
		return [];
	}
}

/**
 * Get device expansions for a license
 */
export async function getDeviceExpansionsForLicense(
	licenseId: string,
): Promise<DeviceExpansion[]> {
	try {
		const expansions = await prisma.deviceExpansion.findMany({
			where: { licenseId },
			include: {
				paymentIntent: true,
			},
			orderBy: {
				createdAt: "desc",
			},
		});

		return expansions;
	} catch (error) {
		Logger.error(
			EndpointPrefix.LICENSE_STATUS,
			`Failed to get device expansions for license: ${error}`,
			{ body: { licenseId } },
		);
		return [];
	}
}

// ============================================================================
// ADMIN DEVICE MANAGEMENT FUNCTIONS
// ============================================================================

import {
	prepareDatabaseQuery,
	type StandardPaginationParams,
} from "@/utils/pagination";

/**
 * Get all devices with pagination and filtering (Admin)
 */
export async function getDevices(
	params: StandardPaginationParams,
): Promise<{ devices: any[]; total: number }> {
	try {
		// Use enhanced pagination utilities
		const { skip, take, where, orderBy } = prepareDatabaseQuery(
			params,
			["deviceName", "userNickname", "deviceType", "deviceModel"], // Search fields
			{
				// Additional filters from params.filters
				...(params.filters?.licenseId && {
					licenseId: params.filters.licenseId,
				}),
				...(params.filters?.status && {
					status: params.filters.status,
				}),
				...(params.filters?.deviceType && {
					deviceType: params.filters.deviceType,
				}),
				...(params.filters?.operatingSystem && {
					operatingSystem: params.filters.operatingSystem,
				}),
			},
		);

		// Get devices with license information
		const [devices, total] = await Promise.all([
			prisma.device.findMany({
				skip,
				take,
				where,
				orderBy,
				include: {
					license: {
						select: {
							id: true,
							licenseKey: true,
							customerEmail: true,
							customerName: true,
							status: true,
							licenseType: true,
							maxDevices: true,
							expiresAt: true,
						},
					},
				},
			}),
			prisma.device.count({ where }),
		]);

		Logger.info(
			EndpointPrefix.DEVICE_LIST,
			`Retrieved ${devices.length} devices (total: ${total})`,
			{ body: { page: params.page, limit: params.limit, total } },
		);

		return { devices, total };
	} catch (error) {
		Logger.error(
			EndpointPrefix.DEVICE_LIST,
			`Failed to get devices: ${error}`,
			{ body: { ...params } },
		);
		return { devices: [], total: 0 };
	}
}

/**
 * Get specific device details (Admin)
 */
export async function getDeviceById(
	deviceId: string,
): Promise<{ device: any | null; error?: string }> {
	try {
		const device = await prisma.device.findUnique({
			where: { id: deviceId },
			include: {
				license: {
					select: {
						id: true,
						licenseKey: true,
						customerEmail: true,
						customerName: true,
						status: true,
						licenseType: true,
						maxDevices: true,
						expiresAt: true,
						createdAt: true,
					},
				},
			},
		});

		if (!device) {
			return { device: null, error: "Device not found" };
		}

		Logger.info(
			EndpointPrefix.DEVICE_DETAILS,
			`Retrieved device details: ${device.deviceHash}`,
			{ body: { deviceId, licenseId: device.licenseId } },
		);

		return { device };
	} catch (error) {
		Logger.error(
			EndpointPrefix.DEVICE_DETAILS,
			`Failed to get device: ${error}`,
			{ body: { deviceId } },
		);
		return { device: null, error: "Failed to retrieve device" };
	}
}

/**
 * Remove device from license (Admin)
 */
export async function adminRemoveDevice(
	deviceId: string,
	actorId: string,
): Promise<{ success: boolean; error?: string }> {
	try {
		const device = await prisma.device.findUnique({
			where: { id: deviceId },
			include: {
				license: {
					select: {
						id: true,
						licenseKey: true,
						customerEmail: true,
					},
				},
			},
		});

		if (!device) {
			return { success: false, error: "Device not found" };
		}

		// Remove device
		await prisma.device.delete({
			where: { id: deviceId },
		});

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "DEVICE_REMOVED", // Type assertion needed due to enum mismatch
				actorId,
				targetId: deviceId,
				licenseKey: device.license.licenseKey,
				customerEmail: device.license.customerEmail,
				details: {
					deviceHash: device.deviceHash,
					deviceName: device.deviceName,
					reason: "Admin removal",
				},
			},
		});

		Logger.info(
			EndpointPrefix.DEVICE_REMOVE,
			`Device removed: ${device.deviceHash}`,
			{ body: { deviceId, licenseId: device.licenseId, actorId } },
		);

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.DEVICE_REMOVE,
			`Failed to remove device: ${error}`,
			{ body: { deviceId, actorId } },
		);
		return { success: false, error: "Failed to remove device" };
	}
}

/**
 * Update device metadata (Admin)
 */
export async function adminUpdateDevice(
	deviceId: string,
	updateData: {
		deviceName?: string;
		userNickname?: string;
		location?: string;
		status?: "ACTIVE" | "INACTIVE";
	},
	actorId: string,
): Promise<{ success: boolean; device?: any; error?: string }> {
	try {
		const existingDevice = await prisma.device.findUnique({
			where: { id: deviceId },
			include: {
				license: {
					select: {
						id: true,
						licenseKey: true,
						customerEmail: true,
					},
				},
			},
		});

		if (!existingDevice) {
			return { success: false, error: "Device not found" };
		}

		// Update device
		const updatedDevice = await prisma.device.update({
			where: { id: deviceId },
			data: updateData,
			include: {
				license: {
					select: {
						id: true,
						licenseKey: true,
						customerEmail: true,
						customerName: true,
						status: true,
						licenseType: true,
						maxDevices: true,
						expiresAt: true,
					},
				},
			},
		});

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "DEVICE_UPDATED", // Type assertion needed due to enum mismatch
				actorId,
				targetId: deviceId,
				licenseKey: existingDevice.license.licenseKey,
				customerEmail: existingDevice.license.customerEmail,
				details: {
					deviceHash: existingDevice.deviceHash,
					changes: updateData,
					previousData: {
						deviceName: existingDevice.deviceName,
						userNickname: existingDevice.userNickname,
						location: existingDevice.location,
						status: existingDevice.status,
					},
				},
			},
		});

		Logger.info(
			EndpointPrefix.DEVICE_UPDATE,
			`Device updated: ${existingDevice.deviceHash}`,
			{ body: { deviceId, changes: updateData, actorId } },
		);

		return { success: true, device: updatedDevice };
	} catch (error) {
		Logger.error(
			EndpointPrefix.DEVICE_UPDATE,
			`Failed to update device: ${error}`,
			{ body: { deviceId, updateData, actorId } },
		);
		return { success: false, error: "Failed to update device" };
	}
}

// ============================================================================
// DEVICE EXPANSION MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Purchase additional device slots (Customer)
 */
export async function purchaseDeviceExpansion(
	licenseKey: string,
	additionalDevices: number,
): Promise<{ success: boolean; checkoutUrl?: string; error?: string }> {
	try {
		// Find license by key
		const license = await prisma.license.findUnique({
			where: { licenseKey },
		});

		if (!license) {
			return { success: false, error: "License not found" };
		}

		if (license.status !== "ACTIVE") {
			return { success: false, error: "License is not active" };
		}

		// Calculate price (e.g., $1.99 per additional device)
		const pricePerDevice = 199; // $1.99 in cents
		const totalAmount = additionalDevices * pricePerDevice;

		// Import Stripe dynamically
		const stripe = (await import("stripe")).default;
		const stripeClient = new stripe(process.env.STRIPE_SECRET_KEY!);

		// Create Stripe checkout session
		const session = await stripeClient.checkout.sessions.create({
			payment_method_types: ["card"],
			line_items: [
				{
					price_data: {
						currency: "usd",
						product_data: {
							name: `Additional Device Slots (${additionalDevices})`,
							description: `Add ${additionalDevices} more device${additionalDevices > 1 ? "s" : ""} to your SnapBack license`,
						},
						unit_amount: pricePerDevice,
					},
					quantity: additionalDevices,
				},
			],
			mode: "payment",
			success_url: `${process.env.FRONTEND_URL}/expansion/success?session_id={CHECKOUT_SESSION_ID}`,
			cancel_url: `${process.env.FRONTEND_URL}/expansion/cancel`,
			metadata: {
				type: "device_expansion",
				licenseId: license.id,
				licenseKey: license.licenseKey,
				additionalDevices: additionalDevices.toString(),
			},
		});

		// Create pending device expansion record
		await prisma.deviceExpansion.create({
			data: {
				licenseId: license.id,
				paymentIntentId: session.payment_intent as string,
				additionalDevices,
				amount: totalAmount,
				status: "PENDING",
			},
		});

		Logger.info(
			EndpointPrefix.DEVICE_EXPANSION,
			`Device expansion checkout created: ${session.id}`,
			{
				body: {
					licenseKey,
					additionalDevices,
					amount: totalAmount,
					sessionId: session.id,
				},
			},
		);

		return { success: true, checkoutUrl: session.url as string };
	} catch (error) {
		Logger.error(
			EndpointPrefix.DEVICE_EXPANSION,
			`Failed to create device expansion checkout: ${error}`,
			{ body: { licenseKey, additionalDevices } },
		);
		return { success: false, error: "Failed to create checkout session" };
	}
}

/**
 * Get all device expansions with pagination and filtering (Admin)
 */
export async function getDeviceExpansions(
	params: StandardPaginationParams,
): Promise<{ expansions: any[]; total: number }> {
	try {
		// Use enhanced pagination utilities
		const { skip, take, where, orderBy } = prepareDatabaseQuery(
			params,
			[], // No search fields for expansions
			{
				// Additional filters from params.filters
				...(params.filters?.licenseId && {
					licenseId: params.filters.licenseId,
				}),
				...(params.filters?.status && {
					status: params.filters.status,
				}),
			},
		);

		// Get expansions with license information
		const [expansions, total] = await Promise.all([
			prisma.deviceExpansion.findMany({
				skip,
				take,
				where,
				orderBy,
				include: {
					license: {
						select: {
							id: true,
							licenseKey: true,
							customerEmail: true,
							customerName: true,
							status: true,
							licenseType: true,
							maxDevices: true,
						},
					},
					paymentIntent: {
						select: {
							id: true,
							amount: true,
							status: true,
							createdAt: true,
						},
					},
				},
			}),
			prisma.deviceExpansion.count({ where }),
		]);

		Logger.info(
			EndpointPrefix.DEVICE_EXPANSION_LIST,
			`Retrieved ${expansions.length} device expansions (total: ${total})`,
			{ body: { page: params.page, limit: params.limit, total } },
		);

		return { expansions, total };
	} catch (error) {
		Logger.error(
			EndpointPrefix.DEVICE_EXPANSION_LIST,
			`Failed to get device expansions: ${error}`,
			{ body: { ...params } },
		);
		return { expansions: [], total: 0 };
	}
}

/**
 * Manually process device expansion (Admin)
 */
export async function processDeviceExpansionAdmin(
	expansionId: string,
	approve: boolean,
	adminNotes: string | undefined,
	actorId: string,
): Promise<{ success: boolean; error?: string }> {
	try {
		const expansion = await prisma.deviceExpansion.findUnique({
			where: { id: expansionId },
			include: {
				license: {
					select: {
						id: true,
						licenseKey: true,
						customerEmail: true,
						maxDevices: true,
					},
				},
			},
		});

		if (!expansion) {
			return { success: false, error: "Device expansion not found" };
		}

		if (expansion.status !== "PENDING") {
			return { success: false, error: "Device expansion is not pending" };
		}

		const newStatus = approve ? "PROCESSED" : "FAILED";

		// Update expansion status
		await prisma.deviceExpansion.update({
			where: { id: expansionId },
			data: {
				status: newStatus,
				processedAt: new Date(),
			},
		});

		// If approved, update license max devices
		if (approve) {
			await prisma.license.update({
				where: { id: expansion.licenseId },
				data: {
					maxDevices: { increment: expansion.additionalDevices },
				},
			});
		}

		// Create audit log
		await prisma.auditLog.create({
			data: {
				action: "DEVICE_EXPANSION_PROCESSED",
				actorId,
				targetId: expansionId,
				licenseKey: expansion.license.licenseKey,
				customerEmail: expansion.license.customerEmail,
				details: {
					expansionId,
					additionalDevices: expansion.additionalDevices,
					amount: expansion.amount,
					adminNotes,
					previousMaxDevices: expansion.license.maxDevices,
					newMaxDevices: approve
						? expansion.license.maxDevices + expansion.additionalDevices
						: expansion.license.maxDevices,
				},
			},
		});

		Logger.info(
			EndpointPrefix.DEVICE_EXPANSION_PROCESS,
			`Device expansion ${approve ? "approved" : "rejected"}: ${expansionId}`,
			{
				body: {
					expansionId,
					approve,
					additionalDevices: expansion.additionalDevices,
					licenseKey: expansion.license.licenseKey,
					actorId,
				},
			},
		);

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.DEVICE_EXPANSION_PROCESS,
			`Failed to process device expansion: ${error}`,
			{ body: { expansionId, approve, actorId } },
		);
		return { success: false, error: "Failed to process device expansion" };
	}
}
