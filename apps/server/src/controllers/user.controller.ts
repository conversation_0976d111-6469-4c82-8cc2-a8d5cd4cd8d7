import crypto from "crypto";
import { Endpoint<PERSON>refix, Logger } from "@/utils/logger";
import {
	prepareDatabaseQuery,
	type StandardPaginationParams,
} from "@/utils/pagination";
import prisma from "../../../../packages/shared/src/prisma";
import type {
	AuditLog,
	User,
	UserInvitation,
} from "../../../../packages/shared/src/prisma/generated/client";
import type {
	AuditAction,
	UserRole,
} from "../../../../packages/shared/src/prisma/generated/enums";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface CreateUserInvitationOptions {
	email: string;
	role: UserRole;
	sentBy: string; // User ID of the person sending the invitation
	expiresInDays?: number;
}

export interface CreateUserInvitationResult {
	success: boolean;
	invitation?: UserInvitation;
	error?: string;
}

export interface AcceptInvitationOptions {
	token: string;
	userData: {
		name: string;
		email: string;
		password?: string;
	};
}

export interface AcceptInvitationResult {
	success: boolean;
	user?: User;
	error?: string;
}

export interface CreateUserOptions {
	name: string;
	email: string;
	role: "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "USER" | "VIEWER";
	createdBy: string; // User ID of the person creating the user
}

export interface CreateUserResult {
	success: boolean;
	user?: User;
	error?: string;
}

export interface UpdateUserOptions {
	userId: string;
	name?: string;
	role?: UserRole;
	isActive?: boolean;
	updatedBy: string; // User ID of the person updating the user
}

export interface UpdateUserResult {
	success: boolean;
	user?: User;
	error?: string;
}

// Custom user type for API responses (subset of full User model)
export interface UserSummary {
	id: string;
	name: string;
	email: string;
	role: UserRole;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface CreateAuditLogOptions {
	action: AuditAction;
	actorId?: string;
	actorEmail?: string;
	targetId?: string;
	customerEmail?: string;
	licenseKey?: string;
	licenseId?: string;
	deviceId?: string;
	deviceHash?: string;
	ipAddress?: string;
	userAgent?: string;
	details?: any;
}

// ============================================================================
// USER MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Get all users with pagination and filtering (using enhanced utilities)
 */
export async function getUsers(
	params: StandardPaginationParams,
): Promise<{ users: UserSummary[]; total: number }> {
	try {
		// Use enhanced pagination utilities - eliminates manual calculations
		const { skip, take, where, orderBy } = prepareDatabaseQuery(
			params,
			["name", "email"], // searchFields for user search
			{
				// Additional filters from params.filters
				...(params.filters?.role && { role: params.filters.role }),
				...(params.filters?.isActive !== undefined && {
					isActive: params.filters.isActive,
				}),
			},
		);

		const [users, total] = await Promise.all([
			prisma.user.findMany({
				where,
				skip,
				take,
				orderBy,
				select: {
					id: true,
					name: true,
					email: true,
					role: true,
					isActive: true,
					createdAt: true,
					updatedAt: true,
				},
			}),
			prisma.user.count({ where }),
		]);

		return { users, total };
	} catch (error) {
		Logger.error(EndpointPrefix.USER_LIST, `Failed to get users: ${error}`, {
			body: { ...params },
		});
		return { users: [], total: 0 };
	}
}

/**
 * Get user by ID
 */
export async function getUserById(userId: string): Promise<UserSummary | null> {
	try {
		const user = await prisma.user.findUnique({
			where: { id: userId },
			select: {
				id: true,
				name: true,
				email: true,
				role: true,
				isActive: true,
				createdAt: true,
				updatedAt: true,
			},
		});

		return user;
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_LIST,
			`Failed to get user by ID: ${error}`,
			{ body: { userId } },
		);
		return null;
	}
}

/**
 * Create a new user
 */
export async function createUser(
	options: CreateUserOptions,
): Promise<CreateUserResult> {
	try {
		// Check if user already exists
		const existingUser = await prisma.user.findUnique({
			where: { email: options.email },
		});

		if (existingUser) {
			return { success: false, error: "User with this email already exists" };
		}

		const user = await prisma.user.create({
			data: {
				name: options.name,
				email: options.email,
				role: options.role,
				isActive: true,
			},
		});

		// Create audit log
		await createAuditLog({
			action: "USER_CREATED",
			actorId: options.createdBy,
			targetId: user.id,
			customerEmail: user.email,
			details: { role: user.role },
		});

		Logger.info(
			EndpointPrefix.USER_CREATE,
			`User created successfully: ${user.email}`,
			{ body: { userId: user.id, role: user.role } },
		);

		return { success: true, user };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_CREATE,
			`Failed to create user: ${error}`,
			{ body: { email: options.email, role: options.role } },
		);

		return { success: false, error: "Failed to create user" };
	}
}

/**
 * Update user
 */
export async function updateUser(
	options: UpdateUserOptions,
): Promise<UpdateUserResult> {
	try {
		const existingUser = await prisma.user.findUnique({
			where: { id: options.userId },
		});

		if (!existingUser) {
			return { success: false, error: "User not found" };
		}

		const updateData: any = {};
		if (options.name !== undefined) updateData.name = options.name;
		if (options.role !== undefined) updateData.role = options.role;
		if (options.isActive !== undefined) updateData.isActive = options.isActive;

		const user = await prisma.user.update({
			where: { id: options.userId },
			data: updateData,
		});

		// Create audit log
		await createAuditLog({
			action: options.isActive === false ? "USER_DEACTIVATED" : "USER_UPDATED",
			actorId: options.updatedBy,
			targetId: user.id,
			customerEmail: user.email,
			details: updateData,
		});

		Logger.info(
			EndpointPrefix.USER_UPDATE,
			`User updated successfully: ${user.email}`,
			{ body: { userId: user.id, changes: updateData } },
		);

		return { success: true, user };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_UPDATE,
			`Failed to update user: ${error}`,
			{ body: { userId: options.userId } },
		);

		return { success: false, error: "Failed to update user" };
	}
}

// ============================================================================
// USER INVITATION MANAGEMENT
// ============================================================================

/**
 * Create a new user invitation
 */
export async function createUserInvitation(
	options: CreateUserInvitationOptions,
): Promise<CreateUserInvitationResult> {
	try {
		Logger.info(
			EndpointPrefix.USER_INVITE,
			`Creating user invitation for: ${options.email}`,
			{
				body: {
					email: options.email,
					role: options.role,
					sentBy: options.sentBy,
				},
			},
		);

		// Check if user already exists
		const existingUser = await prisma.user.findUnique({
			where: { email: options.email.toLowerCase() },
		});

		if (existingUser) {
			return {
				success: false,
				error: "User with this email already exists",
			};
		}

		// Check for existing pending invitation
		const existingInvitation = await prisma.userInvitation.findFirst({
			where: {
				email: options.email.toLowerCase(),
				status: "PENDING",
			},
		});

		if (existingInvitation) {
			return {
				success: false,
				error: "Pending invitation already exists for this email",
			};
		}

		// Generate invitation token
		const token = crypto.randomBytes(32).toString("hex");

		// Calculate expiration date
		const expiresAt = new Date();
		expiresAt.setDate(expiresAt.getDate() + (options.expiresInDays || 7));

		// Create invitation
		const invitation = await prisma.userInvitation.create({
			data: {
				email: options.email.toLowerCase(),
				role: options.role,
				token,
				status: "PENDING",
				expiresAt,
				sentBy: options.sentBy,
			},
		});

		// Create audit log
		await createAuditLog({
			action: "INVITATION_SENT",
			actorId: options.sentBy,
			customerEmail: options.email,
			details: {
				role: options.role,
				expiresAt,
			},
		});

		Logger.info(
			EndpointPrefix.USER_INVITE,
			`User invitation created: ${invitation.id}`,
			{
				body: {
					invitationId: invitation.id,
					email: options.email,
					role: options.role,
				},
			},
		);

		return {
			success: true,
			invitation,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_INVITE,
			`Failed to create user invitation: ${error}`,
			{
				body: {
					email: options.email,
					role: options.role,
				},
			},
		);

		return {
			success: false,
			error: "Failed to create user invitation",
		};
	}
}

/**
 * Accept a user invitation and create user account
 */
export async function acceptUserInvitation(
	options: AcceptInvitationOptions,
): Promise<AcceptInvitationResult> {
	try {
		Logger.info(
			EndpointPrefix.USER_CREATE,
			`Accepting user invitation with token: ${options.token}`,
		);

		// Find invitation by token
		const invitation = await prisma.userInvitation.findUnique({
			where: { token: options.token },
			include: {
				sentByUser: true,
			},
		});

		if (!invitation) {
			return {
				success: false,
				error: "Invalid invitation token",
			};
		}

		// Check invitation status
		if (invitation.status !== "PENDING") {
			return {
				success: false,
				error: "Invitation has already been processed",
			};
		}

		// Check expiration
		if (invitation.expiresAt < new Date()) {
			// Mark invitation as expired
			await prisma.userInvitation.update({
				where: { id: invitation.id },
				data: { status: "EXPIRED" },
			});

			return {
				success: false,
				error: "Invitation has expired",
			};
		}

		// Check if user already exists
		const existingUser = await prisma.user.findUnique({
			where: { email: options.userData.email.toLowerCase() },
		});

		if (existingUser) {
			return {
				success: false,
				error: "User with this email already exists",
			};
		}

		// Create user account
		const user = await prisma.user.create({
			data: {
				name: options.userData.name,
				email: options.userData.email.toLowerCase(),
				role: invitation.role,
				isActive: true,
				invitedBy: invitation.sentBy,
				invitedAt: invitation.sentAt,
			},
		});

		// Update invitation status
		await prisma.userInvitation.update({
			where: { id: invitation.id },
			data: {
				status: "ACCEPTED",
				acceptedAt: new Date(),
				acceptedBy: user.id,
			},
		});

		// Create audit logs
		await createAuditLog({
			action: "INVITATION_ACCEPTED",
			actorId: user.id,
			customerEmail: user.email,
			details: {
				invitationId: invitation.id,
				role: user.role,
			},
		});

		await createAuditLog({
			action: "USER_CREATED_FROM_INVITATION",
			actorId: user.id,
			targetId: user.id,
			details: {
				invitedBy: invitation.sentBy,
				role: user.role,
			},
		});

		Logger.info(
			EndpointPrefix.USER_CREATE,
			`User created from invitation: ${user.id}`,
			{
				body: {
					userId: user.id,
					email: user.email,
					role: user.role,
					invitationId: invitation.id,
				},
			},
		);

		return {
			success: true,
			user,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_CREATE,
			`Failed to accept user invitation: ${error}`,
			{ body: { token: options.token } },
		);

		return {
			success: false,
			error: "Failed to accept user invitation",
		};
	}
}

// ============================================================================
// AUDIT LOGGING
// ============================================================================

/**
 * Create audit log entry with new schema
 */
export async function createAuditLog(
	options: CreateAuditLogOptions,
): Promise<AuditLog | null> {
	try {
		const auditLog = await prisma.auditLog.create({
			data: {
				action: options.action as AuditAction,
				actorId: options.actorId,
				actorEmail: options.actorEmail,
				targetId: options.targetId,
				customerEmail: options.customerEmail,
				licenseKey: options.licenseKey,
				licenseId: options.licenseId,
				deviceId: options.deviceId,
				deviceHash: options.deviceHash,
				ipAddress: options.ipAddress,
				userAgent: options.userAgent,
				details: options.details,
				createdAt: new Date(),
			},
		});

		return auditLog;
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_CREATE,
			`Failed to create audit log: ${error}`,
			{ body: { action: options.action } },
		);
		return null;
	}
}

// ============================================================================
// LOOKUP FUNCTIONS
// ============================================================================

/**
 * Get user invitations with pagination (using enhanced utilities)
 */
export async function getUserInvitations(
	params: StandardPaginationParams,
): Promise<{ invitations: UserInvitation[]; total: number }> {
	try {
		// Use enhanced pagination utilities
		const { skip, take, where } = prepareDatabaseQuery(
			params,
			[], // No search fields for invitations
			{
				// Additional filters from params.filters.status
				...(params.filters?.status && { status: params.filters.status }),
			},
		);

		const [invitations, total] = await Promise.all([
			prisma.userInvitation.findMany({
				where,
				include: {
					sentByUser: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					acceptedByUser: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
				orderBy: { sentAt: "desc" }, // Override default ordering
				skip,
				take,
			}),
			prisma.userInvitation.count({ where }),
		]);

		return { invitations, total };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_LIST,
			`Failed to get user invitations: ${error}`,
			{ body: { ...params } },
		);
		return { invitations: [], total: 0 };
	}
}

/**
 * Get audit logs with pagination (using enhanced utilities)
 */
export async function getAuditLogs(
	params: StandardPaginationParams,
): Promise<{ logs: AuditLog[]; total: number }> {
	try {
		// Use enhanced pagination utilities
		const { skip, take, where } = prepareDatabaseQuery(
			params,
			[], // No search fields for audit logs
			{
				// Additional filters from params.filters
				...(params.filters?.action && { action: params.filters.action }),
				...(params.filters?.actorId && { actorId: params.filters.actorId }),
				...(params.filters?.customerEmail && {
					customerEmail: params.filters.customerEmail,
				}),
				...(params.filters?.licenseId && {
					licenseId: params.filters.licenseId,
				}),
			},
		);

		const [logs, total] = await Promise.all([
			prisma.auditLog.findMany({
				where,
				include: {
					actor: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					target: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
				orderBy: { createdAt: "desc" }, // Override default ordering
				skip,
				take,
			}),
			prisma.auditLog.count({ where }),
		]);

		return { logs, total };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_LIST,
			`Failed to get audit logs: ${error}`,
			{ body: { ...params } },
		);
		return { logs: [], total: 0 };
	}
}

// ============================================================================
// ADMIN INVITATION MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Resend invitation email (Admin)
 */
export async function resendInvitation(
	invitationId: string,
	customMessage: string | undefined,
	actorId: string,
): Promise<{ success: boolean; error?: string }> {
	try {
		const invitation = await prisma.userInvitation.findUnique({
			where: { id: invitationId },
			include: {
				sentByUser: {
					select: {
						id: true,
						name: true,
						email: true,
					},
				},
			},
		});

		if (!invitation) {
			return { success: false, error: "Invitation not found" };
		}

		if (invitation.status !== "PENDING") {
			return { success: false, error: "Can only resend pending invitations" };
		}

		if (invitation.expiresAt < new Date()) {
			return { success: false, error: "Cannot resend expired invitation" };
		}

		// Generate new token for security
		const newToken = crypto.randomBytes(32).toString("hex");

		// Update invitation with new token and reset sent date
		await prisma.userInvitation.update({
			where: { id: invitationId },
			data: {
				token: newToken,
				sentAt: new Date(),
			},
		});

		// Create audit log
		await createAuditLog({
			action: "INVITATION_RESENT",
			actorId,
			targetId: invitationId,
			customerEmail: invitation.email,
			details: {
				invitationId,
				originalSentBy: invitation.sentBy,
				customMessage,
			},
		});

		Logger.info(
			EndpointPrefix.USER_INVITE,
			`Invitation resent: ${invitationId}`,
			{
				body: {
					invitationId,
					email: invitation.email,
					actorId,
				},
			},
		);

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_INVITE,
			`Failed to resend invitation: ${error}`,
			{ body: { invitationId, actorId } },
		);
		return { success: false, error: "Failed to resend invitation" };
	}
}

/**
 * Cancel/delete invitation (Admin)
 */
export async function cancelInvitation(
	invitationId: string,
	actorId: string,
): Promise<{ success: boolean; error?: string }> {
	try {
		const invitation = await prisma.userInvitation.findUnique({
			where: { id: invitationId },
		});

		if (!invitation) {
			return { success: false, error: "Invitation not found" };
		}

		if (invitation.status !== "PENDING") {
			return { success: false, error: "Can only cancel pending invitations" };
		}

		// Update invitation status to revoked
		await prisma.userInvitation.update({
			where: { id: invitationId },
			data: {
				status: "REVOKED",
			},
		});

		// Create audit log
		await createAuditLog({
			action: "INVITATION_CANCELLED",
			actorId,
			targetId: invitationId,
			customerEmail: invitation.email,
			details: {
				invitationId,
				originalSentBy: invitation.sentBy,
				reason: "Admin cancellation",
			},
		});

		Logger.info(
			EndpointPrefix.USER_INVITE,
			`Invitation cancelled: ${invitationId}`,
			{
				body: {
					invitationId,
					email: invitation.email,
					actorId,
				},
			},
		);

		return { success: true };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_INVITE,
			`Failed to cancel invitation: ${error}`,
			{ body: { invitationId, actorId } },
		);
		return { success: false, error: "Failed to cancel invitation" };
	}
}

/**
 * Update invitation details (Admin)
 */
export async function updateInvitation(
	invitationId: string,
	updateData: {
		role?: UserRole;
		expiresAt?: string;
	},
	actorId: string,
): Promise<{ success: boolean; invitation?: any; error?: string }> {
	try {
		const existingInvitation = await prisma.userInvitation.findUnique({
			where: { id: invitationId },
		});

		if (!existingInvitation) {
			return { success: false, error: "Invitation not found" };
		}

		if (existingInvitation.status !== "PENDING") {
			return { success: false, error: "Can only update pending invitations" };
		}

		// Prepare update data
		const updatePayload: any = {};
		if (updateData.role) updatePayload.role = updateData.role;
		if (updateData.expiresAt)
			updatePayload.expiresAt = new Date(updateData.expiresAt);

		// Update invitation
		const updatedInvitation = await prisma.userInvitation.update({
			where: { id: invitationId },
			data: updatePayload,
			include: {
				sentByUser: {
					select: {
						id: true,
						name: true,
						email: true,
					},
				},
			},
		});

		// Create audit log
		await createAuditLog({
			action: "INVITATION_UPDATED",
			actorId,
			targetId: invitationId,
			customerEmail: existingInvitation.email,
			details: {
				invitationId,
				changes: updateData,
				previousData: {
					role: existingInvitation.role,
					expiresAt: existingInvitation.expiresAt,
				},
			},
		});

		Logger.info(
			EndpointPrefix.USER_INVITE,
			`Invitation updated: ${invitationId}`,
			{
				body: {
					invitationId,
					changes: updateData,
					actorId,
				},
			},
		);

		return { success: true, invitation: updatedInvitation };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_INVITE,
			`Failed to update invitation: ${error}`,
			{ body: { invitationId, updateData, actorId } },
		);
		return { success: false, error: "Failed to update invitation" };
	}
}

// ============================================================================
// AUTHENTICATION & USER MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Validate invitation token (Public)
 */
export async function validateInvitation(token: string): Promise<{
	success: boolean;
	data?: {
		valid: boolean;
		email: string;
		role: string;
		sentBy: string;
		expiresAt: Date;
	};
	error?: string;
}> {
	try {
		Logger.info(
			EndpointPrefix.AUTH_INVITATION,
			`Invitation validation request: ${token}`,
			{ body: { token } },
		);

		// Find invitation by token
		const invitation = await prisma.userInvitation.findUnique({
			where: { token },
			include: {
				sentByUser: {
					select: { name: true, email: true },
				},
			},
		});

		if (!invitation) {
			return {
				success: false,
				error: "Invalid invitation token. Please check your invitation link.",
			};
		}

		if (invitation.status !== "PENDING") {
			return {
				success: false,
				error: "This invitation has already been used or is no longer valid.",
			};
		}

		if (invitation.expiresAt < new Date()) {
			// Mark invitation as expired
			await prisma.userInvitation.update({
				where: { id: invitation.id },
				data: { status: "EXPIRED" },
			});

			return {
				success: false,
				error: "This invitation has expired. Please request a new invitation.",
			};
		}

		return {
			success: true,
			data: {
				valid: true,
				email: invitation.email,
				role: invitation.role,
				sentBy: invitation.sentByUser.name,
				expiresAt: invitation.expiresAt,
			},
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.AUTH_INVITATION,
			`Failed to validate invitation: ${error}`,
			{ body: { token } },
		);
		return {
			success: false,
			error: "Failed to validate invitation",
		};
	}
}

/**
 * Accept invitation and create user account (Public)
 * DEPRECATED: Replaced by better-auth flow
 */
export async function acceptInvitation(
	token: string,
	name: string,
): Promise<{ success: boolean; user?: any; error?: string }> {
	try {
		// Find invitation by token
		const invitation = await prisma.userInvitation.findUnique({
			where: { token },
		});

		if (!invitation) {
			return { success: false, error: "Invalid invitation token" };
		}

		if (invitation.status !== "PENDING") {
			return { success: false, error: "Invitation is no longer valid" };
		}

		if (invitation.expiresAt < new Date()) {
			return { success: false, error: "Invitation has expired" };
		}

		// Check if user already exists with this email
		const existingUser = await prisma.user.findUnique({
			where: { email: invitation.email },
		});

		if (existingUser) {
			return { success: false, error: "User already exists with this email" };
		}

		// Create user account (password will be handled by better-auth)
		const user = await prisma.user.create({
			data: {
				email: invitation.email,
				name,
				role: invitation.role,
				isActive: true,
				emailVerified: true, // Email is verified through invitation
				invitedBy: invitation.sentBy,
				invitedAt: invitation.sentAt,
			},
		});

		// Update invitation status
		await prisma.userInvitation.update({
			where: { id: invitation.id },
			data: {
				status: "ACCEPTED",
				acceptedAt: new Date(),
				acceptedBy: user.id,
			},
		});

		// Create audit log
		await createAuditLog({
			action: "USER_CREATED",
			actorId: user.id,
			targetId: user.id,
			customerEmail: user.email,
			details: {
				invitationId: invitation.id,
				role: user.role,
				createdViaInvitation: true,
			},
		});

		Logger.info(
			EndpointPrefix.AUTH_INVITATION,
			`Invitation accepted and user created: ${user.email}`,
			{
				body: {
					userId: user.id,
					email: user.email,
					role: user.role,
					invitationId: invitation.id,
				},
			},
		);

		return { success: true, user };
	} catch (error) {
		Logger.error(
			EndpointPrefix.AUTH_INVITATION,
			`Failed to accept invitation: ${error}`,
			{ body: { token } },
		);
		return { success: false, error: "Failed to accept invitation" };
	}
}

/**
 * Reactivate admin user (Admin)
 */
export async function reactivateUser(
	userId: string,
	reason: string | undefined,
	actorId: string,
): Promise<{ success: boolean; user?: any; error?: string }> {
	try {
		const existingUser = await prisma.user.findUnique({
			where: { id: userId },
		});

		if (!existingUser) {
			return { success: false, error: "User not found" };
		}

		if (existingUser.isActive) {
			return { success: false, error: "User is already active" };
		}

		// Reactivate user
		const updatedUser = await prisma.user.update({
			where: { id: userId },
			data: {
				isActive: true,
			},
		});

		// Create audit log
		await createAuditLog({
			action: "USER_REACTIVATED",
			actorId,
			targetId: userId,
			customerEmail: existingUser.email,
			details: {
				previousStatus: existingUser.isActive ? "ACTIVE" : "INACTIVE",
				reason,
				reactivatedBy: actorId,
			},
		});

		Logger.info(
			EndpointPrefix.USER_REACTIVATE,
			`User reactivated: ${existingUser.email}`,
			{
				body: {
					userId,
					email: existingUser.email,
					previousStatus: existingUser.isActive,
					actorId,
				},
			},
		);

		return { success: true, user: updatedUser };
	} catch (error) {
		Logger.error(
			EndpointPrefix.USER_REACTIVATE,
			`Failed to reactivate user: ${error}`,
			{ body: { userId, actorId } },
		);
		return { success: false, error: "Failed to reactivate user" };
	}
}
