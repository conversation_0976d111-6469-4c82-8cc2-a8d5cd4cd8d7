// Remove unused imports
import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../../../packages/shared/src/prisma";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface DashboardStats {
	totalLicenses: number;
	activeLicenses: number;
	totalRevenue: number;
	monthlyRevenue: number;
	totalDevices: number;
	activeDevices: number;
	pendingRefunds: number;
	recentGrowth: {
		licenses: number; // percentage change
		revenue: number; // percentage change
		devices: number; // percentage change
	};
}

export interface RevenueAnalytics {
	period: "monthly" | "yearly";
	data: Array<{
		period: string; // "2024-01" or "2024"
		revenue: number;
		licenses: number;
		averageValue: number;
	}>;
	totals: {
		revenue: number;
		licenses: number;
		averageValue: number;
	};
}

export interface LicenseAnalytics {
	byType: Array<{
		type: string;
		count: number;
		percentage: number;
	}>;
	byStatus: Array<{
		status: string;
		count: number;
		percentage: number;
	}>;
	growth: Array<{
		date: string;
		count: number;
		cumulative: number;
	}>;
}

export interface DeviceAnalytics {
	registrations: Array<{
		date: string;
		count: number;
		cumulative: number;
	}>;
	activeDevices: {
		total: number;
		byLicenseType: Array<{
			type: string;
			count: number;
		}>;
	};
	averageDevicesPerLicense: number;
}

// ============================================================================
// DASHBOARD ANALYTICS FUNCTIONS
// ============================================================================

/**
 * Get dashboard overview statistics
 */
export async function getDashboardStats(
	period: "7d" | "30d" | "90d" | "1y" = "30d",
): Promise<DashboardStats> {
	try {
		const now = new Date();
		const periodStart = new Date();

		// Calculate period start date
		switch (period) {
			case "7d":
				periodStart.setDate(now.getDate() - 7);
				break;
			case "30d":
				periodStart.setDate(now.getDate() - 30);
				break;
			case "90d":
				periodStart.setDate(now.getDate() - 90);
				break;
			case "1y":
				periodStart.setFullYear(now.getFullYear() - 1);
				break;
		}

		// Get current totals
		const [
			totalLicenses,
			activeLicenses,
			totalRevenue,
			monthlyRevenue,
			totalDevices,
			activeDevices,
			pendingRefunds,
		] = await Promise.all([
			prisma.license.count(),
			prisma.license.count({ where: { status: "ACTIVE" } }),
			prisma.paymentIntent.aggregate({
				_sum: { amount: true },
				where: { status: "SUCCEEDED" },
			}),
			prisma.paymentIntent.aggregate({
				_sum: { amount: true },
				where: {
					status: "SUCCEEDED",
					createdAt: { gte: new Date(now.getFullYear(), now.getMonth(), 1) },
				},
			}),
			prisma.device.count(),
			prisma.device.count({ where: { status: "ACTIVE" } }),
			prisma.refundRequest.count({ where: { status: "PENDING" } }),
		]);

		// Get previous period data for growth calculation
		const previousPeriodStart = new Date(periodStart);
		previousPeriodStart.setTime(
			previousPeriodStart.getTime() - (now.getTime() - periodStart.getTime()),
		);

		const [prevLicenses, prevRevenue, prevDevices] = await Promise.all([
			prisma.license.count({
				where: {
					createdAt: {
						gte: previousPeriodStart,
						lt: periodStart,
					},
				},
			}),
			prisma.paymentIntent.aggregate({
				_sum: { amount: true },
				where: {
					status: "SUCCEEDED",
					createdAt: {
						gte: previousPeriodStart,
						lt: periodStart,
					},
				},
			}),
			prisma.device.count({
				where: {
					firstSeen: {
						gte: previousPeriodStart,
						lt: periodStart,
					},
				},
			}),
		]);

		// Calculate current period data
		const [currentLicenses, currentRevenue, currentDevices] = await Promise.all(
			[
				prisma.license.count({
					where: {
						createdAt: { gte: periodStart },
					},
				}),
				prisma.paymentIntent.aggregate({
					_sum: { amount: true },
					where: {
						status: "SUCCEEDED",
						createdAt: { gte: periodStart },
					},
				}),
				prisma.device.count({
					where: {
						firstSeen: { gte: periodStart },
					},
				}),
			],
		);

		// Calculate growth percentages
		const licenseGrowth =
			prevLicenses > 0
				? ((currentLicenses - prevLicenses) / prevLicenses) * 100
				: 0;
		const revenueGrowth =
			(prevRevenue._sum.amount || 0) > 0
				? (((currentRevenue._sum.amount || 0) -
						(prevRevenue._sum.amount || 0)) /
						(prevRevenue._sum.amount || 0)) *
					100
				: 0;
		const deviceGrowth =
			prevDevices > 0
				? ((currentDevices - prevDevices) / prevDevices) * 100
				: 0;

		Logger.info(
			EndpointPrefix.ANALYTICS_DASHBOARD,
			`Dashboard stats retrieved for period: ${period}`,
			{
				body: {
					period,
					totalLicenses,
					activeLicenses,
					totalRevenue: totalRevenue._sum.amount || 0,
				},
			},
		);

		return {
			totalLicenses,
			activeLicenses,
			totalRevenue: totalRevenue._sum.amount || 0,
			monthlyRevenue: monthlyRevenue._sum.amount || 0,
			totalDevices,
			activeDevices,
			pendingRefunds,
			recentGrowth: {
				licenses: Math.round(licenseGrowth * 100) / 100,
				revenue: Math.round(revenueGrowth * 100) / 100,
				devices: Math.round(deviceGrowth * 100) / 100,
			},
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.ANALYTICS_DASHBOARD,
			`Failed to get dashboard stats: ${error}`,
			{ body: { period } },
		);
		throw error;
	}
}

/**
 * Get revenue analytics (monthly/yearly)
 */
export async function getRevenueAnalytics(
	period: "monthly" | "yearly" = "monthly",
	year?: number,
): Promise<RevenueAnalytics> {
	try {
		const currentYear = year || new Date().getFullYear();
		let startDate: Date;
		let endDate: Date;

		if (period === "monthly") {
			startDate = new Date(currentYear, 0, 1); // January 1st
			endDate = new Date(currentYear + 1, 0, 1); // January 1st next year
		} else {
			startDate = new Date(currentYear - 4, 0, 1); // 5 years of data
			endDate = new Date(currentYear + 1, 0, 1);
		}

		// Get payment data grouped by period
		const payments = await prisma.paymentIntent.findMany({
			where: {
				status: "SUCCEEDED",
				createdAt: {
					gte: startDate,
					lt: endDate,
				},
			},
			select: {
				amount: true,
				createdAt: true,
			},
			orderBy: {
				createdAt: "asc",
			},
		});

		// Group payments by period
		const groupedData = new Map<
			string,
			{ revenue: number; licenses: number }
		>();

		for (const payment of payments) {
			const periodKey =
				period === "monthly"
					? `${payment.createdAt.getFullYear()}-${String(payment.createdAt.getMonth() + 1).padStart(2, "0")}`
					: payment.createdAt.getFullYear().toString();

			const existing = groupedData.get(periodKey) || {
				revenue: 0,
				licenses: 0,
			};
			existing.revenue += payment.amount;
			existing.licenses += 1;
			groupedData.set(periodKey, existing);
		}

		// Convert to array format
		const data = Array.from(groupedData.entries()).map(([period, stats]) => ({
			period,
			revenue: stats.revenue,
			licenses: stats.licenses,
			averageValue:
				stats.licenses > 0 ? Math.round(stats.revenue / stats.licenses) : 0,
		}));

		// Calculate totals
		const totals = data.reduce(
			(acc, item) => ({
				revenue: acc.revenue + item.revenue,
				licenses: acc.licenses + item.licenses,
				averageValue: 0, // Will be calculated below
			}),
			{ revenue: 0, licenses: 0, averageValue: 0 },
		);

		totals.averageValue =
			totals.licenses > 0 ? Math.round(totals.revenue / totals.licenses) : 0;

		Logger.info(
			EndpointPrefix.ANALYTICS_REVENUE,
			`Revenue analytics retrieved for period: ${period}`,
			{
				body: {
					period,
					year: currentYear,
					totalRevenue: totals.revenue,
					totalLicenses: totals.licenses,
				},
			},
		);

		return {
			period,
			data,
			totals,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.ANALYTICS_REVENUE,
			`Failed to get revenue analytics: ${error}`,
			{ body: { period, year } },
		);
		throw error;
	}
}

/**
 * Get license analytics (growth, types, etc.)
 */
export async function getLicenseAnalytics(
	period: "7d" | "30d" | "90d" | "1y" = "30d",
	groupBy: "type" | "status" | "date" = "type",
): Promise<LicenseAnalytics> {
	try {
		const now = new Date();
		const periodStart = new Date();

		// Calculate period start date
		switch (period) {
			case "7d":
				periodStart.setDate(now.getDate() - 7);
				break;
			case "30d":
				periodStart.setDate(now.getDate() - 30);
				break;
			case "90d":
				periodStart.setDate(now.getDate() - 90);
				break;
			case "1y":
				periodStart.setFullYear(now.getFullYear() - 1);
				break;
		}

		// Get license data
		const [licenses, totalCount] = await Promise.all([
			prisma.license.findMany({
				where: {
					createdAt: { gte: periodStart },
				},
				select: {
					licenseType: true,
					status: true,
					createdAt: true,
				},
				orderBy: {
					createdAt: "asc",
				},
			}),
			prisma.license.count(),
		]);

		// Group by type
		const typeGroups = new Map<string, number>();
		const statusGroups = new Map<string, number>();
		const dateGroups = new Map<string, number>();

		for (const license of licenses) {
			// By type
			const typeCount = typeGroups.get(license.licenseType) || 0;
			typeGroups.set(license.licenseType, typeCount + 1);

			// By status
			const statusCount = statusGroups.get(license.status) || 0;
			statusGroups.set(license.status, statusCount + 1);

			// By date (daily)
			const dateKey = license.createdAt.toISOString().split("T")[0];
			const dateCount = dateGroups.get(dateKey) || 0;
			dateGroups.set(dateKey, dateCount + 1);
		}

		// Convert to arrays with percentages
		const byType = Array.from(typeGroups.entries()).map(([type, count]) => ({
			type,
			count,
			percentage:
				totalCount > 0 ? Math.round((count / totalCount) * 100 * 100) / 100 : 0,
		}));

		const byStatus = Array.from(statusGroups.entries()).map(
			([status, count]) => ({
				status,
				count,
				percentage:
					totalCount > 0
						? Math.round((count / totalCount) * 100 * 100) / 100
						: 0,
			}),
		);

		// Generate growth data (cumulative)
		const growth: Array<{ date: string; count: number; cumulative: number }> =
			[];
		let cumulative = 0;

		const sortedDates = Array.from(dateGroups.entries()).sort(([a], [b]) =>
			a.localeCompare(b),
		);
		for (const [date, count] of sortedDates) {
			cumulative += count;
			growth.push({ date, count, cumulative });
		}

		Logger.info(
			EndpointPrefix.ANALYTICS_LICENSES,
			`License analytics retrieved for period: ${period}`,
			{
				body: {
					period,
					groupBy,
					totalLicenses: licenses.length,
					typeGroups: byType.length,
				},
			},
		);

		return {
			byType,
			byStatus,
			growth,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.ANALYTICS_LICENSES,
			`Failed to get license analytics: ${error}`,
			{ body: { period, groupBy } },
		);
		throw error;
	}
}

/**
 * Get device analytics (registrations, active devices)
 */
export async function getDeviceAnalytics(
	period: "7d" | "30d" | "90d" | "1y" = "30d",
	groupBy: "registrations" | "active" | "date" = "registrations",
): Promise<DeviceAnalytics> {
	try {
		const now = new Date();
		const periodStart = new Date();

		// Calculate period start date
		switch (period) {
			case "7d":
				periodStart.setDate(now.getDate() - 7);
				break;
			case "30d":
				periodStart.setDate(now.getDate() - 30);
				break;
			case "90d":
				periodStart.setDate(now.getDate() - 90);
				break;
			case "1y":
				periodStart.setFullYear(now.getFullYear() - 1);
				break;
		}

		// Get device registration data
		const devices = await prisma.device.findMany({
			where: {
				firstSeen: { gte: periodStart },
			},
			select: {
				firstSeen: true,
				status: true,
				license: {
					select: {
						licenseType: true,
					},
				},
			},
			orderBy: {
				firstSeen: "asc",
			},
		});

		// Get active devices by license type
		const activeDevicesByType = await prisma.device.groupBy({
			by: ["licenseId"],
			where: {
				status: "ACTIVE",
			},
			_count: {
				id: true,
			},
		});

		// Get license types for active devices
		const licenseTypes = await prisma.license.findMany({
			where: {
				id: {
					in: activeDevicesByType.map((d) => d.licenseId),
				},
			},
			select: {
				id: true,
				licenseType: true,
			},
		});

		// Group registrations by date
		const registrationGroups = new Map<string, number>();
		let cumulative = 0;

		for (const device of devices) {
			const dateKey = device.firstSeen.toISOString().split("T")[0];
			const count = registrationGroups.get(dateKey) || 0;
			registrationGroups.set(dateKey, count + 1);
		}

		// Generate registration data
		const registrations = Array.from(registrationGroups.entries())
			.sort(([a], [b]) => a.localeCompare(b))
			.map(([date, count]) => {
				cumulative += count;
				return { date, count, cumulative };
			});

		// Group active devices by license type
		const typeGroups = new Map<string, number>();
		for (const activeDevice of activeDevicesByType) {
			const license = licenseTypes.find((l) => l.id === activeDevice.licenseId);
			if (license) {
				const count = typeGroups.get(license.licenseType) || 0;
				typeGroups.set(license.licenseType, count + activeDevice._count.id);
			}
		}

		const byLicenseType = Array.from(typeGroups.entries()).map(
			([type, count]) => ({
				type,
				count,
			}),
		);

		// Calculate average devices per license
		const totalLicenses = await prisma.license.count({
			where: { status: "ACTIVE" },
		});
		const totalActiveDevices = await prisma.device.count({
			where: { status: "ACTIVE" },
		});
		const averageDevicesPerLicense =
			totalLicenses > 0
				? Math.round((totalActiveDevices / totalLicenses) * 100) / 100
				: 0;

		Logger.info(
			EndpointPrefix.ANALYTICS_DEVICES,
			`Device analytics retrieved for period: ${period}`,
			{
				body: {
					period,
					groupBy,
					totalRegistrations: devices.length,
					totalActiveDevices,
					averageDevicesPerLicense,
				},
			},
		);

		return {
			registrations,
			activeDevices: {
				total: totalActiveDevices,
				byLicenseType,
			},
			averageDevicesPerLicense,
		};
	} catch (error) {
		Logger.error(
			EndpointPrefix.ANALYTICS_DEVICES,
			`Failed to get device analytics: ${error}`,
			{ body: { period, groupBy } },
		);
		throw error;
	}
}
