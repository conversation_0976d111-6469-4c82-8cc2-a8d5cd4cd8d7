import { EndpointPrefix, Logger } from "@/utils/logger";
import prisma from "../../../../packages/shared/src/prisma";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface ReportFilters {
	startDate?: Date;
	endDate?: Date;
	status?: string;
	licenseType?: string;
	[key: string]: any;
}

export interface ExportOptions {
	format: "csv" | "json";
	filters?: ReportFilters;
}

// ============================================================================
// CSV UTILITY FUNCTIONS
// ============================================================================

/**
 * Convert array of objects to CSV string
 */
function arrayToCSV(data: any[]): string {
	if (data.length === 0) return "";

	const headers = Object.keys(data[0]);
	const csvRows = [
		headers.join(","), // Header row
		...data.map((row) =>
			headers
				.map((header) => {
					const value = row[header];
					// Escape commas and quotes in CSV
					if (
						typeof value === "string" &&
						(value.includes(",") || value.includes('"'))
					) {
						return `"${value.replace(/"/g, '""')}"`;
					}
					return value;
				})
				.join(","),
		),
	];

	return csvRows.join("\n");
}

/**
 * Format date for CSV export
 */
function formatDateForCSV(date: Date): string {
	return date.toISOString().split("T")[0];
}

// ============================================================================
// REPORT EXPORT FUNCTIONS
// ============================================================================

/**
 * Generate license report (CSV export)
 */
export async function generateLicenseReport(
	options: ExportOptions = { format: "csv" },
): Promise<{ data: string; filename: string; contentType: string }> {
	try {
		const { filters = {} } = options;

		// Build where clause
		const where: any = {};
		if (filters.startDate) where.createdAt = { gte: filters.startDate };
		if (filters.endDate) {
			where.createdAt = { ...where.createdAt, lte: filters.endDate };
		}
		if (filters.status) where.status = filters.status;
		if (filters.licenseType) where.licenseType = filters.licenseType;

		// Get license data
		const licenses = await prisma.license.findMany({
			where,
			include: {
				devices: {
					select: {
						id: true,
						status: true,
						firstSeen: true,
					},
				},
				paymentIntent: {
					select: {
						amount: true,
						status: true,
						createdAt: true,
					},
				},
			},
			orderBy: {
				createdAt: "desc",
			},
		});

		// Transform data for export
		const exportData = licenses.map((license) => ({
			licenseKey: license.licenseKey,
			customerEmail: license.customerEmail,
			customerName: license.customerName || "",
			licenseType: license.licenseType,
			status: license.status,
			maxDevices: license.maxDevices,
			usedDevices: license.usedDevices,
			activeDevices: license.devices.filter((d) => d.status === "ACTIVE")
				.length,
			totalPaidAmount: license.totalPaidAmount || 0,
			createdAt: formatDateForCSV(license.createdAt),
			activatedAt: license.activatedAt
				? formatDateForCSV(license.activatedAt)
				: "",
			expiresAt: license.expiresAt ? formatDateForCSV(license.expiresAt) : "",
			// lastValidatedAt field doesn't exist in current schema
		}));

		const timestamp = new Date().toISOString().split("T")[0];
		const filename = `license-report-${timestamp}.${options.format}`;

		let data: string;
		let contentType: string;

		if (options.format === "csv") {
			data = arrayToCSV(exportData);
			contentType = "text/csv";
		} else {
			data = JSON.stringify(exportData, null, 2);
			contentType = "application/json";
		}

		Logger.info(
			EndpointPrefix.REPORTS_EXPORT,
			`License report generated: ${licenses.length} records`,
			{
				body: {
					format: options.format,
					recordCount: licenses.length,
					filters,
				},
			},
		);

		return { data, filename, contentType };
	} catch (error) {
		Logger.error(
			EndpointPrefix.REPORTS_EXPORT,
			`Failed to generate license report: ${error}`,
			{ body: { options } },
		);
		throw error;
	}
}

/**
 * Generate revenue report (CSV export)
 */
export async function generateRevenueReport(
	options: ExportOptions = { format: "csv" },
): Promise<{ data: string; filename: string; contentType: string }> {
	try {
		const { filters = {} } = options;

		// Build where clause
		const where: any = { status: "SUCCEEDED" };
		if (filters.startDate) where.createdAt = { gte: filters.startDate };
		if (filters.endDate) {
			where.createdAt = { ...where.createdAt, lte: filters.endDate };
		}

		// Get payment data
		const payments = await prisma.paymentIntent.findMany({
			where,
			orderBy: {
				createdAt: "desc",
			},
		});

		// Get associated licenses for each payment
		const paymentIds = payments.map((p) => p.id);
		const licenses = await prisma.license.findMany({
			where: {
				paymentIntentId: { in: paymentIds },
			},
			select: {
				paymentIntentId: true,
				licenseKey: true,
				licenseType: true,
			},
		});

		// Create a map for quick lookup
		const licenseMap = new Map(licenses.map((l) => [l.paymentIntentId, l]));

		// Transform data for export
		const exportData = payments.map((payment) => {
			const license = licenseMap.get(payment.id);
			return {
				paymentIntentId: payment.id,
				licenseKey: license?.licenseKey || "",
				customerEmail: payment.customerEmail,
				customerName: payment.customerName || "",
				licenseType: license?.licenseType || "",
				amount: payment.amount,
				currency: payment.currency,
				status: payment.status,
				paymentType: payment.paymentType,
				createdAt: formatDateForCSV(payment.createdAt),
				processedAt: payment.processedAt
					? formatDateForCSV(payment.processedAt)
					: "",
			};
		});

		const timestamp = new Date().toISOString().split("T")[0];
		const filename = `revenue-report-${timestamp}.${options.format}`;

		let data: string;
		let contentType: string;

		if (options.format === "csv") {
			data = arrayToCSV(exportData);
			contentType = "text/csv";
		} else {
			data = JSON.stringify(exportData, null, 2);
			contentType = "application/json";
		}

		Logger.info(
			EndpointPrefix.REPORTS_EXPORT,
			`Revenue report generated: ${payments.length} records`,
			{
				body: {
					format: options.format,
					recordCount: payments.length,
					totalRevenue: payments.reduce((sum, p) => sum + p.amount, 0),
					filters,
				},
			},
		);

		return { data, filename, contentType };
	} catch (error) {
		Logger.error(
			EndpointPrefix.REPORTS_EXPORT,
			`Failed to generate revenue report: ${error}`,
			{ body: { options } },
		);
		throw error;
	}
}

/**
 * Generate device usage report
 */
export async function generateDeviceReport(
	options: ExportOptions = { format: "csv" },
): Promise<{ data: string; filename: string; contentType: string }> {
	try {
		const { filters = {} } = options;

		// Build where clause
		const where: any = {};
		if (filters.startDate) where.firstSeen = { gte: filters.startDate };
		if (filters.endDate) {
			where.firstSeen = { ...where.firstSeen, lte: filters.endDate };
		}
		if (filters.status) where.status = filters.status;

		// Get device data
		const devices = await prisma.device.findMany({
			where,
			include: {
				license: {
					select: {
						licenseKey: true,
						customerEmail: true,
						customerName: true,
						licenseType: true,
					},
				},
			},
			orderBy: {
				firstSeen: "desc",
			},
		});

		// Transform data for export
		const exportData = devices.map((device) => ({
			deviceId: device.id,
			licenseKey: device.license.licenseKey,
			customerEmail: device.license.customerEmail,
			customerName: device.license.customerName || "",
			licenseType: device.license.licenseType,
			deviceName: device.deviceName || "",
			deviceType: device.deviceType || "",
			deviceModel: device.deviceModel || "",
			operatingSystem: device.operatingSystem || "",
			status: device.status,
			firstSeen: formatDateForCSV(device.firstSeen),
			lastSeen: formatDateForCSV(device.lastSeen),
			appVersion: device.appVersion || "",
			userNickname: device.userNickname || "",
			location: device.location || "",
		}));

		const timestamp = new Date().toISOString().split("T")[0];
		const filename = `device-report-${timestamp}.${options.format}`;

		let data: string;
		let contentType: string;

		if (options.format === "csv") {
			data = arrayToCSV(exportData);
			contentType = "text/csv";
		} else {
			data = JSON.stringify(exportData, null, 2);
			contentType = "application/json";
		}

		Logger.info(
			EndpointPrefix.REPORTS_EXPORT,
			`Device report generated: ${devices.length} records`,
			{
				body: {
					format: options.format,
					recordCount: devices.length,
					filters,
				},
			},
		);

		return { data, filename, contentType };
	} catch (error) {
		Logger.error(
			EndpointPrefix.REPORTS_EXPORT,
			`Failed to generate device report: ${error}`,
			{ body: { options } },
		);
		throw error;
	}
}

/**
 * Generate refunds report
 */
export async function generateRefundReport(
	options: ExportOptions = { format: "csv" },
): Promise<{ data: string; filename: string; contentType: string }> {
	try {
		const { filters = {} } = options;

		// Build where clause
		const where: any = {};
		if (filters.startDate) where.createdAt = { gte: filters.startDate };
		if (filters.endDate) {
			where.createdAt = { ...where.createdAt, lte: filters.endDate };
		}
		if (filters.status) where.status = filters.status;

		// Get refund data
		const refunds = await prisma.refundRequest.findMany({
			where,
			orderBy: {
				createdAt: "desc",
			},
		});

		// Get associated licenses for each refund
		const licenseIds = refunds.map((r) => r.licenseId);
		const licenses = await prisma.license.findMany({
			where: {
				id: { in: licenseIds },
			},
			select: {
				id: true,
				licenseKey: true,
				customerEmail: true,
				customerName: true,
				licenseType: true,
				paymentIntentId: true,
			},
		});

		// Get payment intents for original amounts
		const paymentIntentIds = licenses
			.map((l) => l.paymentIntentId)
			.filter(Boolean) as string[];

		const paymentIntents = await prisma.paymentIntent.findMany({
			where: {
				id: { in: paymentIntentIds },
			},
			select: {
				id: true,
				amount: true,
				currency: true,
			},
		});

		// Create maps for quick lookup
		const licenseMap = new Map(licenses.map((l) => [l.id, l]));
		const paymentMap = new Map(paymentIntents.map((p) => [p.id, p]));

		// Transform data for export
		const exportData = refunds.map((refund) => {
			const license = licenseMap.get(refund.licenseId);
			const payment = license?.paymentIntentId
				? paymentMap.get(license.paymentIntentId)
				: null;

			return {
				refundId: refund.id,
				licenseKey: license?.licenseKey || "",
				customerEmail: license?.customerEmail || "",
				customerName: license?.customerName || "",
				licenseType: license?.licenseType || "",
				requestedBy: refund.requestedBy,
				reason: refund.reason,
				status: refund.status,
				originalAmount: payment?.amount || 0,
				requestedAmount: refund.requestedAmount || 0,
				approvedAmount: refund.approvedAmount || 0,
				currency: payment?.currency || "USD",
				createdAt: formatDateForCSV(refund.createdAt),
				processedAt: refund.processedAt
					? formatDateForCSV(refund.processedAt)
					: "",
				adminNotes: refund.adminNotes || "",
			};
		});

		const timestamp = new Date().toISOString().split("T")[0];
		const filename = `refund-report-${timestamp}.${options.format}`;

		let data: string;
		let contentType: string;

		if (options.format === "csv") {
			data = arrayToCSV(exportData);
			contentType = "text/csv";
		} else {
			data = JSON.stringify(exportData, null, 2);
			contentType = "application/json";
		}

		Logger.info(
			EndpointPrefix.REPORTS_EXPORT,
			`Refund report generated: ${refunds.length} records`,
			{
				body: {
					format: options.format,
					recordCount: refunds.length,
					totalRefundAmount: refunds.reduce(
						(sum, r) => sum + (r.approvedAmount || 0),
						0,
					),
					filters,
				},
			},
		);

		return { data, filename, contentType };
	} catch (error) {
		Logger.error(
			EndpointPrefix.REPORTS_EXPORT,
			`Failed to generate refund report: ${error}`,
			{ body: { options } },
		);
		throw error;
	}
}
