/**
 * Controllers for the new normalized database schema
 *
 * These controllers work with the updated schema structure:
 * - PaymentIntent model for payment tracking
 * - WebhookEvent model for webhook processing
 * - DeviceExpansion model for device expansions
 * - Updated License, Device, AuditLog, and UserInvitation models
 *
 * The controllers follow the same architectural patterns as the existing services
 * but are designed to work with the new normalized schema structure.
 */

export type {
	DeviceExpansionOptions,
	DeviceExpansionResult,
	// Device types
	DeviceRegistrationOptions,
	DeviceRegistrationResult,
} from "./device.controller";
// Device management
export * from "./device.controller";
// Re-export types for convenience
export type {
	CreateLicenseFromWebhookOptions,
	// License types
	CreateLicenseOptions,
	CreateLicenseResult,
	ValidateLicenseOptions,
	ValidateLicenseResult,
} from "./license.controller";
// License management
export * from "./license.controller";
export type {
	// Payment types
	CreatePaymentIntentOptions,
	ProcessWebhookOptions,
	ProcessWebhookResult,
} from "./payment.controller";
// Payment processing
export * from "./payment.controller";
// Refund management
export * from "./refund.controller";
export type {
	AcceptInvitationOptions,
	AcceptInvitationResult,
	CreateAuditLogOptions,
	// User types
	CreateUserInvitationOptions,
	CreateUserInvitationResult,
} from "./user.controller";
// User management
export * from "./user.controller";
