import {
	refundListSchema,
	requestRefundSchema,
} from "@snapback/shared/schemas";
import { Router } from "express";
import { getRefundRequests } from "@/controllers/refund.controller";
import { createAuditLog } from "@/controllers/user.controller";
import { requireAuth, requireRole } from "@/middleware/auth";
import type { ApiResponse } from "@/types/api";
import { handleError } from "@/utils/errors";
import { EndpointPrefix, Logger } from "@/utils/logger";
import { paginate } from "@/utils/pagination";
import prisma from "../../../../packages/shared/src/prisma";

const router: Router = Router();

// ============================================================================
// PUBLIC REFUND ROUTES (for Customers)
// ============================================================================

/**
 * POST /api/refunds/request
 * Request refund (public, requires license key)
 */
router.post("/request", async (req, res) => {
	try {
		const validatedData = requestRefundSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.REFUND_REQUEST,
			`Refund request: ${validatedData.licenseKey}`,
			{
				body: {
					licenseKey: validatedData.licenseKey,
					reason: validatedData.reason,
				},
			},
		);

		// Find license by key
		const license = await prisma.license.findUnique({
			where: { licenseKey: validatedData.licenseKey },
			include: {
				paymentIntent: true,
				refundRequests: {
					where: {
						status: {
							in: ["PENDING", "APPROVED"],
						},
					},
				},
			},
		});

		if (!license) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License not found",
				error: {
					code: "LICENSE_NOT_FOUND",
					message: "License not found",
				},
			};
			return res.status(404).json(response);
		}

		// Check if license is already refunded
		if (license.status === "REFUNDED") {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License has already been refunded",
				error: {
					code: "ALREADY_REFUNDED",
					message: "License has already been refunded",
				},
			};
			return res.status(400).json(response);
		}

		// Check for existing pending refund requests
		if (license.refundRequests.length > 0) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "A refund request already exists for this license",
				error: {
					code: "REFUND_REQUEST_EXISTS",
					message: "A refund request already exists for this license",
				},
			};
			return res.status(400).json(response);
		}

		// Determine refund amount (default to total paid amount)
		const requestedAmount =
			validatedData.requestedAmount || license.totalPaidAmount;

		// Create refund request
		const refundRequest = await prisma.refundRequest.create({
			data: {
				licenseId: license.id,
				requestedBy: license.customerEmail,
				reason: validatedData.reason,
				status: "PENDING",
				requestedAmount,
			},
		});

		// Create audit log
		await createAuditLog({
			action: "REFUND_REQUESTED",
			customerEmail: license.customerEmail,
			licenseId: license.id,
			licenseKey: license.licenseKey,
			details: {
				refundRequestId: refundRequest.id,
				reason: validatedData.reason,
				requestedAmount,
			},
		});

		const response: ApiResponse<{
			requestId: string;
			status: string;
			requestedAmount: number;
		}> = {
			success: true,
			data: {
				requestId: refundRequest.id,
				status: refundRequest.status,
				requestedAmount: refundRequest.requestedAmount || 0,
			},
			message: "Refund request submitted successfully",
		};

		res.status(201).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.REFUND_REQUEST);
	}
});

/**
 * GET /api/refunds/status/:requestId
 * Check refund status
 */
router.get("/status/:requestId", async (req, res) => {
	try {
		const { requestId } = req.params;

		Logger.info(
			EndpointPrefix.REFUND_STATUS,
			`Refund status request: ${requestId}`,
			{ body: { requestId } },
		);

		const refundRequest = await prisma.refundRequest.findUnique({
			where: { id: requestId },
			include: {
				license: {
					select: {
						licenseKey: true,
						customerEmail: true,
					},
				},
			},
		});

		if (!refundRequest) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "Refund request not found",
				error: {
					code: "REFUND_REQUEST_NOT_FOUND",
					message: "Refund request not found",
				},
			};
			return res.status(404).json(response);
		}

		const response: ApiResponse<{
			requestId: string;
			status: string;
			requestedAmount: number | null;
			approvedAmount: number | null;
			reason: string;
			createdAt: Date;
			processedAt: Date | null;
		}> = {
			success: true,
			data: {
				requestId: refundRequest.id,
				status: refundRequest.status,
				requestedAmount: refundRequest.requestedAmount,
				approvedAmount: refundRequest.approvedAmount,
				reason: refundRequest.reason,
				createdAt: refundRequest.createdAt,
				processedAt: refundRequest.processedAt,
			},
			message: "Refund status retrieved",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.REFUND_STATUS);
	}
});

// ============================================================================
// ADMIN REFUND ROUTES
// ============================================================================

/**
 * GET /api/admin/refunds
 * List all refund requests (with filters)
 */
router.get(
	"/admin",
	requireAuth,
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { page, limit, search, sortBy, sortOrder, filters } =
				refundListSchema.parse(req.query);

			Logger.info(
				EndpointPrefix.REFUND_LIST,
				`Admin refund list request - Page: ${page}, Limit: ${limit}`,
				{ body: { page, limit, search, sortBy, sortOrder, filters } },
			);

			const refundParams = {
				page,
				limit,
				search,
				sortBy,
				sortOrder,
				filters,
			};

			const { refundRequests, total } = await getRefundRequests(refundParams);

			const pagination = paginate(total, page, limit);

			const response: ApiResponse<{
				refundRequests: typeof refundRequests;
				pagination: typeof pagination;
			}> = {
				success: true,
				data: {
					refundRequests,
					pagination,
				},
				message: `Retrieved ${refundRequests.length} refund requests`,
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.REFUND_LIST);
		}
	},
);

/**
 * GET /api/admin/refunds/:id
 * Get specific refund request
 */
router.get(
	"/admin/:id",
	requireAuth,
	requireRole("ADMIN", "MANAGER", "SUPER_ADMIN"),
	async (req, res) => {
		try {
			const { id } = req.params;

			Logger.info(
				EndpointPrefix.REFUND_STATUS,
				`Admin refund detail request: ${id}`,
				{ body: { refundRequestId: id } },
			);

			const refundRequest = await prisma.refundRequest.findUnique({
				where: { id },
				include: {
					license: {
						include: {
							paymentIntent: true,
							devices: {
								where: { status: "ACTIVE" },
							},
						},
					},
					processedByUser: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
			});

			if (!refundRequest) {
				const response: ApiResponse<null> = {
					success: false,
					data: null,
					message: "Refund request not found",
					error: {
						code: "REFUND_REQUEST_NOT_FOUND",
						message: "Refund request not found",
					},
				};
				return res.status(404).json(response);
			}

			const response: ApiResponse<typeof refundRequest> = {
				success: true,
				data: refundRequest,
				message: "Refund request details retrieved",
			};

			res.status(200).json(response);
		} catch (error) {
			handleError(res, error, EndpointPrefix.REFUND_STATUS);
		}
	},
);

export default router;
