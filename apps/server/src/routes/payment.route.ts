import {
	createCheckoutSessionSchema,
	createExpansionCheckoutSchema,
	createTierUpgradeSchema,
} from "@snapback/shared/schemas";
import { Router } from "express";
import {
	getLicenseByEmail,
	getLicenseById,
} from "@/controllers/license.controller";
import {
	createPaymentIntent,
	getPaymentIntentByStripeId,
} from "@/controllers/payment.controller";
import { stripe } from "@/lib/stripe";
import { requireAuth } from "@/middleware/auth";
import type { ApiResponse } from "@/types/api";
import { handleError } from "@/utils/errors";
import { EndpointPrefix, Logger } from "@/utils/logger";

const router: Router = Router();

// ============================================================================
// STRIPE CHECKOUT ROUTES
// ============================================================================

/**
 * POST /api/payments/create-checkout
 * Create Stripe checkout session for license purchase
 */
router.post("/create-checkout", async (req, res) => {
	try {
		const validatedData = createCheckoutSessionSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.PAYMENT_CREATE_INTENT,
			`Creating checkout session for license purchase: ${validatedData.customerEmail}`,
			{
				body: {
					licenseType: validatedData.licenseType,
					customerEmail: validatedData.customerEmail,
				},
			},
		);

		// Check if customer already has a license
		const existingLicense = await getLicenseByEmail(
			validatedData.customerEmail,
		);

		if (existingLicense) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "Customer already has an active license",
				error: {
					code: "LICENSE_EXISTS",
					message: "A license already exists for this email address",
				},
			};
			return res.status(400).json(response);
		}

		// Determine pricing based on license type
		const pricing = {
			TRIAL: { amount: 0, name: "SnapBack Free Trial" },
			PRO: { amount: 499, name: "SnapBack Pro License" }, // $4.99
			ENTERPRISE: { amount: 999, name: "SnapBack Enterprise License" }, // $9.99
		};

		const priceInfo = pricing[validatedData.licenseType];

		// Create Stripe checkout session
		const session = await stripe.checkout.sessions.create({
			payment_method_types: ["card"],
			line_items: [
				{
					price_data: {
						currency: "usd",
						product_data: {
							name: priceInfo.name,
							description: `${validatedData.licenseType} license for SnapBack app`,
						},
						unit_amount: priceInfo.amount,
					},
					quantity: 1,
				},
			],
			mode: "payment",
			customer_email: validatedData.customerEmail,
			success_url: validatedData.successUrl,
			cancel_url: validatedData.cancelUrl,
			metadata: {
				licenseType: validatedData.licenseType,
				customerEmail: validatedData.customerEmail,
				customerName: validatedData.customerName || "",
			},
		});

		// Create PaymentIntent record
		if (session.payment_intent) {
			await createPaymentIntent({
				stripePaymentIntentId: session.payment_intent as string,
				stripeCheckoutSessionId: session.id,
				amount: priceInfo.amount,
				currency: "usd",
				paymentType: "LICENSE_PURCHASE",
				customerEmail: validatedData.customerEmail,
				customerName: validatedData.customerName,
			});
		}

		const response: ApiResponse<{
			sessionId: string;
			url: string;
		}> = {
			success: true,
			data: {
				sessionId: session.id,
				url: session.url || "",
			},
			message: "Checkout session created successfully",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.PAYMENT_CREATE_INTENT);
	}
});

/**
 * POST /api/payments/create-expansion-checkout
 * Create checkout for device expansion
 */
router.post("/create-expansion-checkout", async (req, res) => {
	try {
		const validatedData = createExpansionCheckoutSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.PAYMENT_CREATE_INTENT,
			`Creating expansion checkout session: ${validatedData.licenseId}`,
			{
				body: {
					licenseId: validatedData.licenseId,
					additionalDevices: validatedData.additionalDevices,
				},
			},
		);

		// Verify license exists and is active
		const license = await getLicenseById(validatedData.licenseId);

		if (!license) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License not found",
				error: {
					code: "LICENSE_NOT_FOUND",
					message: "License not found",
				},
			};
			return res.status(404).json(response);
		}

		if (license.status !== "ACTIVE") {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License is not active",
				error: {
					code: "LICENSE_NOT_ACTIVE",
					message: "License is not active",
				},
			};
			return res.status(400).json(response);
		}

		// Calculate pricing for device expansion ($1.99 per device)
		const pricePerDevice = 199; // $1.99 in cents
		const totalAmount = pricePerDevice * validatedData.additionalDevices;

		// Create Stripe checkout session
		const session = await stripe.checkout.sessions.create({
			payment_method_types: ["card"],
			line_items: [
				{
					price_data: {
						currency: "usd",
						product_data: {
							name: "SnapBack Device Expansion",
							description: `Add ${validatedData.additionalDevices} additional device${
								validatedData.additionalDevices > 1 ? "s" : ""
							} to your license`,
						},
						unit_amount: pricePerDevice,
					},
					quantity: validatedData.additionalDevices,
				},
			],
			mode: "payment",
			customer_email: license.customerEmail,
			success_url: validatedData.successUrl,
			cancel_url: validatedData.cancelUrl,
			metadata: {
				licenseId: validatedData.licenseId,
				additionalDevices: validatedData.additionalDevices.toString(),
				customerEmail: license.customerEmail,
				expansionType: "device_expansion",
			},
		});

		// Create PaymentIntent record
		if (session.payment_intent) {
			await createPaymentIntent({
				stripePaymentIntentId: session.payment_intent as string,
				stripeCheckoutSessionId: session.id,
				amount: totalAmount,
				currency: "usd",
				paymentType: "DEVICE_EXPANSION",
				customerEmail: license.customerEmail,
				customerName: license.customerName,
			});
		}

		const response: ApiResponse<{
			sessionId: string;
			url: string;
			amount: number;
		}> = {
			success: true,
			data: {
				sessionId: session.id,
				url: session.url as string,
				amount: totalAmount,
			},
			message: "Device expansion checkout session created successfully",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.PAYMENT_CREATE_INTENT);
	}
});

/**
 * POST /api/payments/create-tier-upgrade-checkout
 * Create checkout for license tier upgrade
 */
router.post("/create-tier-upgrade-checkout", async (req, res) => {
	try {
		const validatedData = createTierUpgradeSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.PAYMENT_CREATE_INTENT,
			`Creating tier upgrade checkout session: ${validatedData.licenseId}`,
			{
				body: {
					licenseId: validatedData.licenseId,
					targetTier: validatedData.targetTier,
				},
			},
		);

		// Verify license exists and is active
		const license = await getLicenseById(validatedData.licenseId);

		if (!license) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License not found",
				error: {
					code: "LICENSE_NOT_FOUND",
					message: "License not found",
				},
			};
			return res.status(404).json(response);
		}

		if (license.status !== "ACTIVE") {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License is not active",
				error: {
					code: "LICENSE_NOT_ACTIVE",
					message: "License is not active",
				},
			};
			return res.status(400).json(response);
		}

		// Validate upgrade path
		if (license.licenseType === validatedData.targetTier) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "License is already at the target tier",
				error: {
					code: "INVALID_UPGRADE",
					message: "License is already at the target tier",
				},
			};
			return res.status(400).json(response);
		}

		// Only allow PRO -> ENTERPRISE upgrades for now
		if (
			license.licenseType !== "PRO" ||
			validatedData.targetTier !== "ENTERPRISE"
		) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message:
					"Invalid upgrade path. Only PRO to ENTERPRISE upgrades are supported",
				error: {
					code: "INVALID_UPGRADE_PATH",
					message:
						"Invalid upgrade path. Only PRO to ENTERPRISE upgrades are supported",
				},
			};
			return res.status(400).json(response);
		}

		// Calculate upgrade pricing (Enterprise $9.99 - Pro $4.99 = $5.00)
		const tierPricing = {
			PRO: 499, // $4.99
			ENTERPRISE: 999, // $9.99
		};

		const upgradeAmount =
			tierPricing[validatedData.targetTier] - tierPricing[license.licenseType];

		// Create Stripe checkout session
		const session = await stripe.checkout.sessions.create({
			payment_method_types: ["card"],
			line_items: [
				{
					price_data: {
						currency: "usd",
						product_data: {
							name: "SnapBack License Upgrade",
							description: `Upgrade from ${license.licenseType} to ${validatedData.targetTier}`,
						},
						unit_amount: upgradeAmount,
					},
					quantity: 1,
				},
			],
			mode: "payment",
			customer_email: license.customerEmail,
			success_url: validatedData.successUrl,
			cancel_url: validatedData.cancelUrl,
			metadata: {
				licenseId: validatedData.licenseId,
				fromTier: license.licenseType,
				toTier: validatedData.targetTier,
				customerEmail: license.customerEmail,
				upgradeType: "tier_upgrade",
			},
		});

		// Create PaymentIntent record
		if (session.payment_intent) {
			await createPaymentIntent({
				stripePaymentIntentId: session.payment_intent as string,
				stripeCheckoutSessionId: session.id,
				amount: upgradeAmount,
				currency: "usd",
				paymentType: "LICENSE_PURCHASE", // Reuse existing enum value
				customerEmail: license.customerEmail,
				customerName: license.customerName,
			});
		}

		const response: ApiResponse<{
			sessionId: string;
			url: string;
			amount: number;
		}> = {
			success: true,
			data: {
				sessionId: session.id,
				url: session.url as string,
				amount: upgradeAmount,
			},
			message: "Tier upgrade checkout session created successfully",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.PAYMENT_CREATE_INTENT);
	}
});

/**
 * GET /api/payments/:paymentIntentId
 * Get payment details
 */
router.get("/:paymentIntentId", requireAuth, async (req, res) => {
	try {
		const { paymentIntentId } = req.params;

		Logger.info(
			EndpointPrefix.PAYMENT_STATUS,
			`Payment details request: ${paymentIntentId}`,
			{ body: { paymentIntentId } },
		);

		const paymentIntent = await getPaymentIntentByStripeId(paymentIntentId);

		if (!paymentIntent) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "Payment not found",
				error: {
					code: "PAYMENT_NOT_FOUND",
					message: "Payment not found",
				},
			};
			return res.status(404).json(response);
		}

		const response: ApiResponse<typeof paymentIntent> = {
			success: true,
			data: paymentIntent,
			message: "Payment details retrieved",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.PAYMENT_STATUS);
	}
});

export default router;
