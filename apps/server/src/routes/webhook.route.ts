import { Router, raw } from "express";
import type Stripe from "stripe";
import { processDeviceExpansion } from "@/controllers/device.controller";
import {
	createLicenseFromCheckout,
	processTierUpgrade,
	updateEmailDeliveryStatus,
} from "@/controllers/license.controller";
import {
	processWebhookEvent,
	updatePaymentIntentStatus,
} from "@/controllers/payment.controller";
import { stripe } from "@/lib/stripe";
import {
	sendDeviceExpansionEmail,
	sendLicenseEmail,
	sendTierUpgradeEmail,
} from "@/templates";
import { EndpointPrefix, Logger } from "@/utils/logger";

const router: Router = Router();

// ============================================================================
// STRIPE WEBHOOK PROCESSING
// ============================================================================

/**
 * POST /api/payments/webhooks/stripe
 * Handle Stripe webhooks (license creation, refunds)
 *
 * Note: This route must be registered BEFORE express.json() middleware
 * to preserve the raw body for signature verification
 */
router.post("/stripe", raw({ type: "application/json" }), async (req, res) => {
	const sig = req.headers["stripe-signature"] as string;
	const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

	if (!endpointSecret) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			"Stripe webhook secret not configured",
		);
		return res.status(500).json({ error: "Webhook secret not configured" });
	}

	let event: Stripe.Event;

	try {
		// Verify webhook signature
		event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
	} catch (err) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Webhook signature verification failed: ${err}`,
		);
		return res.status(400).json({ error: "Invalid signature" });
	}

	Logger.info(
		EndpointPrefix.PAYMENT_WEBHOOK,
		`Received webhook event: ${event.type}`,
		{
			body: {
				eventId: event.id,
				eventType: event.type,
			},
		},
	);

	try {
		// Process different webhook event types
		switch (event.type) {
			case "checkout.session.completed": {
				const session = event.data.object as Stripe.Checkout.Session;

				Logger.info(
					EndpointPrefix.PAYMENT_WEBHOOK,
					`Processing checkout.session.completed: ${session.id}`,
					{
						body: {
							sessionId: session.id,
							paymentIntentId: session.payment_intent,
							customerEmail: session.customer_details?.email,
						},
					},
				);

				// Update PaymentIntent status
				if (session.payment_intent) {
					await updatePaymentIntentStatus(
						session.payment_intent as string,
						"SUCCEEDED",
					);
				}

				// Step 1: Process webhook event for idempotency and audit
				const webhookResult = await processWebhookEvent({
					stripeEventId: event.id,
					eventType: event.type,
					paymentIntentId: session.payment_intent as string,
					checkoutSessionData: session,
				});

				if (!webhookResult.success) {
					Logger.error(
						EndpointPrefix.PAYMENT_WEBHOOK,
						`Failed to process webhook event: ${webhookResult.error}`,
						{
							body: {
								sessionId: session.id,
								error: webhookResult.error,
							},
						},
					);
					return res.status(500).json({ error: "Failed to process webhook" });
				}

				// If already processed, skip business logic
				if (webhookResult.processed && webhookResult.success) {
					Logger.info(
						EndpointPrefix.PAYMENT_WEBHOOK,
						`Webhook already processed, skipping business logic: ${session.id}`,
					);
					break;
				}

				// Step 2: Process business logic based on metadata
				const metadata = session.metadata;
				const customerEmail =
					session.customer_details?.email || session.customer_email;
				const customerName = session.customer_details?.name;
				const paymentIntentId = session.payment_intent as string;
				const amountTotal = session.amount_total || 0;

				if (!customerEmail || !paymentIntentId) {
					Logger.error(
						EndpointPrefix.PAYMENT_WEBHOOK,
						"Missing required customer data in checkout session",
						{
							body: {
								sessionId: session.id,
								hasEmail: !!customerEmail,
								hasPaymentIntent: !!paymentIntentId,
							},
						},
					);
					break;
				}

				// Handle different types of purchases based on metadata
				if (metadata?.type === "device_expansion") {
					// Device expansion purchase
					const licenseId = metadata.licenseId;
					const additionalDevices = Number.parseInt(
						metadata.additionalDevices || "0",
					);

					if (licenseId && additionalDevices > 0) {
						const expansionResult = await processDeviceExpansion({
							licenseId,
							additionalDevices,
							paymentIntentId,
							amount: amountTotal,
						});

						if (expansionResult.success && expansionResult.license) {
							// Send device expansion notification email directly
							await sendDeviceExpansionEmail(
								expansionResult.license.customerEmail,
								expansionResult.license.licenseKey,
								additionalDevices,
								expansionResult.license.maxDevices,
							);
							Logger.info(
								EndpointPrefix.PAYMENT_WEBHOOK,
								`Device expansion processed and email sent: ${licenseId}`,
								{ body: { licenseId, additionalDevices } },
							);
						} else {
							Logger.error(
								EndpointPrefix.PAYMENT_WEBHOOK,
								`Failed to process device expansion: ${expansionResult.error}`,
								{ body: { licenseId, additionalDevices } },
							);
						}
					}
				} else if (metadata?.type === "tier_upgrade") {
					// Tier upgrade purchase
					const licenseId = metadata.licenseId;
					const targetTier = metadata.targetTier;
					const fromTier = metadata.fromTier;

					if (licenseId && targetTier) {
						const upgradeResult = await processTierUpgrade({
							licenseId,
							fromTier: (fromTier as "PRO" | "ENTERPRISE") || "PRO",
							toTier: targetTier as "PRO" | "ENTERPRISE",
							paymentIntentId,
							amount: amountTotal,
						});

						if (upgradeResult.success && upgradeResult.license) {
							// Send tier upgrade notification email directly
							await sendTierUpgradeEmail(
								upgradeResult.license.customerEmail,
								upgradeResult.license.licenseKey,
								fromTier || "PRO",
								targetTier,
								upgradeResult.license.maxDevices,
							);
							Logger.info(
								EndpointPrefix.PAYMENT_WEBHOOK,
								`Tier upgrade processed and email sent: ${licenseId}`,
								{ body: { licenseId, fromTier, targetTier } },
							);
						} else {
							Logger.error(
								EndpointPrefix.PAYMENT_WEBHOOK,
								`Failed to process tier upgrade: ${upgradeResult.error}`,
								{ body: { licenseId, targetTier } },
							);
						}
					}
				} else {
					// Regular license purchase (new license creation)
					const licenseResult = await createLicenseFromCheckout({
						customerEmail,
						customerName,
						paymentIntentId,
						amount: amountTotal,
						licenseType:
							metadata?.licenseType === "ENTERPRISE" ? "ENTERPRISE" : "PRO",
					});

					if (licenseResult.success && licenseResult.license) {
						await sendLicenseEmail(
							licenseResult.license.customerEmail,
							licenseResult.license.licenseKey,
							licenseResult.license.licenseType,
							licenseResult.license.expiresAt,
						);

						// Update email delivery status
						await updateEmailDeliveryStatus(licenseResult.license.id, "sent");

						Logger.info(
							EndpointPrefix.PAYMENT_WEBHOOK,
							`License created and welcome email sent: ${customerEmail}`,
							{
								body: {
									licenseId: licenseResult.license.id,
									licenseKey: licenseResult.license.licenseKey,
								},
							},
						);
					} else {
						Logger.error(
							EndpointPrefix.PAYMENT_WEBHOOK,
							`Failed to create license: ${licenseResult.error}`,
							{ body: { customerEmail, paymentIntentId } },
						);
					}
				}

				// Handle device expansion if metadata indicates it
				if (session.metadata?.expansionType === "device_expansion") {
					const licenseId = session.metadata.licenseId;
					const additionalDevices = Number.parseInt(
						session.metadata.additionalDevices || "0",
					);

					if (licenseId && additionalDevices > 0) {
						const expansionResult = await processDeviceExpansion({
							licenseId,
							paymentIntentId: session.payment_intent as string,
							additionalDevices,
							amount: session.amount_total || 0,
						});

						if (!expansionResult.success) {
							Logger.error(
								EndpointPrefix.PAYMENT_WEBHOOK,
								`Failed to process device expansion: ${expansionResult.error}`,
								{
									body: {
										licenseId,
										additionalDevices,
										error: expansionResult.error,
									},
								},
							);
						}
					}
				}

				// Handle tier upgrade if metadata indicates it
				if (session.metadata?.upgradeType === "tier_upgrade") {
					const licenseId = session.metadata.licenseId;
					const fromTier = session.metadata.fromTier as "PRO" | "ENTERPRISE";
					const toTier = session.metadata.toTier as "PRO" | "ENTERPRISE";

					if (licenseId && fromTier && toTier) {
						const upgradeResult = await processTierUpgrade({
							licenseId,
							paymentIntentId: session.payment_intent as string,
							fromTier,
							toTier,
							amount: session.amount_total || 0,
						});

						if (!upgradeResult.success) {
							Logger.error(
								EndpointPrefix.PAYMENT_WEBHOOK,
								`Failed to process tier upgrade: ${upgradeResult.error}`,
								{
									body: {
										licenseId,
										fromTier,
										toTier,
										error: upgradeResult.error,
									},
								},
							);
						}
					}
				}

				break;
			}

			case "payment_intent.succeeded": {
				const paymentIntent = event.data.object as Stripe.PaymentIntent;

				Logger.info(
					EndpointPrefix.PAYMENT_WEBHOOK,
					`Processing payment_intent.succeeded: ${paymentIntent.id}`,
					{
						body: {
							paymentIntentId: paymentIntent.id,
							amount: paymentIntent.amount,
						},
					},
				);

				// Update PaymentIntent status
				await updatePaymentIntentStatus(paymentIntent.id, "SUCCEEDED");

				// Process webhook event for audit logging only (no business logic for fallback)
				await processWebhookEvent({
					stripeEventId: event.id,
					eventType: event.type,
					paymentIntentId: paymentIntent.id,
				});

				break;
			}

			case "payment_intent.payment_failed": {
				const paymentIntent = event.data.object as Stripe.PaymentIntent;

				Logger.warn(
					EndpointPrefix.PAYMENT_WEBHOOK,
					`Processing payment_intent.payment_failed: ${paymentIntent.id}`,
					{
						body: {
							paymentIntentId: paymentIntent.id,
							lastPaymentError: paymentIntent.last_payment_error,
						},
					},
				);

				// Update PaymentIntent status
				await updatePaymentIntentStatus(paymentIntent.id, "FAILED");

				// Process webhook event for audit logging only
				await processWebhookEvent({
					stripeEventId: event.id,
					eventType: event.type,
					paymentIntentId: paymentIntent.id,
				});

				break;
			}

			case "charge.dispute.created": {
				const dispute = event.data.object as Stripe.Dispute;

				Logger.warn(
					EndpointPrefix.PAYMENT_WEBHOOK,
					`Processing charge.dispute.created: ${dispute.id}`,
					{
						body: {
							disputeId: dispute.id,
							chargeId: dispute.charge,
							amount: dispute.amount,
							reason: dispute.reason,
						},
					},
				);

				// Process webhook event for audit logging only
				await processWebhookEvent({
					stripeEventId: event.id,
					eventType: event.type,
				});

				// TODO: Implement dispute handling logic
				// - Notify admins
				// - Suspend related license if needed
				// - Create audit log entry

				break;
			}

			case "invoice.payment_succeeded": {
				const invoice = event.data.object as Stripe.Invoice;

				// Extract subscription ID from invoice lines if available
				const subscriptionId = invoice.lines?.data?.[0]?.subscription || null;

				Logger.info(
					EndpointPrefix.PAYMENT_WEBHOOK,
					`Processing invoice.payment_succeeded: ${invoice.id}`,
					{
						body: {
							invoiceId: invoice.id,
							subscriptionId,
							amount: invoice.amount_paid,
							customerId: invoice.customer,
						},
					},
				);

				// Process webhook event for audit logging only
				await processWebhookEvent({
					stripeEventId: event.id,
					eventType: event.type,
				});

				// TODO: Handle subscription renewals when implemented

				break;
			}

			default: {
				Logger.info(
					EndpointPrefix.PAYMENT_WEBHOOK,
					`Unhandled webhook event type: ${event.type}`,
					{
						body: {
							eventId: event.id,
							eventType: event.type,
						},
					},
				);

				// Process webhook event for audit logging only
				await processWebhookEvent({
					stripeEventId: event.id,
					eventType: event.type,
				});
			}
		}

		Logger.info(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Successfully processed webhook event: ${event.id}`,
			{
				body: {
					eventId: event.id,
					eventType: event.type,
				},
			},
		);

		res.status(200).json({ received: true });
	} catch (error) {
		Logger.error(
			EndpointPrefix.PAYMENT_WEBHOOK,
			`Webhook processing error: ${error}`,
			{
				body: {
					eventId: event.id,
					eventType: event.type,
					error: String(error),
				},
			},
		);

		// Return 500 to trigger Stripe's retry mechanism
		res.status(500).json({ error: "Webhook processing failed" });
	}
});

export default router;
