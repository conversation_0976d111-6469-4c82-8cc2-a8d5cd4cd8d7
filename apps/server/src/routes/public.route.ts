import {
	acceptInvitationSchema,
	createSupportTicketSchema,
	licenseLookupSchema,
	pricingSchema,
	validatePurchaseSchema,
} from "@snapback/shared/schemas";
import { Router } from "express";
import { getLicenseByEmail } from "@/controllers/license.controller";
import {
	createSupportTicket,
	getFaqData,
} from "@/controllers/support.controller";
import {
	acceptInvitation,
	validateInvitation,
} from "@/controllers/user.controller";
import type { ApiResponse } from "@/types/api";
import { handleError } from "@/utils/errors";
import { EndpointPrefix, Logger } from "@/utils/logger";

const router: Router = Router();

// ============================================================================
// PRICING & PURCHASE FLOW
// ============================================================================

/**
 * GET /api/public/pricing
 * Get current pricing information
 */
router.get("/pricing", async (req, res) => {
	try {
		const { currency } = pricingSchema.parse(req.query);

		Logger.info(
			EndpointPrefix.PAYMENT_PRICING,
			`Pricing request for currency: ${currency}`,
			{ body: { currency } },
		);

		// Define pricing in different currencies
		const pricing = {
			usd: {
				currency: "USD",
				symbol: "$",
				free: {
					name: "Free",
					price: 0,
					devices: 1,
					workspaces: 3,
					features: [
						"1 device",
						"3 workspaces",
						"Basic functionality",
						"No cloud sync",
						"No import/export",
					],
				},
				pro: {
					name: "Pro",
					price: 4.99,
					devices: 2,
					features: [
						"2 devices",
						"Unlimited workspaces",
						"Cloud sync",
						"Import/Export",
						"Premium features",
						"Email support",
					],
				},
				enterprise: {
					name: "Enterprise",
					price: 9.99,
					devices: 5,
					features: [
						"5 devices",
						"Unlimited workspaces",
						"Cloud sync",
						"Import/Export",
						"Premium features",
						"Priority support",
						"Advanced analytics",
					],
				},
				deviceExpansion: {
					name: "Additional Device",
					price: 1.99,
					description: "Add one more device to your license",
				},
			},
			eur: {
				currency: "EUR",
				symbol: "€",
				free: {
					name: "Free",
					price: 0,
					devices: 1,
					workspaces: 3,
					features: [
						"1 device",
						"3 workspaces",
						"Basic functionality",
						"No cloud sync",
						"No import/export",
					],
				},
				pro: {
					name: "Pro",
					price: 4.49,
					devices: 2,
					features: [
						"2 devices",
						"Unlimited workspaces",
						"Cloud sync",
						"Import/Export",
						"Premium features",
						"Email support",
					],
				},
				enterprise: {
					name: "Enterprise",
					price: 8.99,
					devices: 5,
					features: [
						"5 devices",
						"Unlimited workspaces",
						"Cloud sync",
						"Import/Export",
						"Premium features",
						"Priority support",
						"Advanced analytics",
					],
				},
				deviceExpansion: {
					name: "Additional Device",
					price: 1.79,
					description: "Add one more device to your license",
				},
			},
			gbp: {
				currency: "GBP",
				symbol: "£",
				free: {
					name: "Free",
					price: 0,
					devices: 1,
					workspaces: 3,
					features: [
						"1 device",
						"3 workspaces",
						"Basic functionality",
						"No cloud sync",
						"No import/export",
					],
				},
				pro: {
					name: "Pro",
					price: 3.99,
					devices: 2,
					features: [
						"2 devices",
						"Unlimited workspaces",
						"Cloud sync",
						"Import/Export",
						"Premium features",
						"Email support",
					],
				},
				enterprise: {
					name: "Enterprise",
					price: 7.99,
					devices: 5,
					features: [
						"5 devices",
						"Unlimited workspaces",
						"Cloud sync",
						"Import/Export",
						"Premium features",
						"Priority support",
						"Advanced analytics",
					],
				},
				deviceExpansion: {
					name: "Additional Device",
					price: 1.59,
					description: "Add one more device to your license",
				},
			},
		};

		const response: ApiResponse<(typeof pricing)[typeof currency]> = {
			success: true,
			data: pricing[currency],
			message: "Pricing information retrieved",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.PAYMENT_PRICING);
	}
});

/**
 * POST /api/public/purchase/validate
 * Validate purchase details before checkout
 */
router.post("/purchase/validate", async (req, res) => {
	try {
		const validatedData = validatePurchaseSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.PAYMENT_CREATE_INTENT,
			`Purchase validation request: ${validatedData.customerEmail}`,
			{
				body: {
					licenseType: validatedData.licenseType,
					customerEmail: validatedData.customerEmail,
				},
			},
		);

		// Check if customer already has a license
		const existingLicense = await getLicenseByEmail(
			validatedData.customerEmail,
		);

		if (existingLicense && existingLicense.status === "ACTIVE") {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "Customer already has an active license",
				error: {
					code: "LICENSE_EXISTS",
					message: "A license already exists for this email address",
				},
			};
			return res.status(400).json(response);
		}

		const response: ApiResponse<{
			valid: boolean;
			licenseType: string;
			customerEmail: string;
		}> = {
			success: true,
			data: {
				valid: true,
				licenseType: validatedData.licenseType,
				customerEmail: validatedData.customerEmail,
			},
			message: "Purchase details are valid",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.PAYMENT_CREATE_INTENT);
	}
});

/**
 * GET /api/public/purchase/success
 * Handle successful purchase redirect
 */
router.get("/purchase/success", async (req, res) => {
	try {
		const { session_id } = req.query;

		if (!session_id || typeof session_id !== "string") {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "Session ID is required",
				error: {
					code: "VALIDATION_ERROR",
					message: "Session ID is required",
				},
			};
			return res.status(400).json(response);
		}

		Logger.info(
			EndpointPrefix.PAYMENT_STATUS,
			`Purchase success redirect: ${session_id}`,
			{ body: { sessionId: session_id } },
		);

		const response: ApiResponse<{
			sessionId: string;
			status: string;
			message: string;
		}> = {
			success: true,
			data: {
				sessionId: session_id,
				status: "success",
				message:
					"Purchase completed successfully. Your license will be sent to your email shortly.",
			},
			message: "Purchase completed successfully",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.PAYMENT_STATUS);
	}
});

/**
 * GET /api/public/purchase/cancel
 * Handle cancelled purchase
 */
router.get("/purchase/cancel", async (req, res) => {
	try {
		const { session_id } = req.query;

		Logger.info(
			EndpointPrefix.PAYMENT_STATUS,
			`Purchase cancelled: ${session_id || "unknown"}`,
			{ body: { sessionId: session_id } },
		);

		const response: ApiResponse<{
			status: string;
			message: string;
		}> = {
			success: true,
			data: {
				status: "cancelled",
				message: "Purchase was cancelled. You can try again anytime.",
			},
			message: "Purchase cancelled",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.PAYMENT_STATUS);
	}
});

// ============================================================================
// CUSTOMER SUPPORT
// ============================================================================

/**
 * POST /api/public/support/license-lookup
 * Look up license by email (rate limited)
 */
router.post("/support/license-lookup", async (req, res) => {
	try {
		const { email } = licenseLookupSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.LICENSE_STATUS,
			`Public license lookup request: ${email}`,
			{ body: { email } },
		);

		const license = await getLicenseByEmail(email);

		if (!license) {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: "No license found for this email address",
				error: {
					code: "LICENSE_NOT_FOUND",
					message: "No license found for this email address",
				},
			};
			return res.status(404).json(response);
		}

		// Return limited information for security
		const response: ApiResponse<{
			found: boolean;
			licenseType: string;
			status: string;
			expiresAt: Date | null;
		}> = {
			success: true,
			data: {
				found: true,
				licenseType: license.licenseType,
				status: license.status,
				expiresAt: license.expiresAt,
			},
			message: "License found. Check your email for the license key.",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.LICENSE_STATUS);
	}
});

// ============================================================================
// AUTHENTICATION & INVITATIONS
// ============================================================================

/**
 * GET /api/public/invitations/:token/validate
 * Validate invitation token (Public) - follows established naming pattern
 */
router.get("/invitations/:token/validate", async (req, res) => {
	try {
		const { token } = req.params;

		// Use controller function for business logic
		const result = await validateInvitation(token);

		if (result.success && result.data) {
			const response: ApiResponse<typeof result.data> = {
				success: true,
				data: result.data,
				message: "Invitation is valid",
			};
			res.status(200).json(response);
		} else {
			// Determine appropriate error code and status
			let errorCode = "INVITATION_ERROR";
			let statusCode = 400;

			if (result.error?.includes("Invalid invitation token")) {
				errorCode = "INVITATION_NOT_FOUND";
				statusCode = 404;
			} else if (result.error?.includes("already been used")) {
				errorCode = "INVITATION_INVALID";
			} else if (result.error?.includes("expired")) {
				errorCode = "INVITATION_EXPIRED";
			}

			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: result.error || "Invitation validation failed",
				error: {
					code: errorCode,
					message: result.error || "Invitation validation failed",
				},
			};
			res.status(statusCode).json(response);
		}
	} catch (error) {
		handleError(res, error, EndpointPrefix.AUTH_INVITATION);
	}
});

/**
 * POST /api/public/invitations/:token/accept
 * Accept invitation and create user account (Public)
 * DEPRECATED: Replaced by better-auth flow
 */
router.post("/invitations/:token/accept", async (req, res) => {
	try {
		const { token } = req.params;
		const validatedData = acceptInvitationSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.AUTH_INVITATION,
			`Invitation acceptance request: ${token}`,
			{ body: { name: validatedData.name } },
		);

		// Use existing controller function but integrate with Better Auth
		const result = await acceptInvitation(token, validatedData.name);

		if (result.success) {
			const response: ApiResponse<typeof result.user> = {
				success: true,
				data: result.user,
				message: "Invitation accepted and user account created successfully",
			};
			res.status(201).json(response);
		} else {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: result.error || "Failed to accept invitation",
				error: {
					code: "INVITATION_ACCEPTANCE_FAILED",
					message: result.error || "Failed to accept invitation",
				},
			};
			res.status(400).json(response);
		}
	} catch (error) {
		handleError(res, error, EndpointPrefix.AUTH_INVITATION);
	}
});

// ============================================================================
// SUPPORT SYSTEM
// ============================================================================

/**
 * POST /api/public/support/contact
 * Create a new support ticket
 */
router.post("/support/contact", async (req, res) => {
	try {
		const validatedData = createSupportTicketSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.SUPPORT_TICKET_CREATE,
			`Support ticket creation request from ${validatedData.customerEmail}`,
			{
				body: {
					subject: validatedData.subject,
					category: validatedData.category,
					customerEmail: validatedData.customerEmail,
					hasLicenseKey: !!validatedData.licenseKey,
				},
			},
		);

		const result = await createSupportTicket({
			subject: validatedData.subject,
			description: validatedData.description,
			category: validatedData.category,
			customerEmail: validatedData.customerEmail,
			customerName: validatedData.customerName,
			licenseKey: validatedData.licenseKey,
		});

		if (result.success && result.ticket) {
			const response: ApiResponse<typeof result.ticket> = {
				success: true,
				data: result.ticket,
				message:
					"Support ticket created successfully. We'll get back to you soon!",
			};
			res.status(201).json(response);
		} else {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: result.error || "Failed to create support ticket",
				error: {
					code: "SUPPORT_TICKET_CREATION_FAILED",
					message: result.error || "Failed to create support ticket",
				},
			};
			res.status(400).json(response);
		}
	} catch (error) {
		handleError(res, error, EndpointPrefix.SUPPORT_TICKET_CREATE);
	}
});

/**
 * GET /api/public/support/faq
 * Get FAQ data with categories and questions
 */
router.get("/support/faq", async (req, res) => {
	try {
		Logger.info(EndpointPrefix.SUPPORT_FAQ, "FAQ data request", {
			body: { action: "get_faq" },
		});

		const faqData = await getFaqData();

		const response: ApiResponse<typeof faqData> = {
			success: true,
			data: faqData,
			message: "FAQ data retrieved successfully",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.SUPPORT_FAQ);
	}
});

export default router;
