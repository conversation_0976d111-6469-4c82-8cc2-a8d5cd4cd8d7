import {
	licenseLookupSchema,
	licensePingSchema,
	validateLicenseSchema,
} from "@snapback/shared/schemas";
import { Router } from "express";
import {
	getLicenseByEmail,
	getLicenseByKey,
	validateLicense,
} from "@/controllers/license.controller";

import type { ApiResponse } from "@/types/api";
import { handleError, LicenseErrors } from "@/utils/errors";
import { EndpointPrefix, Logger } from "@/utils/logger";

const router: Router = Router();

// ============================================================================
// PUBLIC LICENSE ROUTES (for App)
// ============================================================================

/**
 * POST /api/licenses/validate
 * Validate license key and register/update device
 */
router.post("/validate", async (req, res) => {
	try {
		const validatedData = validateLicenseSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.LICENSE_VALIDATE,
			`License validation request: ${validatedData.licenseKey}`,
			{ body: { licenseKey: validatedData.licenseKey } },
		);

		const result = await validateLicense(validatedData);

		if (result.success) {
			const response: ApiResponse<typeof result> = {
				success: true,
				data: result,
				message: "License validated successfully",
			};
			res.status(200).json(response);
		} else {
			const response: ApiResponse<null> = {
				success: false,
				data: null,
				message: result.error || "License validation failed",
				error: {
					code: result.errorCode || "VALIDATION_FAILED",
					message: result.error || "License validation failed",
				},
			};
			res.status(400).json(response);
		}
	} catch (error) {
		handleError(res, error, EndpointPrefix.LICENSE_VALIDATE);
	}
});

/**
 * POST /api/licenses/ping
 * Regular ping from app to check license status
 */
router.post("/ping", async (req, res) => {
	try {
		const { licenseKey } = licensePingSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.LICENSE_VALIDATE,
			`License ping: ${licenseKey}`,
			{ body: { licenseKey } },
		);

		const license = await getLicenseByKey(licenseKey);

		if (!license) {
			return LicenseErrors.notFound(res);
		}

		// Check license status
		const isValid =
			license.status === "ACTIVE" &&
			(!license.expiresAt || license.expiresAt > new Date());

		const response: ApiResponse<{
			valid: boolean;
			status: string;
			expiresAt: Date | null;
			maxDevices: number;
			usedDevices: number;
		}> = {
			success: true,
			data: {
				valid: isValid,
				status: license.status,
				expiresAt: license.expiresAt,
				maxDevices: license.maxDevices,
				usedDevices: license.usedDevices,
			},
			message: "License status retrieved",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.LICENSE_VALIDATE);
	}
});

/**
 * GET /api/licenses/:licenseKey/status
 * Get license status (for app)
 */
router.get("/:licenseKey/status", async (req, res) => {
	try {
		const { licenseKey } = req.params;

		Logger.info(
			EndpointPrefix.LICENSE_STATUS,
			`License status request: ${licenseKey}`,
			{ body: { licenseKey } },
		);

		const license = await getLicenseByKey(licenseKey);

		if (!license) {
			return LicenseErrors.notFound(res);
		}

		const response: ApiResponse<{
			licenseKey: string;
			status: string;
			licenseType: string;
			maxDevices: number;
			usedDevices: number;
			expiresAt: Date | null;
			createdAt: Date;
		}> = {
			success: true,
			data: {
				licenseKey: license.licenseKey,
				status: license.status,
				licenseType: license.licenseType,
				maxDevices: license.maxDevices,
				usedDevices: license.usedDevices,
				expiresAt: license.expiresAt,
				createdAt: license.createdAt,
			},
			message: "License status retrieved",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.LICENSE_STATUS);
	}
});

/**
 * POST /api/licenses/lookup
 * Look up license by customer email (for lost keys)
 */
router.post("/lookup", async (req, res) => {
	try {
		const { email } = licenseLookupSchema.parse(req.body);

		Logger.info(
			EndpointPrefix.LICENSE_STATUS,
			`License lookup request: ${email}`,
			{ body: { email } },
		);

		const license = await getLicenseByEmail(email);

		if (!license) {
			return LicenseErrors.notFound(res);
		}

		const response: ApiResponse<{
			licenseKey: string;
			licenseType: string;
			status: string;
			maxDevices: number;
			usedDevices: number;
			expiresAt: Date | null;
		}> = {
			success: true,
			data: {
				licenseKey: license.licenseKey,
				licenseType: license.licenseType,
				status: license.status,
				maxDevices: license.maxDevices,
				usedDevices: license.usedDevices,
				expiresAt: license.expiresAt,
			},
			message: "License found",
		};

		res.status(200).json(response);
	} catch (error) {
		handleError(res, error, EndpointPrefix.LICENSE_STATUS);
	}
});

export default router;
