{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "tsx prisma/seed.ts", "db:start": "docker compose up -d", "db:watch": "docker compose up", "db:stop": "docker compose stop", "db:down": "docker compose down", "stripe:listen": "stripe listen --forward-to http://localhost:3000/api/payments/webhooks"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.13.0", "@snapback/shared": "workspace:*", "bcryptjs": "^3.0.2", "better-auth": "^1.3.4", "chalk": "^5.6.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "nodemailer": "^7.0.5", "stripe": "^18.4.0", "zod": "^4.0.2"}, "devDependencies": {"@types/chalk": "^2.2.4", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^22.13.11", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@vitest/ui": "^3.2.4", "prisma": "^6.13.0", "prisma-zod-generator": "^1.18.5", "supertest": "^7.1.4", "tsdown": "^0.12.9", "tsx": "^4.19.2", "typescript": "^5.8.2", "vitest": "^3.2.4"}}