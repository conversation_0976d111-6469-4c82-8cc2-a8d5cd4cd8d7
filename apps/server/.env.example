# Server Configuration
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/

# Auth Configuration
BETTER_AUTH_SECRET=your-better-auth-secret-at-least-32-characters
BETTER_AUTH_URL=http://localhost:3000


# CORS Configuration
CORS_ORIGIN=http://localhost:3001

# JWT Configuration
JWT_SECRET=your-jwt-secret-at-least-32-characters

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLIC_KEY=pk_test_your-stripe-public-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret
ALLOW_TEST_STRIPE_KEYS=true  # For development only
SKIP_PAYMENT_VERIFICATION=true

# Email Configuration (SMTP)
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASS=your-smtp-password
FROM_EMAIL=<EMAIL>

# Security Configuration (Optional)
BLOCKED_IPS=*************,*********
REQUEST_SIGNING_SECRET=your-request-signing-secret-at-least-32-characters

# Rate Limiting Configuration (Optional)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration (Optional)
LOG_LEVEL=info