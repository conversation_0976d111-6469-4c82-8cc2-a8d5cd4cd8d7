# Snapback Server API Documentation

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Rate Limiting](#rate-limiting)
4. [Error Handling](#error-handling)
5. [API Endpoint Summary](#api-endpoint-summary)
6. [License Management Endpoints](#license-management-endpoints)
7. [Payment Processing Endpoints](#payment-processing-endpoints)
8. [Refund Management Endpoints](#refund-management-endpoints)
9. [User Management Endpoints](#user-management-endpoints)
10. [Webhook Endpoints](#webhook-endpoints)
11. [Health Check Endpoints](#health-check-endpoints)
12. [Security Features](#security-features)
13. [Service Layer Architecture](#service-layer-architecture)
14. [Middleware Stack](#middleware-stack)
15. [Error Codes](#error-codes)
16. [Response Formats](#response-formats)

## Overview

The Snapback Server API provides secure license management and payment processing capabilities for the Snapback application. The API is built with Express.js and uses Stripe for payment processing with a webhook-only architecture for maximum security.

**Base URL:** `https://localhost:3000/api`

**Supported HTTP Methods:** GET, POST, DELETE, OPTIONS

**Content Type:** `application/json`

### Key Features
- 🔒 **Webhook-Only Paid Licenses**: Industry-standard security for payment processing
- ⚡ **In-App Trial System**: Instant trial activation with rich device data
- 🛡️ **Advanced Security**: Device fingerprinting, VM detection, abuse prevention
- 📊 **Comprehensive Audit**: Full logging and monitoring for all operations
- 🎯 **Service Layer Architecture**: Clean separation of HTTP and business logic

### Security Architecture
- **Zero Client Trust**: No client-provided payment data accepted
- **Device-Based Authentication**: Hardware-level identification and rate limiting
- **Multi-Layer Protection**: IP, email, device, and hardware signature-based limits
- **Real-Time Monitoring**: Immediate detection and blocking of abuse patterns

## Authentication

### Device Token Authentication
Some endpoints require device token authentication via the `Authorization` header:

```
Authorization: Bearer <device_token>
```

Device tokens are JWT tokens generated during license validation and contain:
- `licenseId`: The license ID
- `deviceHash`: Hashed device identifier

## Rate Limiting

All API endpoints are protected by comprehensive rate limiting strategies:

### Standard Rate Limits
- **General API Limit:** 100 requests per 15 minutes per IP
- **License Validation:** 30 requests per minute per IP
- **License Resend:** 3 requests per hour per IP
- **Payment Processing:** 10 requests per 15 minutes per IP
- **Refund Requests:** 3 requests per hour per IP

### Trial License Rate Limits
- **Web-based Trials:** 5 requests per hour per IP + 1 request per email per day
- **In-app Trials:** 1 request per device per day + 3 requests per hardware signature per hour

### Device-Based Rate Limiting
For in-app trial requests, advanced rate limiting uses multiple device identifiers:
- **Device Fingerprint:** Combines deviceId, deviceFingerprint, and hardwareUUID
- **Hardware Signature:** Based on OS, architecture, memory, screen resolution, and device model
- **Composite Keys:** SHA-256 hashed combinations prevent spoofing

Rate limit headers are included in responses:
- `RateLimit-Limit`: Request limit per window
- `RateLimit-Remaining`: Remaining requests in current window
- `RateLimit-Reset`: Time when the rate limit resets

## Error Handling

### Standard Error Response Format

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "message": "Detailed error description",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/endpoint",
  "details": {
    "additional": "context"
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR`: Invalid input data
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `LICENSE_NOT_FOUND`: License key not found
- `LICENSE_EXPIRED`: License has expired
- `MAX_DEVICES_REACHED`: Device limit exceeded
- `TRIAL_ALREADY_USED_EMAIL`: Trial already used with email
- `TRIAL_ALREADY_USED_DEVICE`: Trial already used on device
- `PAYMENT_INCOMPLETE`: Payment not completed
- `UNAUTHORIZED`: Invalid or missing authentication
- `DATABASE_ERROR`: Database operation failed
- `REFUND_NOT_ELIGIBLE`: License not eligible for refund
- `REFUND_ALREADY_EXISTS`: Refund request already exists
- `REFUND_ALREADY_PROCESSED`: Refund request already processed
- `EXTERNAL_SERVICE_ERROR`: Payment provider error

## API Endpoint Summary

### License Management
| Method | Endpoint | Purpose | Security |
|--------|----------|---------|----------|
| `POST` | `/api/licenses/trial` | Create web-based trial license | Email + IP rate limiting |
| `POST` | `/api/licenses/trial/in-app` | **Create in-app trial license** | Device + hardware rate limiting |
| `GET` | `/api/licenses/payment-status/{clientSecret}` | Poll for paid license creation | Webhook-only architecture |
| `POST` | `/api/licenses/validate` | Validate license and register device | Standard rate limiting |
| `POST` | `/api/licenses/resend` | Resend license email | Email enumeration protection |
| ~~`GET`~~ | ~~`/api/licenses/upgrade-status/{clientSecret}`~~ | ~~Poll for license upgrade completion~~ | **REMOVED** - Single-license model |
| `GET` | `/api/licenses/status/{licenseKey}` | Get license status and devices | Public endpoint |
| `DELETE` | `/api/licenses/devices/{deviceId}` | Remove device from license | Device token required |
| `PUT` | `/api/licenses/devices/metadata` | Update device metadata | Device token required |

### Payment Processing
| Method | Endpoint | Purpose | Security |
|--------|----------|---------|----------|
| `GET` | `/api/payments/pricing` | Get current pricing | Public endpoint |
| `POST` | `/api/payments/create-payment-intent` | Create payment intent (embedded) | Rate limited |
| `POST` | `/api/payments/create-checkout-session` | Create checkout session (redirect) | Rate limited |
| ~~`POST`~~ | ~~`/api/payments/create-upgrade-payment-intent`~~ | ~~Create upgrade payment intent~~ | **REMOVED** - Single-license model |

### Webhook Processing (Internal)
| Method | Endpoint | Purpose | Security |
|--------|----------|---------|----------|
| `POST` | `/api/webhooks/stripe` | Process Stripe webhook events | Signature verification |

### Key Architecture Changes
- 🔒 **Paid License Creation**: Now webhook-only for maximum security
- ⚡ **In-App Trials**: Instant activation with rich device data
- 🛡️ **Device-Based Security**: Hardware fingerprinting and abuse detection
- 📊 **Comprehensive Logging**: Full audit trails for all operations

## License Management Endpoints

### ⚠️ Paid License Creation (Webhook-Only Architecture)

**IMPORTANT:** Paid license creation (`pro`) is now handled exclusively through Stripe webhooks for maximum security. Direct API license creation has been removed to prevent payment manipulation attacks.

**Paid License Flow:**
1. User completes payment via Stripe Checkout or Elements
2. Stripe sends verified webhook event to server
3. Server creates license automatically via webhook handler
4. Frontend polls for license completion using status endpoint

### Create Trial License (Web-Based)

Creates a trial license for email marketing capture. Device registration happens later during license validation.

**Endpoint:** `POST /api/licenses/trial`

**Rate Limit:** 5 requests per hour per IP + 1 request per email per day

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Request Validation:**
- `email`: Valid email address (max 254 chars), normalized to lowercase

**Success Response (201):**
```json
{
  "success": true,
  "license": {
    "licenseKey": "TRIAL-ABCD-1234-EFGH-5678",
    "licenseType": "trial",
    "maxDevices": 1,
    "expiresAt": "2024-01-15T00:00:00.000Z",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Trial license created successfully! Check your email for the license key. You can then validate the license on your device to start using SnapBack.",
  "instructions": {
    "nextSteps": [
      "Check your email for the trial license key",
      "Download and install SnapBack on your device",
      "Enter the license key when prompted to activate your trial"
    ],
    "note": "Device registration will happen automatically when you validate your license"
  }
}
```

**Error Responses:**
- `400`: Trial registration disabled or validation error
- `409`: Trial license already exists for email
- `429`: Rate limit exceeded
- `500`: Internal server error

### Create Trial License (In-App) - **RECOMMENDED**

Creates a trial license with immediate device registration for in-app requests. This provides instant activation without email dependency.

**Endpoint:** `POST /api/licenses/trial/in-app`

**Rate Limit:** 1 request per device per day + 3 requests per hardware signature per hour

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "deviceId": "unique-hardware-identifier",
  "deviceFingerprint": "comprehensive-device-signature",
  "appVersion": "1.0.0",
  "deviceMetadata": {
    "operatingSystem": "macOS 14.1",
    "architecture": "arm64",
    "totalMemory": 16384,
    "screenResolution": "2560x1600",
    "deviceModel": "MacBook Pro",
    "hardwareUUID": "system-generated-uuid",
    "macAddress": "optional-mac-address",
    "cpuInfo": "Apple M2 Pro",
    "diskSerial": "optional-disk-serial"
  }
}
```

**Request Validation:**
- `email`: Valid email address (max 254 chars), normalized to lowercase
- `deviceId`: 32-128 character alphanumeric device identifier
- `deviceFingerprint`: 32-128 character device signature for security
- `appVersion`: Semantic versioning format (e.g., "1.0.0")
- `deviceMetadata`: Rich device information object
  - `operatingSystem`: OS version (required, max 100 chars)
  - `architecture`: CPU architecture (required, max 50 chars)
  - `totalMemory`: RAM in MB as integer (required, positive number)
  - `screenResolution`: Display resolution in WIDTHxHEIGHT format (required, max 50 chars)
  - `deviceModel`: Device model (required, max 100 chars)
  - `hardwareUUID`: Hardware UUID (required, max 100 chars)
  - `macAddress`: MAC address (optional, max 100 chars)
  - `cpuInfo`: CPU information (optional, max 200 chars)
  - `diskSerial`: Disk serial number (optional, max 100 chars)

**Success Response (201) - New Trial:**
```json
{
  "success": true,
  "license": {
    "licenseKey": "TRIAL-ABCD-1234-EFGH-5678",
    "licenseType": "trial",
    "maxDevices": 1,
    "expiresAt": "2024-01-15T00:00:00.000Z",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "device": {
    "deviceToken": "jwt-device-authentication-token",
    "isNewDevice": true
  },
  "message": "Trial license created and activated successfully! You can now start using SnapBack."
}
```

**Success Response (200) - Existing Trial:**
```json
{
  "success": true,
  "license": {
    "licenseKey": "TRIAL-ABCD-1234-EFGH-5678",
    "licenseType": "trial",
    "maxDevices": 1,
    "expiresAt": "2024-01-15T00:00:00.000Z",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "device": {
    "deviceToken": "jwt-device-authentication-token",
    "isNewDevice": false
  },
  "message": "Existing trial license found for this device",
  "isExisting": true
}
```

**Error Responses:**
- `400`: Trial registration disabled or validation error
- `403`: Security violation or blocked request (VM detected, known abuse device, etc.)
- `429`: Rate limit exceeded (device or hardware-based)
- `500`: Internal server error

**Security Features:**
- **Virtual Machine Detection**: Identifies VM environments used for trial farming
- **Hardware Fingerprinting**: Prevents device spoofing and cloning
- **Disposable Email Detection**: Blocks temporary email services
- **Abuse Pattern Recognition**: Tracks known malicious devices and patterns
- **Multi-Layer Rate Limiting**: Device-based and hardware signature-based limits

### Check License Creation Status (For Paid Licenses)

Polls for license creation completion after Stripe payment. Used by frontend to detect when webhook processing is complete.

**Endpoint:** `GET /api/licenses/payment-status/{clientSecret}`

**Rate Limit:** 60 requests per minute per IP

**Path Parameters:**
- `clientSecret`: Stripe payment intent client secret (format: `pi_xxxxx_secret_yyyy`)

**Success Response (200) - License Created:**
```json
{
  "status": "completed",
  "license": {
    "licenseKey": "ABCD1234EFGH5678IJKL9012",
    "licenseType": "pro",
    "maxDevices": 2,
    "expiresAt": null,
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "License created successfully"
}
```

**Success Response (200) - License Pending:**
```json
{
  "status": "pending",
  "message": "License creation in progress. Please wait..."
}
```

**Error Responses:**
- `400`: Invalid client secret format
- `500`: Internal server error

**Usage Pattern:**
```javascript
// Frontend polling example
async function pollForLicense(clientSecret) {
  for (let i = 0; i < 30; i++) { // 30 attempts = 1 minute
    const response = await fetch(`/api/licenses/payment-status/${clientSecret}`);
    const data = await response.json();

    if (data.status === 'completed') {
      return data.license; // License created!
    }

    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
  }

  return null; // Timeout
}
```

### Validate License

Validates a license key and registers/authenticates a device.

**Endpoint:** `POST /api/licenses/validate`

**Rate Limit:** 30 requests per minute per IP

**Request Body:**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "deviceId": "unique-device-identifier",
  "appVersion": "1.0.0",
  "deviceMetadata": { // Optional device information
    "deviceName": "John's MacBook Pro",
    "deviceType": "MacBook Pro",
    "deviceModel": "14-inch M2 2023",
    "operatingSystem": "macOS 14.1",
    "architecture": "arm64",
    "screenResolution": "3024x1964",
    "totalMemory": "16 GB",
    "userNickname": "Work Laptop",
    "location": "Office",
    "notes": "Primary development machine"
  }
}
```

**Request Validation:**
- `licenseKey`: Exactly 24 characters, uppercase letters and numbers only
- `deviceId`: 32-128 character alphanumeric string
- `appVersion`: Semantic versioning format (optional)

**Success Response (200):**
```json
{
  "valid": true,
  "deviceToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "licenseType": "pro",
  "expiresAt": null,
  "maxDevices": 2,
  "devicesUsed": 1,
  "trialDaysRemaining": 0
}
```

**Error Responses:**
- `400`: Invalid input data
- `403`: License expired or device limit reached
- `404`: License not found
- `429`: Rate limit exceeded
- `500`: Internal server error

**Business Rules:**
- Automatically registers new devices if under limit
- Updates existing device's last seen timestamp and app version
- Returns device token for authenticated API access
- Calculates trial days remaining for trial licenses

### Resend License

Resends license information to the registered email address.

**Endpoint:** `POST /api/licenses/resend`

**Rate Limit:** 3 requests per hour per IP

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "message": "If a valid license exists for this email, it has been resent."
}
```

**Security Note:** Always returns success to prevent email enumeration attacks.

### ⚠️ License Upgrade Functionality Removed

**IMPORTANT:** License upgrade functionality has been completely removed from SnapBack. The system now uses a **single-license-per-purchase model**.

#### New Device Acquisition Model:
- **Only Pro Licenses**: Each license supports exactly 2 devices
- **No Upgrades**: Users cannot add additional devices to existing licenses
- **Multiple Licenses**: Users needing more devices must purchase separate pro licenses
- **Independent Operation**: Each license operates independently with its own license key

#### Migration for Existing Users:
- **Existing Licenses**: Continue to work with their current device limits
- **Additional Devices**: Must purchase new pro licenses for additional devices
- **No Retroactive Changes**: Existing upgrade history is preserved but no new upgrades are possible

### Get License Status

Retrieves detailed status information for a license.

**Endpoint:** `GET /api/licenses/status/{licenseKey}`

**Success Response (200):**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "licenseType": "pro",
  "email": "<EMAIL>",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "expiresAt": null,
  "maxDevices": 2,
  "devicesUsed": 1,
  "isExpired": false,
  "isActive": true,
  "trialDaysRemaining": 0,
  "devices": [
    {
      "id": "device-uuid",
      "firstSeen": "2024-01-01T00:00:00.000Z",
      "lastSeen": "2024-01-01T12:00:00.000Z",
      "appVersion": "1.0.0",
      "isActive": true,
      "deviceName": "John's MacBook Pro",
      "deviceType": "MacBook Pro",
      "deviceModel": "14-inch M2 2023",
      "operatingSystem": "macOS 14.1",
      "architecture": "arm64",
      "screenResolution": "3024x1964",
      "totalMemory": "16 GB",
      "userNickname": "Work Laptop",
      "location": "Office",
      "notes": "Primary development machine"
    }
  ]
}
```

### Remove Device

Removes a device from a license.

**Endpoint:** `DELETE /api/licenses/devices/{deviceId}`

**Authentication:** Required (Bearer token)

**Success Response (200):**
```json
{
  "message": "Device removed successfully"
}
```

**Error Responses:**
- `401`: Invalid or missing device token
- `404`: Device not found
- `500`: Internal server error

### Update Device Metadata

Updates device metadata for better user experience and device management.

**Endpoint:** `PUT /api/licenses/devices/metadata`

**Authentication:** Required (Bearer token)

**Request Body:**
```json
{
  "deviceId": "unique-device-identifier",
  "deviceMetadata": {
    "deviceName": "John's MacBook Pro",
    "deviceType": "MacBook Pro",
    "deviceModel": "14-inch M2 2023",
    "operatingSystem": "macOS 14.1",
    "architecture": "arm64",
    "screenResolution": "3024x1964",
    "totalMemory": "16 GB",
    "userNickname": "Work Laptop",
    "location": "Office",
    "notes": "Primary development machine"
  }
}
```

**Request Validation:**
- `deviceId`: 32-128 character alphanumeric string
- `deviceMetadata`: Object with optional device information fields
  - `deviceName`: User-friendly device name (max 100 chars)
  - `deviceType`: Device type (max 50 chars)
  - `deviceModel`: Model details (max 50 chars)
  - `operatingSystem`: OS version (max 50 chars)
  - `architecture`: CPU architecture (max 20 chars)
  - `screenResolution`: Display resolution (max 20 chars)
  - `totalMemory`: RAM amount (max 20 chars)
  - `userNickname`: Custom device nickname (max 50 chars)
  - `location`: Device location (max 50 chars)
  - `notes`: User notes about device (max 500 chars)

**Success Response (200):**
```json
{
  "message": "Device metadata updated successfully",
  "device": {
    "id": "device-uuid",
    "deviceName": "John's MacBook Pro",
    "deviceType": "MacBook Pro",
    "deviceModel": "14-inch M2 2023",
    "operatingSystem": "macOS 14.1",
    "architecture": "arm64",
    "screenResolution": "3024x1964",
    "totalMemory": "16 GB",
    "userNickname": "Work Laptop",
    "location": "Office",
    "notes": "Primary development machine",
    "lastSeen": "2024-01-01T12:00:00.000Z",
    "appVersion": "1.0.0"
  }
}
```

**Error Responses:**
- `400`: Validation error (invalid input data)
- `401`: Invalid or missing device token
- `404`: Device not found
- `500`: Internal server error

## Payment Processing Endpoints

### 🔒 Webhook-Only License Creation Architecture

**SECURITY NOTICE:** SnapBack uses a webhook-only architecture for paid license creation to prevent payment manipulation attacks. This follows industry best practices used by GitHub, Shopify, and other major platforms.

**Payment Flow:**
1. **Frontend**: User completes payment via Stripe Checkout or Elements
2. **Stripe**: Sends verified webhook event to server
3. **Server**: Creates license automatically via webhook handler (secure)
4. **Frontend**: Polls license status endpoint for completion
5. **User**: Receives license immediately upon payment verification

**Why Webhook-Only?**
- ✅ **Zero client trust**: No client-provided payment data accepted
- ✅ **Impossible to fake payments**: Only verified Stripe events create licenses
- ✅ **Industry standard**: Same approach used by major SaaS platforms
- ✅ **Automatic retry**: Stripe handles webhook delivery reliability

### Get Pricing Information

Retrieves current pricing for all license types.

**Endpoint:** `GET /api/payments/pricing`

**Success Response (200):**
```json
{
  "trial": {
    "price": 0,
    "maxDevices": 1,
    "duration": "14 days"
  },
  "pro": {
    "price": 499,
    "maxDevices": 2,
    "duration": "Lifetime"
  }
}
```

**Note:** SnapBack now uses a single-license-per-purchase model with only pro licenses available.

**Note:** Prices are in cents (USD).

### Create Payment Intent (Embedded Flow)

Creates a Stripe payment intent for embedded payment processing. **License creation happens automatically via webhook after successful payment.**

**Endpoint:** `POST /api/payments/create-payment-intent`

**Rate Limit:** 10 requests per 15 minutes per IP

**Request Body:**
```json
{
  "licenseType": "pro",
  "email": "<EMAIL>",
  "deviceId": "unique-device-identifier"
}
```

**Note:** Only pro licenses are supported.

**Success Response (200):**
```json
{
  "clientSecret": "pi_1234567890_secret_abcdef",
  "amount": 499,
  "licenseType": "pro",
  "paymentIntentId": "pi_1234567890"
}
```

**Important Notes:**
- This endpoint only creates a payment intent, **not a license**
- License creation happens automatically via Stripe webhook after payment
- Use `GET /api/licenses/payment-status/{clientSecret}` to poll for license completion
- Frontend should implement polling mechanism for license delivery

### Create Checkout Session (Redirect Flow)

Creates a Stripe checkout session for redirect-based payment processing. **License creation happens automatically via webhook after successful payment.**

**Endpoint:** `POST /api/payments/create-checkout-session`

**Request Body:**
```json
{
  "licenseType": "pro",
  "email": "<EMAIL>",
  "deviceId": "unique-device-identifier",
  "successUrl": "https://yoursite.com/success",
  "cancelUrl": "https://yoursite.com/cancel"
}
```

**Note:** Only pro licenses are supported.

**Success Response (200):**
```json
{
  "sessionId": "cs_1234567890",
  "url": "https://checkout.stripe.com/pay/cs_1234567890",
  "amount": 499,
  "licenseType": "pro"
}
```

**Important Notes:**
- This endpoint only creates a checkout session, **not a license**
- License creation happens automatically via Stripe webhook after payment
- Success page should poll `GET /api/licenses/payment-status/{clientSecret}` for license
- Client secret can be extracted from the checkout session after payment

### ~~Create Upgrade Payment Intent~~ **[REMOVED]**

**License upgrade functionality has been completely removed.** SnapBack now uses a single-license-per-purchase model where users must buy separate pro licenses for additional devices.

**Migration Path:**
- Users needing more devices should purchase new pro licenses
- Each license operates independently with 2 device slots
- No upgrade or device addition functionality is available

### Stripe Webhook

Handles Stripe webhook events for payment processing.

**Endpoint:** `POST /api/payments/webhook`

**Content-Type:** `application/json` (raw body)

**Headers:**
- `stripe-signature`: Stripe webhook signature for verification

**Supported Events:**
- `payment_intent.succeeded`: Processes successful payments
- `payment_intent.payment_failed`: Handles failed payments
- `checkout.session.completed`: Processes completed checkout sessions

**Response (200):**
```json
{
  "received": true
}
```

### Get Checkout Session Status

Retrieves the status of a checkout session.

**Endpoint:** `GET /api/payments/checkout-session/{sessionId}`

**Success Response (200):**
```json
{
  "sessionId": "cs_1234567890",
  "paymentStatus": "paid",
  "customerEmail": "<EMAIL>",
  "amountTotal": 499,
  "currency": "usd",
  "license": {
    "licenseKey": "ABCD1234EFGH5678IJKL9012",
    "licenseType": "pro",
    "maxDevices": 2,
    "expiresAt": null,
    "email": "<EMAIL>"
  },
  "metadata": {
    "licenseType": "pro",
    "email": "<EMAIL>"
  }
}
```

### Get Payment Intent Status

Retrieves the status of a payment intent.

**Endpoint:** `GET /api/payments/payment-intent/{paymentIntentId}`

**Success Response (200):**
```json
{
  "paymentIntentId": "pi_1234567890",
  "status": "succeeded",
  "amount": 499,
  "currency": "usd",
  "license": {
    "licenseKey": "ABCD1234EFGH5678IJKL9012",
    "licenseType": "pro",
    "maxDevices": 2,
    "expiresAt": null,
    "email": "<EMAIL>"
  },
  "metadata": {
    "licenseType": "pro",
    "email": "<EMAIL>"
  }
}
```

## Refund Management Endpoints

### Request Refund

Submits a refund request for a license.

**Endpoint:** `POST /api/refunds/request`

**Rate Limit:** 3 requests per hour per IP

**Request Body:**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "reason": "Detailed reason for refund request",
  "requestedBy": "<EMAIL>"
}
```

**Request Validation:**
- `licenseKey`: Exactly 24 characters, uppercase letters and numbers only
- `reason`: 10-500 characters describing the refund reason
- `requestedBy`: Valid email address of the person requesting the refund

**Success Response (200):**
```json
{
  "message": "Refund request submitted successfully",
  "refundRequest": {
    "id": "req_1234567890abcdef",
    "status": "PENDING",
    "reason": "Detailed reason for refund request",
    "createdAt": "2024-01-15T10:30:00.000Z"
  }
}
```

**Error Responses:**
- `400`: Validation error or license not eligible for refund
- `404`: License not found
- `409`: Refund request already exists for this license
- `429`: Rate limit exceeded
- `500`: Internal server error

**Business Rules:**
- License must have an associated payment (paid licenses only)
- License must not already be refunded
- Refund must be requested within 30 days of license creation
- Only one refund request allowed per license
- Confirmation email sent to requestor

### Process Refund (Admin)

Processes a pending refund request by approving or rejecting it.

**Endpoint:** `POST /api/refunds/process`

**Access:** Admin only

**Request Body:**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "action": "approve|reject",
  "adminNotes": "Optional admin notes about the decision",
  "amount": 299
}
```

**Request Validation:**
- `licenseKey`: Exactly 24 characters, uppercase letters and numbers only
- `action`: Must be either "approve" or "reject"
- `adminNotes`: Optional, max 1000 characters
- `amount`: Optional positive integer for partial refunds (in cents)

**Success Response (200) - Approval:**
```json
{
  "message": "Refund processed successfully",
  "refund": {
    "id": "re_1234567890abcdef",
    "amount": 499,
    "status": "PROCESSED"
  }
}
```

**Success Response (200) - Rejection:**
```json
{
  "message": "Refund request rejected",
  "status": "REJECTED"
}
```

**Error Responses:**
- `400`: Validation error, refund already processed, or Stripe processing failed
- `404`: License or refund request not found
- `500`: Internal server error

**Business Rules:**
- Only pending refund requests can be processed
- Approved refunds are processed immediately through Stripe
- Full refunds deactivate the license and all associated devices
- Partial refunds maintain license validity
- Confirmation emails sent for both approvals and rejections
- Audit logs created for all refund actions

### Get Refund Status

Retrieves the refund status for a specific license.

**Endpoint:** `GET /api/refunds/status/{licenseKey}`

**Request Parameters:**
- `licenseKey`: License key to check refund status for (URL parameter)

**Success Response (200):**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "refunded": true,
  "refundedAt": "2024-01-16T14:30:00.000Z",
  "refundReason": "Customer requested refund due to compatibility issues",
  "refundAmount": 499,
  "refundRequest": {
    "id": "req_1234567890abcdef",
    "status": "PROCESSED",
    "reason": "Customer requested refund due to compatibility issues",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "processedAt": "2024-01-16T14:30:00.000Z"
  }
}
```

**Response for Non-Refunded License:**
```json
{
  "licenseKey": "ABCD1234EFGH5678IJKL9012",
  "refunded": false,
  "refundedAt": null,
  "refundReason": null,
  "refundAmount": null,
  "refundRequest": null
}
```

**Error Responses:**
- `400`: Invalid license key format
- `404`: License not found
- `500`: Internal server error

### Get Refund History (Admin)

Retrieves paginated refund history for administrative purposes.

**Endpoint:** `GET /api/refunds/history`

**Access:** Admin only

**Query Parameters:**
- `page`: Page number for pagination (default: 1)
- `limit`: Number of results per page (default: 20, max: 100)
- `status`: Filter by refund status (PENDING, APPROVED, REJECTED, PROCESSED, FAILED)

**Success Response (200):**
```json
{
  "refundRequests": [
    {
      "id": "req_1234567890abcdef",
      "status": "PROCESSED",
      "reason": "Customer requested refund due to compatibility issues",
      "amount": null,
      "requestedBy": "<EMAIL>",
      "adminNotes": "Approved - valid compatibility concern",
      "processedBy": null,
      "createdAt": "2024-01-15T10:30:00.000Z",
      "processedAt": "2024-01-16T14:30:00.000Z",
      "license": {
        "licenseKey": "ABCD1234EFGH5678IJKL9012",
        "email": "<EMAIL>",
        "licenseType": "pro",
        "refundedAt": "2024-01-16T14:30:00.000Z",
        "refundAmount": 499
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "totalCount": 45,
    "totalPages": 3,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

**Error Responses:**
- `500`: Internal server error

**Business Rules:**
- Results ordered by creation date (newest first)
- Includes complete license information for context
- Supports filtering by refund request status
- Pagination prevents large data transfers

## Validation Schemas

All API endpoints use Zod validation schemas to ensure data integrity and provide clear error messages. Below are the validation rules and constraints for each endpoint.

### Core Field Validations

#### License Key
- **Format**: Exactly 24 characters
- **Pattern**: Uppercase letters and numbers only (`/^[A-Z0-9]+$/`)
- **Example**: `ABCD1234EFGH5678IJKL9012`

#### Email Address
- **Format**: Valid email format
- **Length**: 1-254 characters (RFC 5321 limit)
- **Transformation**: Converted to lowercase and trimmed
- **Example**: `<EMAIL>`

#### Device ID
- **Length**: 32-128 characters
- **Formats Accepted**:
  - Hexadecimal strings (SHA256 format: 64 chars)
  - UUID format with hyphens (36 chars)
- **Pattern**: `/^[a-fA-F0-9]+$/` or UUID format
- **Transformation**: UUIDs normalized by removing hyphens and converting to lowercase
- **Example**: `a1b2c3d4e5f6789012345678901234567890abcdef123456789012345678901234`

#### App Version
- **Format**: Semantic versioning
- **Pattern**: `/^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+)?$/`
- **Length**: Maximum 50 characters
- **Examples**: `1.0.0`, `2.1.3-beta`

#### License Type
- **Allowed Values**: `"pro"`
- **Case Sensitive**: Yes

#### Stripe Payment Intent ID
- **Format**: Stripe payment intent format
- **Pattern**: `/^pi_[a-zA-Z0-9_]+$/`
- **Example**: `pi_1234567890abcdef`

### License Management Schemas

#### Create License Schema
```typescript
{
  email: string;              // Valid email, 1-254 chars, normalized
  licenseType: "pro";
  stripePaymentIntentId?: string; // Optional, Stripe PI format
}
```

#### Validate License Schema
```typescript
{
  licenseKey: string;         // 24 chars, A-Z0-9 only
  deviceId: string;           // 32-128 chars, hex or UUID
  appVersion?: string;        // Optional, semver format
  deviceMetadata?: {          // Optional device information
    deviceName?: string;      // Max 100 chars
    deviceType?: string;      // Max 50 chars
    deviceModel?: string;     // Max 50 chars
    operatingSystem?: string; // Max 50 chars
    architecture?: string;    // Max 25 chars
    screenResolution?: string; // Max 20 chars
    totalMemory?: string;     // Max 20 chars
    userNickname?: string;    // Max 50 chars
    location?: string;        // Max 50 chars
    notes?: string;           // Max 500 chars
  };
}
```

#### Resend License Schema
```typescript
{
  email: string;              // Valid email, 1-254 chars, normalized
}
```

#### Upgrade License Schema
```typescript
{
  licenseKey: string;         // 24 chars, A-Z0-9 only
  additionalDevices: number;  // Integer, 1-50, whole number
  stripePaymentIntentId?: string; // Optional, Stripe PI format
}
```

### Payment Processing Schemas

#### Create Payment Intent Schema
```typescript
{
  licenseType: "pro";
  additionalDevices?: number; // Integer, 0-50, defaults to 0
  email: string;              // Valid email, 1-254 chars, normalized
}
```

#### Create Checkout Session Schema
```typescript
{
  licenseType: "pro";
  additionalDevices?: number; // Integer, 0-50, defaults to 0
  email: string;              // Valid email, 1-254 chars, normalized
  deviceId?: string;          // Optional, 32-128 chars, hex or UUID
  successUrl: string;         // Required, valid URL format
  cancelUrl: string;          // Required, valid URL format
}
```

**URL Validation Rules:**
- **successUrl**: Must be a valid URL format (http:// or https://)
- **cancelUrl**: Must be a valid URL format (http:// or https://)
- Both URLs are required fields and cannot be empty
- URLs should be accessible endpoints that can handle the redirect flow

#### Create Upgrade Payment Intent Schema
```typescript
{
  licenseKey: string;         // 24 chars, A-Z0-9 only
  additionalDevices: number;  // Integer, 1-50, whole number
}
```

### Webhook Validation

#### Stripe Webhook Requirements
```typescript
// Headers
{
  "stripe-signature": string;  // Required Stripe webhook signature
  "content-type": "application/json";
}

// Environment Variables
{
  STRIPE_WEBHOOK_SECRET: string; // Format: /^whsec_[a-zA-Z0-9]{32,}$/
}
```

**Supported Webhook Events:**
- **payment_intent.succeeded**: Processes successful payment intents
- **payment_intent.payment_failed**: Handles failed payment attempts
- **checkout.session.completed**: Processes completed checkout sessions

**Webhook Payload Validation:**
- Raw body must be preserved for signature verification
- Signature verification using Stripe's `constructEvent()` method
- Event type validation against supported events list
- Metadata validation for required fields (licenseType, email, etc.)

**Required Metadata Fields:**
```typescript
{
  licenseType: "pro";
  additionalDevices: string;    // Number as string (e.g., "0", "2")
  email: string;                // Valid email address
  deviceId?: string;            // Optional device identifier
  flow_type: "embedded" | "checkout"; // Payment flow type
}
```

### Refund Management Schemas

#### Request Refund Schema
```typescript
{
  licenseKey: string;         // 24 chars, A-Z0-9 only (uses licenseKeySchema)
  reason: string;             // 10-500 chars, detailed refund reason
  requestedBy: string;        // Valid email address (uses emailSchema)
}
```

**Validation Rules:**
- **licenseKey**: Uses the standard `licenseKeySchema` (24 characters, A-Z0-9 only)
- **reason**: String between 10-500 characters describing the refund reason
- **requestedBy**: Valid email address using the standard `emailSchema` with normalization

#### Process Refund Schema (Admin)
```typescript
{
  licenseKey: string;         // 24 chars, A-Z0-9 only (uses licenseKeySchema)
  action: "approve" | "reject"; // Required action to take
  adminNotes?: string;        // Optional, max 1000 chars
  amount?: number;            // Optional, positive integer for partial refunds (cents)
}
```

**Validation Rules:**
- **licenseKey**: Uses the standard `licenseKeySchema` (24 characters, A-Z0-9 only)
- **action**: Enum with exactly two values: "approve" or "reject"
- **adminNotes**: Optional string field, maximum 1000 characters
- **amount**: Optional positive integer for partial refund amounts (in cents)

### Device Management Schemas

#### Update Device Metadata Schema
```typescript
{
  deviceId: string;           // 32-128 chars, hex or UUID
  deviceMetadata: {           // Device information object
    deviceName?: string;      // Max 100 chars
    deviceType?: string;      // Max 50 chars
    deviceModel?: string;     // Max 50 chars
    operatingSystem?: string; // Max 50 chars
    architecture?: string;    // Max 25 chars
    screenResolution?: string; // Max 20 chars
    totalMemory?: string;     // Max 20 chars
    userNickname?: string;    // Max 50 chars
    location?: string;        // Max 50 chars
    notes?: string;           // Max 500 chars
  };
}
```

### Validation Constants

- **MAX_ADDITIONAL_DEVICES**: 50 (maximum devices that can be added at once)
- **LICENSE_KEY_LENGTH**: 24 (exact length for license keys)
- **DEVICE_ID_MIN_LENGTH**: 32 (minimum device ID length)
- **DEVICE_ID_MAX_LENGTH**: 128 (maximum device ID length)
- **MAX_EMAIL_LENGTH**: 254 (RFC 5321 email limit)
- **MAX_APP_VERSION_LENGTH**: 50 (maximum app version string length)

### Error Handling

All validation errors return a `400 Bad Request` status with detailed error messages:

```json
{
  "error": "Validation failed",
  "details": [
    {
      "field": "licenseKey",
      "message": "License key must be exactly 24 characters long"
    },
    {
      "field": "email",
      "message": "Please provide a valid email address"
    }
  ]
}
```

## Data Models

### License Model

```typescript
interface License {
  id: string;
  licenseKey: string;          // 24-character alphanumeric key
  email: string;               // Normalized to lowercase
  licenseType: "trial" | "pro";
  maxDevices: number;          // 2 for pro
  expiresAt: Date | null;      // null for lifetime licenses (always null for paid licenses)
  createdAt: Date;
  stripePaymentIntentId: string; // Required for all paid licenses
  upgradePaymentIntentId?: string[];

  // Refund information
  refundedAt?: Date | null;     // Date when license was refunded
  refundReason?: string | null; // Reason for refund
  refundAmount?: number | null; // Amount refunded in cents
  stripeRefundId?: string | null; // Stripe refund ID

  devices: Device[];
  refundRequest?: RefundRequest; // Associated refund request
}
```

### Device Model

```typescript
interface Device {
  id: string;
  licenseId: string;
  deviceHash: string;          // Hashed device identifier
  salt: string;                // Salt used for hashing
  firstSeen: Date;
  lastSeen: Date;
  appVersion?: string;
  isActive: boolean;

  // Enhanced device metadata for better user experience
  deviceName?: string;         // User-friendly device name (e.g., "John's MacBook Pro")
  deviceType?: string;         // Device type (e.g., "MacBook Pro", "iMac", "Mac Studio")
  deviceModel?: string;        // Model details (e.g., "14-inch", "M2", "2023")
  operatingSystem?: string;    // OS version (e.g., "macOS 14.1", "macOS Sonoma")
  architecture?: string;       // CPU architecture (e.g., "arm64", "x86_64")
  screenResolution?: string;   // Display resolution (e.g., "3024x1964", "5120x2880")
  totalMemory?: string;        // RAM amount (e.g., "16 GB", "32 GB")
  userNickname?: string;       // Custom name set by user (e.g., "Work Laptop", "Home Desktop")
  location?: string;           // Optional location (e.g., "Office", "Home")
  notes?: string;              // User notes about the device
}
```

### RefundRequest Model

```typescript
interface RefundRequest {
  id: string;
  licenseId: string;           // Associated license ID
  status: "PENDING" | "APPROVED" | "REJECTED" | "PROCESSED" | "FAILED";
  reason: string;              // Customer's reason for refund request
  amount?: number | null;      // Optional amount for partial refunds (in cents)
  requestedBy: string;         // Email of person requesting refund
  adminNotes?: string | null;  // Admin notes about the decision
  processedBy?: string | null; // Admin who processed the request
  createdAt: Date;             // When request was submitted
  processedAt?: Date | null;   // When request was processed
  license: License;            // Associated license information
}
```

### Error Response Model

```typescript
interface ErrorResponse {
  error: string;
  code?: string;
  message?: string;
  timestamp: string;
  path?: string;
  details?: Record<string, unknown>;
}
```

## Business Rules

### License Types and Restrictions

1. **Trial Licenses:**
   - Duration: Lifetime (no expiration)
   - Device limit: 1 device
   - Restrictions: One per email address, one per device
   - Cost: Free

2. **Pro Licenses:**
   - Duration: Lifetime (no expiration)
   - Device limit: 2 devices
   - Cost: $4.99 USD
   - Duration: Lifetime (no expiration)
   - Device limit: 5 devices
   - Cost: $9.99 USD

### Device Management

- Devices are identified by a unique device identifier provided by the client
- Device identifiers are hashed with a random salt for security
- Each device registration updates the last seen timestamp
- Inactive devices can be removed to free up slots

### Payment Processing

- All payments processed through Stripe
- Two payment flows supported:
  - **Embedded Flow:** Payment intent → License creation via API
  - **Redirect Flow:** Checkout session → Automatic license creation
- Payment verification required for all paid licenses
- Upgrade payments add additional device slots

### Refund Processing

- **Refund Eligibility:**
  - Only paid licenses with associated Stripe payments are eligible
  - Refunds must be requested within 30 days of license creation
  - Already refunded licenses cannot be refunded again
  - One refund request per license maximum

- **Refund Types:**
  - **Full Refunds:** Complete refund of original payment amount
  - **Partial Refunds:** Admin-specified amount (useful for pro-rated refunds)

- **Refund Process:**
  1. Customer submits refund request with reason
  2. System validates eligibility and creates pending request
  3. Admin reviews and approves/rejects request
  4. Approved refunds processed immediately through Stripe
  5. License and devices updated based on refund type

- **Post-Refund Actions:**
  - **Full Refunds:** License marked as refunded, all devices deactivated
  - **Partial Refunds:** License remains active, devices unchanged
  - Confirmation emails sent to customers
  - Audit logs created for compliance

### Security Features

#### Webhook-Only Architecture
- **Payment Security:** Paid licenses created exclusively via verified Stripe webhooks
- **Zero Client Trust:** No client-provided payment data accepted
- **Webhook Verification:** Stripe signature validation for all webhook events
- **Idempotency Protection:** Webhook event ID tracking prevents duplicate processing
- **Atomic Operations:** License creation and device registration in single transaction

#### Advanced Rate Limiting
- **Multi-Layer Protection:** IP, email, device, and hardware-based rate limiting
- **Device Fingerprinting:** Comprehensive device identification for abuse prevention
- **Hardware Signatures:** CPU, memory, screen resolution-based limiting
- **Composite Keys:** SHA-256 hashed device identifier combinations
- **Adaptive Thresholds:** Different limits for web vs in-app requests

#### In-App Trial Security
- **Virtual Machine Detection:** Identifies VM environments used for trial farming
- **Hardware Spoofing Prevention:** Multi-factor device authentication
- **Disposable Email Blocking:** Automatic detection of temporary email services
- **Abuse Pattern Recognition:** Machine learning-based suspicious activity detection
- **Known Device Tracking:** Maintains blacklist of previously flagged devices

#### Device Authentication
- **JWT Device Tokens:** Secure device authentication for API access
- **Hardware-Based Binding:** Device tokens tied to specific hardware signatures
- **Token Rotation:** Automatic token refresh for enhanced security
- **Device Metadata Validation:** Rich device information verification

#### Audit & Monitoring
- **Comprehensive Logging:** All requests logged with structured data
- **Security Event Tracking:** Detailed logs for abuse attempts and violations
- **Audit Trails:** Complete history of license and device operations
- **Real-time Monitoring:** Immediate alerts for suspicious patterns
- **Compliance Ready:** Full audit logs for security compliance

#### Additional Protections
- **CORS Protection:** Configurable cross-origin resource sharing
- **Helmet Security Headers:** Comprehensive HTTP security headers
- **Request Validation:** Zod schema validation for all inputs
- **Error Handling:** Secure error responses without information leakage
- **Anti-Enumeration:** Consistent responses to prevent data mining

### Service Layer Architecture

#### Core Service Functions

**`createTrialLicenseWithDevice(options)`**
- Creates trial license with immediate device registration
- Combines license creation and device registration in single transaction
- Returns both license and device token for instant activation
- Used exclusively by in-app trial endpoint

**`createLicenseFromWebhook(options)`**
- Creates paid licenses from verified Stripe webhook events
- Includes idempotency protection via webhook event ID
- Handles device registration if deviceId provided
- Used exclusively by webhook handlers

**`evaluateInAppTrialRequest(request)`**
- Comprehensive security evaluation for in-app trial requests
- Returns risk score and decision (immediate/review/blocked)
- Detects VMs, hardware spoofing, disposable emails, abuse patterns
- Enables intelligent trial distribution

#### Security Evaluation Functions

**Virtual Machine Detection:**
- Analyzes device model, OS, memory, screen resolution patterns
- Identifies VMware, VirtualBox, QEMU, Parallels, Hyper-V environments
- Uses multiple indicators for high-confidence detection

**Hardware Spoofing Detection:**
- Validates hardware UUID authenticity
- Detects generic or placeholder device models
- Identifies suspicious architecture/memory combinations

**Abuse Pattern Recognition:**
- Tracks known malicious devices across time
- Identifies rapid installation patterns
- Monitors email-to-device ratios for suspicious activity

**Disposable Email Detection:**
- Maintains blocklist of temporary email providers
- Real-time domain reputation checking
- Prevents trial farming via throwaway emails

#### Service Layer Benefits
- **Clean Architecture:** Routes handle HTTP, services handle business logic
- **Testability:** Service functions easily unit tested
- **Reusability:** Services used across multiple endpoints
- **Security:** Centralized security logic in service layer
- **Maintainability:** Clear separation of concerns

### Middleware Stack

1. **Helmet:** Security headers
2. **CORS:** Cross-origin resource sharing
3. **Rate Limiting:** Request throttling
4. **Body Parser:** JSON parsing (10MB limit)
5. **Request Logging:** Comprehensive logging
6. **Error Handling:** Standardized error responses

## Environment Configuration

The API requires the following environment variables:

- `PORT`: Server port (default: 3000)
- `CORS_ORIGIN`: Allowed CORS origins
- `STRIPE_SECRET_KEY`: Stripe secret key
- `STRIPE_WEBHOOK_SECRET`: Stripe webhook endpoint secret
- `DATABASE_URL`: PostgreSQL database connection string
- `JWT_SECRET`: Secret for device token signing
- `REQUEST_SIGNING_SECRET`: Secret for request signature verification

## HTTP Status Codes

- `200`: Success
- `201`: Created (license creation)
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid/missing authentication)
- `403`: Forbidden (license expired, device limit reached)
- `404`: Not Found (license/resource not found, refund request not found)
- `409`: Conflict (license already exists, trial restrictions, refund request already exists)
- `429`: Too Many Requests (rate limit exceeded)
- `500`: Internal Server Error
