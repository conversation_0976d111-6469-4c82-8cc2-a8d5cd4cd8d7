# API Endpoint Mapping: Current Client vs New Server

## Overview
This document maps the current web app client API calls to the new server API structure to guide the client-side refactoring.

## Authentication
- **Current**: Better Auth client works with `/api/auth/*` endpoints ✅
- **New**: Same structure, no changes needed
- **Status**: Compatible

## License Management

### Current Client → New Server
| Current Client Call | New Server Endpoint | Status | Notes |
|---------------------|-------------------|---------|-------|
| `GET /licenses` | `GET /api/admin/licenses` | ❌ Mismatch | Admin endpoint, requires auth |
| `POST /licenses/create` | `POST /api/admin/licenses` | ❌ Mismatch | Admin endpoint |
| `POST /licenses/validate` | `POST /api/licenses/validate` | ✅ Match | Public endpoint |
| `GET /licenses/status/:key` | `GET /api/licenses/:licenseKey/status` | ✅ Match | Public endpoint |
| `POST /licenses/resend` | `POST /api/admin/licenses/:email/resend-email` | ❌ Mismatch | Admin endpoint |
| `DELETE /licenses/devices/:id` | `DELETE /api/admin/devices/:id` | ❌ Mismatch | Admin endpoint |
| `PUT /licenses/devices/metadata` | `PATCH /api/devices/:deviceHash/update` | ❌ Mismatch | Different structure |

## Payment Processing

### Current Client → New Server
| Current Client Call | New Server Endpoint | Status | Notes |
|---------------------|-------------------|---------|-------|
| `GET /payments/pricing` | `GET /api/public/pricing` | ❌ Mismatch | Moved to public |
| `POST /payments/create-payment-intent` | ❌ Not implemented | ❌ Missing | Removed in favor of checkout |
| `POST /payments/create-checkout-session` | `POST /api/payments/create-checkout` | ❌ Mismatch | Different endpoint name |
| `GET /payments/checkout-session/:id` | `GET /api/payments/checkout-session/:id` | ❓ Unknown | Need to verify |
| `GET /payments/payment-intent/:id` | `GET /api/payments/:paymentIntentId` | ✅ Match | Requires auth |

## Device Management

### Current Client → New Server
| Current Client Call | New Server Endpoint | Status | Notes |
|---------------------|-------------------|---------|-------|
| ❌ Not in client | `POST /api/devices/register` | ➕ New | Public endpoint |
| ❌ Not in client | `PATCH /api/devices/:deviceHash/update` | ➕ New | Public endpoint |
| ❌ Not in client | `DELETE /api/devices/:deviceHash/remove` | ❓ Unknown | Need to verify |
| ❌ Not in client | `POST /api/devices/heartbeat` | ➕ New | Public endpoint |
| ❌ Not in client | `POST /api/devices/expand` | ❓ Unknown | Need to verify |

## Refund Management

### Current Client → New Server
| Current Client Call | New Server Endpoint | Status | Notes |
|---------------------|-------------------|---------|-------|
| `POST /refunds/request` | `POST /api/refunds/request` | ✅ Match | Public endpoint |
| `POST /refunds/process` | `POST /api/admin/refunds/:id/process` | ❌ Mismatch | Admin endpoint |
| `GET /refunds/status/:key` | `GET /api/refunds/status/:requestId` | ❌ Mismatch | Different parameter |
| `GET /refunds/history` | `GET /api/admin/refunds` | ❌ Mismatch | Admin endpoint |

## User Management

### Current Client → New Server
| Current Client Call | New Server Endpoint | Status | Notes |
|---------------------|-------------------|---------|-------|
| `GET /users/me` | ❌ Not implemented | ❌ Missing | Need to add |
| `GET /users` | `GET /api/admin/users` | ❌ Mismatch | Admin endpoint |
| `POST /users` | `POST /api/admin/users` | ❌ Mismatch | Admin endpoint |
| `PATCH /users/:id` | `PATCH /api/admin/users/:id` | ❌ Mismatch | Admin endpoint |
| `PATCH /users/:id/status` | `POST /api/admin/users/:id/reactivate` | ❌ Mismatch | Different structure |
| `POST /users/invite` | `POST /api/admin/invitations` | ❌ Mismatch | Admin endpoint |
| `POST /users/invitations/:id/resend` | `POST /api/admin/invitations/:id/resend` | ❌ Mismatch | Admin endpoint |
| `DELETE /users/invitations/:id` | `DELETE /api/admin/invitations/:id` | ❌ Mismatch | Admin endpoint |
| `POST /users/invitations/:token/accept` | `POST /api/invitations/:token/accept` | ❌ Mismatch | Public endpoint |
| `GET /users/invitations` | `GET /api/admin/invitations` | ❌ Mismatch | Admin endpoint |

## Analytics & Dashboard

### Current Client → New Server
| Current Client Call | New Server Endpoint | Status | Notes |
|---------------------|-------------------|---------|-------|
| `GET /analytics/dashboard-stats` | `GET /api/admin/dashboard/stats` | ❌ Mismatch | Admin endpoint |
| `GET /analytics/data` | `GET /api/admin/dashboard/revenue` | ❌ Mismatch | Admin endpoint |
| `GET /analytics/recent-activity` | ❌ Not implemented | ❌ Missing | Need to add |
| `GET /analytics/licenses` | `GET /api/admin/dashboard/licenses` | ❌ Mismatch | Admin endpoint |
| `GET /analytics/customers` | ❌ Not implemented | ❌ Missing | Need to add |

## Public Endpoints (New)

### Available Public Endpoints
| Endpoint | Purpose | Status |
|----------|---------|---------|
| `GET /api/public/pricing` | Get pricing info | ➕ New |
| `POST /api/public/purchase/validate` | Validate purchase | ➕ New |
| `GET /api/public/purchase/success` | Purchase success | ➕ New |
| `GET /api/public/purchase/cancel` | Purchase cancel | ➕ New |
| `POST /api/public/support/license-lookup` | License lookup | ➕ New |
| `POST /api/public/support/contact` | Contact support | ➕ New |
| `GET /api/public/support/faq` | Get FAQ | ➕ New |
| `GET /api/invitations/:token/validate` | Validate invitation | ➕ New |

## System Endpoints

### Available System Endpoints
| Endpoint | Purpose | Auth Required |
|----------|---------|---------------|
| `GET /api/health` | Health check | No |
| `GET /api/admin/system/status` | System status | Yes |
| `GET /api/admin/system/metrics` | System metrics | Yes |

## Key Changes Required

1. **Admin Endpoints**: Most management operations moved to `/api/admin/*` and require authentication
2. **Public Endpoints**: New `/api/public/*` structure for public operations
3. **Response Format**: All endpoints use standardized `ApiResponse<T>` format
4. **Authentication**: Better Auth integration remains the same
5. **Device Management**: New device-centric endpoints for app integration
6. **Pagination**: Consistent pagination across all list endpoints

## Next Steps

1. Update shared types to match new server responses
2. Create new API client with correct endpoint mappings
3. Update TanStack Query hooks to use new endpoints
4. Handle authentication requirements for admin endpoints
5. Test all functionality with new client implementation
