{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "db:push": {"cache": false, "persistent": true}, "db:studio": {"cache": false, "persistent": true}, "db:migrate": {"cache": false, "persistent": true}, "db:generate": {"cache": false, "persistent": true}, "db:start": {"cache": false, "persistent": true}, "db:stop": {"cache": false, "persistent": true}, "db:watch": {"cache": false, "persistent": true}, "db:down": {"cache": false, "persistent": true}, "test": {"cache": false, "persistent": true}, "test:run": {"cache": false, "persistent": true}, "test:ui": {"cache": false, "persistent": true}, "test:coverage": {"cache": false, "persistent": true}, "stripe:listen": {"cache": false, "persistent": true}}}